# All Aware Storefront

All Aware Storefront is the next generation of the All Aware e-commerce selling platform. It uses Shopify Hydrogen, Shopify’s stack for headless commerce. Hydrogen is designed to dovetail with [Remix](https://remix.run/), Shopify’s full stack web framework.

[Check out Hydrogen docs](https://shopify.dev/custom-storefronts/hydrogen)
[Get familiar with Remix](https://remix.run/docs/en/v1)

## Current Stack

- Remix
- Hydrogen
- Oxygen
- Shopify CLI
- ESLint
- Prettier
- GraphQL generator
- TypeScript and JavaScript flavors
- Builder.io

## Getting started

- [Environment Setup](docs/env-setup.md)
- [Running and Debugging](docs/running-and-debugging.md)
- [Atomic Design](docs/atomic-design.md)
- [Route Structure](docs/route-structure.md)

- services/ - business logic
- lib/ -shopify stuff
- lib/utils - our utils
- components/ui - shadcn building blocks (with our modifications)
- components/ - everything else
- builder
