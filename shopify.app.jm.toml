# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "1458d53096079f7b8d67d7d30fccda99"
name = "storefront-dev-jm"
handle = "storefront-dev-jm"
application_url = "https://allawaredev-jm.ngrok.io"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "all-aware-sandbox-dev-jm.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_discounts,write_customers,write_companies,write_products,write_orders"

[auth]
redirect_urls = [
  "https://allawaredev-jm.ngrok.io/auth/callback",
  "https://allawaredev-jm.ngrok.io/auth/shopify/callback",
  "https://allawaredev-jm.ngrok.io/api/auth/callback"
]

[webhooks]
api_version = "2024-10"

[pos]
embedded = false
