enum DeviceState {
  inactive
  active
  activating
  deactivating
}

"A device form the device log"
type Device {
  "The state of the device"
  state: DeviceState
  "The handle of the device"
  handle: String
  "The owner id of the device"
  ownerId: String
  "The unique key of the device"
  key: String
  "The system key of the device"
  systemKey: String
  "The nickname of the device (if available)"
  nickname: String
  "The adc customer id of the device (if available)"
  adcCustomerId: Int
  "The device id of the device (if available)"
  deviceId: Int
  "The IMEI of the device (if available)"
  IMEI: String
  "The VIN of the device (if available)"
  VIN: String
  "The MAC of the device (if available)"
  MAC: String
  "The serial number of the device (if available)"
  serialNumber: String
  "The order id of the device"
  orderId: ID
  "The shopify product handle that was used to purchase this device"
  productHandle: String
}
