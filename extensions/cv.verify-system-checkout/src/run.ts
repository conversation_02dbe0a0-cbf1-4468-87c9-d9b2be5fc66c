import { RunInput, FunctionRunResult, FunctionError, BuyerJourneyStep } from '../generated/api';

export function run(input: RunInput): FunctionRunResult {
  const errors: FunctionError[] = [];

  if (input.buyerJourney?.step == BuyerJourneyStep.CartInteraction) {
    return {
      errors: [],
    };
  }

  const loggedInCustomerId = input.cart.buyerIdentity?.customer?.id || undefined;
  const loggedInCompanyLocationId = input.cart.buyerIdentity?.purchasingCompany?.location?.id || undefined;

  const systemType = input.cart.systemType?.value || undefined;
  const orderingCustomerId = input.cart.orderingCustomerId?.value || undefined;
  const companyLocationId = input.cart.companyLocationId?.value || undefined;

  if (systemType == 'business') {
    if (
      !orderingCustomerId ||
      !companyLocationId ||
      loggedInCustomerId != orderingCustomerId ||
      loggedInCompanyLocationId != companyLocationId
    ) {
      errors.push({
        localizedMessage: `You must be logged in to place a business order. Please login and try again or contact support.`,
        target: '$.cart.buyerIdentity.email',
      });
    }
  } else if (systemType == 'personal') {
    if (loggedInCustomerId != orderingCustomerId) {
      errors.push({
        localizedMessage:
          'Looks like you are logged in as the wrong customer. Please go back to cart and try again or contact support.',
        target: '$.cart.buyerIdentity.email',
      });
    }
    if (loggedInCompanyLocationId) {
      errors.push({
        localizedMessage:
          'Looks like you are logged in as a business customer. Please go back to cart and try again or contact support.',
        target: '$.cart.buyerIdentity.email',
      });
    }
  } else {
    // TODO: Uncomment this once we are no longer using the old cart flow
    // errors.push({
    //   localizedMessage:
    //     'Cart does not seem to be setup correctly... Please go back to cart and try again or contact support.',
    //   target: '$.cart',
    // });
  }

  return {
    errors,
  };
}
