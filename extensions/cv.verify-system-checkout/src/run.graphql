query RunInput {
  buyerJourney {
    step
  }
  cart {
    buyerIdentity {
      customer {
        id
      }
      purchasingCompany {
        location {
          id
        }
      }
    }
    systemType: attribute(key: "system_type") {
      value
    }
    orderingCustomerId: attribute(key: "ordering_customer_id") {
      value
    }
    companyLocationId: attribute(key: "company_location_id") {
      value
    }
  }
}
