[[extensions]]
name = "Decode vehicle VIN"
handle = "decode-vehicle-vin"
type = "flow_action"
schema = "./schema.graphql"
return_type_ref = "VINMakeModelYear"

description = "Decodes a VIN using the NHTSA dataset."
runtime_url = "https://{url}/events/flow/decode-vehicle-vin"

[settings]

  [[settings.fields]]
  type = "single_line_text_field"
  key = "VIN"
  name = "Vehicle VIN"
  description = "The vehicle VIN you would like to decode"
  required = true
