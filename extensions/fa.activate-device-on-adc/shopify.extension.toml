[[extensions]]
name = "Activate device on ADC"
handle = "activate-device-on-adc"
type = "flow_action"
schema = "./schema.graphql"
return_type_ref = "DeviceActivationResult"

description = "Call to activate a device on ADC"
runtime_url = "https://{url}/events/flow/activate-device-on-adc"

[settings]

  [[settings.fields]]
  type = "single_line_text_field"
  key = "owner_id"
  name = "Owner id"
  description = "Owner of the device (company or customer id)"
  required = true

  [[settings.fields]]
  type = "single_line_text_field"
  key = "device_key"
  name = "Device key"
  description = "The key of the device in the devices log"
  required = true
