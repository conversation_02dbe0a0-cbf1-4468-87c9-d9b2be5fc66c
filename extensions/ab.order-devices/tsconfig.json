{
  // This tsconfig.json file is only needed to inform the IDE
  // About the `react-jsx` tsconfig option, so IDE doesn't complain about missing react import
  // Changing options here won't affect the build of your extension
  "compilerOptions": {
    "jsx": "react-jsx",
    "target": "ES2020",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "paths": {
      "@Res/*": ["../admin-resources/*"],
      "@App/*": ["../../app/*"]
    }
  },
  "include": ["./src", "../admin-resources"]
}
