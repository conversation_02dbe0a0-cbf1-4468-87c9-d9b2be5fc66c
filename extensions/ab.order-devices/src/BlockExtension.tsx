import {
  Badge,
  BlockStack,
  Button,
  InlineStack,
  Paragraph,
  ProgressIndicator,
  reactExtension,
  Section,
  Text,
  useApi,
} from '@shopify/ui-extensions-react/admin';
import { useEffect, useState } from 'react';
import type { OrderSetupState } from '../../../app/business/core/types/order';
import type { System } from '../../../app/business/core/types/system';

// The target used here must match the target used in the extension's toml file (./shopify.extension.toml)
const TARGET = 'admin.order-details.block.render';

export default reactExtension(TARGET, () => <App />);

function App() {
  // The useApi hook provides access to several useful APIs like i18n and data.
  const { data } = useApi(TARGET);
  const orderId = data?.selected?.[0]?.id;
  const orderNumber = orderId.split('/').at(-1);

  const [state, setState] = useState<OrderSetupState & System>(undefined);
  useEffect(() => {
    fetch(`/admin/api/orders/${orderNumber}/setup-state`)
      .then(res => res.json())
      .then(body => {
        console.log('setup-state', body);
        setState(body);
      })
      .catch(error => {
        console.error(error);
        setState({ error: error.message } as any);
      });
  }, [orderId]);

  if (!state) {
    return (
      <InlineStack inlineAlignment="center">
        <ProgressIndicator size="large-300" />
      </InlineStack>
    );
  }

  if ((state as any).flowNotRun) {
    return <Text fontWeight="bold">Flow not yet run!</Text>;
  }

  const error: string | undefined = (state as any).error;
  if (error) {
    return (
      <Text fontWeight="bold" fontStyle="italic">
        {error}
      </Text>
    );
  }

  return (
    // The AdminBlock component provides an API for setting the title of the Block extension wrapper.
    <BlockStack blockGap="base">
      <InlineStack inlineAlignment="space-between">
        <InlineStack inlineSize="100%" inlineGap="small" inlineAlignment="start">
          <Badge tone={'info'}>{state.newSystem ? 'New system' : 'Existing system'}</Badge>
          <Badge tone={state.servicePaid ? 'success' : 'warning'}>
            {state.servicePaid
              ? `Service paid (consented ${new Date(state.consentTimestamp).toLocaleString()})`
              : 'Service unpaid'}
          </Badge>
          <Badge tone={state.fulfilled ? 'success' : 'warning'}>{state.fulfilled ? 'Fulfilled' : 'Unfulfilled'}</Badge>
        </InlineStack>
        <InlineStack inlineSize="100%" inlineGap="small" inlineAlignment="end">
          <Button
            to={`https://alarmadmin.alarm.com/Support/CustomerInfo.aspx?customer_id=${state.adcCustomerId}`}
            target="_blank"
            disabled={!state.adcCustomerId}
          >
            ADC
          </Button>
          <Button
            disabled={!state.subscriptionId}
            to={`https://dashboard.stripe.com/subscriptions/${state.subscriptionId}`}
          >
            Stripe
          </Button>
        </InlineStack>
      </InlineStack>
      <Section heading="Equipment" padding="base">
        <Paragraph>
          {Object.entries<number>(state.featureQuantities).map(([handle, quantity]) => (
            <>
              <Badge tone="info" key={handle}>
                {quantity} {handle}
              </Badge>{' '}
            </>
          ))}
        </Paragraph>
      </Section>
      <InlineStack inlineAlignment="end">
        <Button variant="primary" tone="default" to={`app:/admin/orders/${orderNumber}/fulfill`}>
          {state.fulfilled ? 'Edit' : 'Fulfill'}
        </Button>
      </InlineStack>
    </BlockStack>
  );
}
