api_version = "2024-10"

[[extensions]]
# Change the merchant-facing name of the extension in locales/en.default.json
name = "t:name"
handle = "order-devices"
type = "ui_extension"


# Only 1 target can be specified for each Admin block extension
[[extensions.targeting]]
module = "./src/BlockExtension.tsx"
# The target used here must match the target used in the module file (./src/BlockExtension.tsx)
target = "admin.order-details.block.render"


# Valid extension targets:

# Abandoned checkout detail page
# - admin.abandoned-checkout-details.block.render

# Collection detail pages
# - admin.collection-details.block.render

# Catalog detail page
# - admin.catalog-details.block.render

# Company detail page
# - admin.company-details.block.render
# - admin.company-location.block.render

# Customer detail page
# - admin.customer-details.block.render

# Draft order detail pages
# - admin.draft-order-details.block.render

# Gift card detail page
# - admin.gift-card-details.block.render

# Order detail page
# - admin.order-details.block.render

# Product detail pages
# - admin.product-details.block.render

# Product variant detail pages
# - admin.product-variant-details.block.render
