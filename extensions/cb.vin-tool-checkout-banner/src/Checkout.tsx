import {
  Image,
  Link,
  reactExtension,
  Style,
  useApi,
  useAppMetafields,
  useSettings,
  useSubscription,
} from '@shopify/ui-extensions-react/checkout';

export default reactExtension('purchase.thank-you.block.render', () => <Extension />);

function Extension() {
  const metafields = useAppMetafields();
  const { orderConfirmation } = useApi<'purchase.thank-you.block.render'>();
  const { order } = useSubscription(orderConfirmation);
  const settings = useSettings();

  if (
    !metafields?.some(
      ({ target, metafield }) =>
        target.type == 'product' &&
        metafield.namespace == 'device' &&
        metafield.key == 'handle' &&
        metafield.value == 'fleet-tracker',
    )
  ) {
    return null;
  }

  const desktopImage = (settings['desktop_image'] || settings['mobile_image']) as string;
  const mobileImage = (settings['mobile_image'] || settings['desktop_image']) as string;
  const link = (settings['vin_tool_url'] as string).replace('{id}', order.id.split('/')?.at(-1));

  // 3. Render a UI
  return (
    <Link to={link} external>
      <Image
        source={Style.default(mobileImage).when(
          {
            viewportInlineSize: { min: 'extraSmall' },
          },
          desktopImage,
        )}
      />
    </Link>
  );
}
