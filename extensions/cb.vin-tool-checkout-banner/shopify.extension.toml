# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-04"

[[extensions]]
name = "Vin tool checkout banner"
handle = "vin-tool-checkout-banner"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.thank-you.block.render"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
# network_access = true

# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

[[extensions.metafields]]
namespace = "device"
key = "handle"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]

[[extensions.settings.fields]]
key = "vin_tool_url"
type = "single_line_text_field"
name = "VIN Tool URL"
description = "Enter the VIN tool URL with {id} for the order id. e.g. https://allaware.com/vin-tool/{id}"

[[extensions.settings.fields]]
key = "desktop_image"
type = "single_line_text_field"
name = "Desktop image"
description = "Enter a image url for the desktop"

[[extensions.settings.fields]]
key = "mobile_image"
type = "single_line_text_field"
name = "Mobile image"
description = "Enter a image url for the desktop"
