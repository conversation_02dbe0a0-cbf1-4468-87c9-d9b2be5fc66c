const path = require('path');

module.exports = {
  plugins: ['@svgr/plugin-svgo', '@svgr/plugin-jsx'],
  typescript: true,
  prettier: false,
  ref: true,
  indexTemplate(filePaths) {
    const iconRefs = filePaths.map(({ path: filePath }, index) => {
      const basename = path.basename(filePath, path.extname(filePath));
      const typename = basename.replace(/[A-Z]+/g, (match, offset) => (offset > 0 ? '-' : '') + match.toLowerCase());
      return { basename, typename, variable: `Icon${index + 1}` };
    });

    const imports = iconRefs.map(({ basename, variable }) => `import ${variable} from "./${basename}";`);
    const keys = iconRefs.map(({ typename, variable }) => `"${typename}": ${variable},`);

    return `${imports.join('\n')}

export const icons = {
  "none": null,
  ${keys.join('\n  ')}
};`;
  },
  outDir: 'app/components/Icon/icons',
  //   indexTemplate(filePaths) {
  //     const exportEntries = filePaths.map(({ path: filePath }) => {
  //       const basename = path.basename(filePath, path.extname(filePath));
  //       const typename = basename.replace(/[A-Z]+/g, (match, offset) => (offset > 0 ? '-' : '') + match.toLowerCase());
  //       return `"${typename}": lazy(async () => import("./${basename}")),`;
  //     });
  //     return `import { lazy } from "react";

  // export const icons = {
  //   "none": null,
  //   ${exportEntries.join('\n  ')}
  // };`;
  //   },
};
