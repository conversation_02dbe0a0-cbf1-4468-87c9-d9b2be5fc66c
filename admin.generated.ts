/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
import * as AdminTypes from './admin.types';

export type CustomerAddressViewFragment = Pick<AdminTypes.MailingAddress, 'id' | 'address1' | 'address2' | 'firstName' | 'lastName' | 'company' | 'phone' | 'city' | 'provinceCode' | 'province' | 'zip' | 'country'>;

export type CompanyAddressViewFragment = Pick<AdminTypes.CompanyAddress, 'id' | 'address1' | 'address2' | 'companyName' | 'firstName' | 'lastName' | 'phone' | 'city' | 'zoneCode' | 'zip' | 'recipient'>;

export type CompanyLocationViewFragment = (
  Pick<AdminTypes.CompanyLocation, 'id' | 'name' | 'phone'>
  & { billingAddress?: AdminTypes.Maybe<Pick<AdminTypes.CompanyAddress, 'id' | 'address1' | 'address2' | 'companyName' | 'firstName' | 'lastName' | 'phone' | 'city' | 'zoneCode' | 'zip' | 'recipient'>>, shippingAddress?: AdminTypes.Maybe<Pick<AdminTypes.CompanyAddress, 'id' | 'address1' | 'address2' | 'companyName' | 'firstName' | 'lastName' | 'phone' | 'city' | 'zoneCode' | 'zip' | 'recipient'>>, ordersCount?: AdminTypes.Maybe<Pick<AdminTypes.Count, 'count' | 'precision'>> }
);

export type GetSystemOwnerContactInfoQueryVariables = AdminTypes.Exact<{
  ownerId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetSystemOwnerContactInfoQuery = { node?: AdminTypes.Maybe<{ __typename: 'AbandonedCheckout' | 'AbandonedCheckoutLineItem' | 'Abandonment' | 'AddAllProductsOperation' | 'AdditionalFee' | 'App' | 'AppCatalog' | 'AppCredit' | 'AppInstallation' | 'AppPurchaseOneTime' | 'AppRevenueAttributionRecord' | 'AppSubscription' | 'AppUsageRecord' | 'Article' | 'BasicEvent' | 'Blog' | 'BulkOperation' | 'BusinessEntity' | 'CalculatedOrder' | 'CartTransform' } | { __typename: 'CashTrackingAdjustment' | 'CashTrackingSession' | 'CatalogCsvOperation' | 'Channel' | 'ChannelDefinition' | 'ChannelInformation' | 'CheckoutProfile' | 'Collection' | 'Comment' | 'CommentEvent' | 'CompanyAddress' | 'CompanyContact' | 'CompanyContactRole' | 'CompanyContactRoleAssignment' | 'CompanyLocation' | 'CompanyLocationCatalog' | 'CompanyLocationStaffMemberAssignment' | 'CustomerAccountAppExtensionPage' | 'CustomerAccountNativePage' | 'CustomerPaymentMethod' } | { __typename: 'CustomerSegmentMembersQuery' | 'CustomerVisit' | 'DeliveryCarrierService' | 'DeliveryCondition' | 'DeliveryCountry' | 'DeliveryCustomization' | 'DeliveryLocationGroup' | 'DeliveryMethod' | 'DeliveryMethodDefinition' | 'DeliveryParticipant' | 'DeliveryProfile' | 'DeliveryProfileItem' | 'DeliveryPromiseProvider' | 'DeliveryProvince' | 'DeliveryRateDefinition' | 'DeliveryZone' | 'DiscountAutomaticBxgy' | 'DiscountAutomaticNode' | 'DiscountCodeNode' | 'DiscountNode' } | { __typename: 'DiscountRedeemCodeBulkCreation' | 'Domain' | 'DraftOrder' | 'DraftOrderLineItem' | 'DraftOrderTag' | 'Duty' | 'ExchangeLineItem' | 'ExchangeV2' | 'ExternalVideo' | 'Fulfillment' | 'FulfillmentConstraintRule' | 'FulfillmentEvent' | 'FulfillmentHold' | 'FulfillmentLineItem' | 'FulfillmentOrder' | 'FulfillmentOrderDestination' | 'FulfillmentOrderLineItem' | 'FulfillmentOrderMerchantRequest' | 'GenericFile' | 'GiftCard' } | { __typename: 'GiftCardCreditTransaction' | 'GiftCardDebitTransaction' | 'InventoryAdjustmentGroup' | 'InventoryItem' | 'InventoryItemMeasurement' | 'InventoryLevel' | 'InventoryQuantity' | 'LineItem' | 'LineItemGroup' | 'Location' | 'MailingAddress' | 'Market' | 'MarketCatalog' | 'MarketRegionCountry' | 'MarketWebPresence' | 'MarketingActivity' | 'MarketingEvent' | 'MediaImage' | 'Menu' | 'Metafield' } | { __typename: 'MetafieldDefinition' | 'MetafieldStorefrontVisibility' | 'Metaobject' | 'MetaobjectDefinition' | 'Model3d' | 'OnlineStoreTheme' | 'Order' | 'OrderAdjustment' | 'OrderDisputeSummary' | 'OrderTransaction' | 'Page' | 'PaymentCustomization' | 'PaymentMandate' | 'PaymentSchedule' | 'PaymentTerms' | 'PaymentTermsTemplate' | 'PriceList' | 'PriceRule' | 'PriceRuleDiscountCode' | 'PrivateMetafield' } | { __typename: 'Product' | 'ProductBundleOperation' | 'ProductDeleteOperation' | 'ProductDuplicateOperation' | 'ProductFeed' | 'ProductOption' | 'ProductOptionValue' | 'ProductSetOperation' | 'ProductTaxonomyNode' | 'ProductVariant' | 'ProductVariantComponent' | 'Publication' | 'PublicationResourceOperation' | 'QuantityPriceBreak' | 'Refund' | 'RefundShippingLine' | 'Return' | 'ReturnLineItem' | 'ReturnableFulfillment' | 'ReverseDelivery' } | { __typename: 'ReverseDeliveryLineItem' | 'ReverseFulfillmentOrder' | 'ReverseFulfillmentOrderDisposition' | 'ReverseFulfillmentOrderLineItem' | 'SaleAdditionalFee' | 'SavedSearch' | 'ScriptTag' | 'Segment' | 'SellingPlan' | 'SellingPlanGroup' | 'ServerPixel' | 'Shop' | 'ShopAddress' | 'ShopPolicy' | 'ShopifyPaymentsAccount' | 'ShopifyPaymentsBalanceTransaction' | 'ShopifyPaymentsBankAccount' | 'ShopifyPaymentsDispute' | 'ShopifyPaymentsDisputeEvidence' | 'ShopifyPaymentsDisputeFileUpload' } | { __typename: 'ShopifyPaymentsDisputeFulfillment' | 'ShopifyPaymentsPayout' | 'ShopifyPaymentsVerification' | 'StaffMember' | 'StandardMetafieldDefinitionTemplate' | 'StoreCreditAccount' | 'StoreCreditAccountCreditTransaction' | 'StoreCreditAccountDebitRevertTransaction' | 'StoreCreditAccountDebitTransaction' | 'StorefrontAccessToken' | 'SubscriptionBillingAttempt' | 'SubscriptionContract' | 'SubscriptionDraft' | 'TaxonomyAttribute' | 'TaxonomyCategory' | 'TaxonomyChoiceListAttribute' | 'TaxonomyMeasurementAttribute' | 'TaxonomyValue' | 'TenderTransaction' | 'TransactionFee' } | { __typename: 'UnverifiedReturnLineItem' | 'UrlRedirect' | 'UrlRedirectImport' | 'Validation' | 'Video' | 'WebPixel' | 'WebhookSubscription' } | (
    { __typename: 'Company' }
    & Pick<AdminTypes.Company, 'name'>
    & { mainContact?: AdminTypes.Maybe<(
      Pick<AdminTypes.CompanyContact, 'id'>
      & { customer: Pick<AdminTypes.Customer, 'id' | 'email' | 'displayName' | 'phone'> }
    )> }
  ) | (
    { __typename: 'Customer' }
    & Pick<AdminTypes.Customer, 'firstName' | 'lastName' | 'phone' | 'email'>
    & { defaultAddress?: AdminTypes.Maybe<Pick<AdminTypes.MailingAddress, 'id'>>, addresses: Array<Pick<AdminTypes.MailingAddress, 'id' | 'address1' | 'address2' | 'firstName' | 'lastName' | 'company' | 'phone' | 'city' | 'provinceCode' | 'province' | 'zip' | 'country'>> }
  )> };

export type GetPrimaryContactCustomerIdQueryVariables = AdminTypes.Exact<{
  companyId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetPrimaryContactCustomerIdQuery = { company?: AdminTypes.Maybe<{ mainContact?: AdminTypes.Maybe<{ customer: Pick<AdminTypes.Customer, 'id'> }> }> };

export type CreateCompanyMutationVariables = AdminTypes.Exact<{
  input: AdminTypes.CompanyCreateInput;
}>;


export type CreateCompanyMutation = { companyCreate?: AdminTypes.Maybe<{ company?: AdminTypes.Maybe<(
      Pick<AdminTypes.Company, 'id'>
      & { locations: { nodes: Array<Pick<AdminTypes.CompanyLocation, 'id'>> } }
    )> }> };

export type GetLocationContactsQueryVariables = AdminTypes.Exact<{
  locationId: AdminTypes.Scalars['ID']['input'];
  cursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
}>;


export type GetLocationContactsQuery = { companyLocation?: AdminTypes.Maybe<{ roleAssignments: { nodes: Array<(
        Pick<AdminTypes.CompanyContactRoleAssignment, 'id'>
        & { companyContact: (
          Pick<AdminTypes.CompanyContact, 'id'>
          & { customer: Pick<AdminTypes.Customer, 'id' | 'displayName' | 'firstName' | 'lastName' | 'email'> }
        ), role: Pick<AdminTypes.CompanyContactRole, 'name'> }
      )>, pageInfo: Pick<AdminTypes.PageInfo, 'hasNextPage' | 'endCursor'> } }> };

export type GetCompanyLocationQueryVariables = AdminTypes.Exact<{
  locationId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetCompanyLocationQuery = { companyLocation?: AdminTypes.Maybe<(
    Pick<AdminTypes.CompanyLocation, 'externalId' | 'name'>
    & { company: Pick<AdminTypes.Company, 'id'> }
  )> };

export type GetAdminCompanyLocationsQueryVariables = AdminTypes.Exact<{
  companyId: AdminTypes.Scalars['ID']['input'];
  cursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
}>;


export type GetAdminCompanyLocationsQuery = { company?: AdminTypes.Maybe<{ locations: { nodes: Array<Pick<AdminTypes.CompanyLocation, 'id' | 'name'>>, pageInfo: Pick<AdminTypes.PageInfo, 'endCursor' | 'hasNextPage'> } }> };

export type SetCompanyNameMutationVariables = AdminTypes.Exact<{
  companyId: AdminTypes.Scalars['ID']['input'];
  input: AdminTypes.CompanyInput;
}>;


export type SetCompanyNameMutation = { companyUpdate?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }> };

export type SetCompanyMainContactMutationVariables = AdminTypes.Exact<{
  companyId: AdminTypes.Scalars['ID']['input'];
  companyContactId: AdminTypes.Scalars['ID']['input'];
}>;


export type SetCompanyMainContactMutation = { companyAssignMainContact?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }> };

export type UpdateCompanyLocationMutationVariables = AdminTypes.Exact<{
  companyLocationId: AdminTypes.Scalars['ID']['input'];
  input: AdminTypes.CompanyLocationUpdateInput;
}>;


export type UpdateCompanyLocationMutation = { companyLocationUpdate?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }> };

export type AssignCompanyLocationAddressMutationVariables = AdminTypes.Exact<{
  locationId: AdminTypes.Scalars['ID']['input'];
  address: AdminTypes.CompanyAddressInput;
  addressTypes: Array<AdminTypes.CompanyAddressType> | AdminTypes.CompanyAddressType;
}>;


export type AssignCompanyLocationAddressMutation = { companyLocationAssignAddress?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }> };

export type GetCompanyRolesQueryVariables = AdminTypes.Exact<{
  companyId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetCompanyRolesQuery = { company?: AdminTypes.Maybe<{ contactRoles: { nodes: Array<Pick<AdminTypes.CompanyContactRole, 'id' | 'name'>>, pageInfo: Pick<AdminTypes.PageInfo, 'hasNextPage'> } }> };

export type DeleteCompanyContactMutationVariables = AdminTypes.Exact<{
  companyContactId: AdminTypes.Scalars['ID']['input'];
}>;


export type DeleteCompanyContactMutation = { companyContactDelete?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }> };

export type RevokeLocationRolesMutationVariables = AdminTypes.Exact<{
  locationId: AdminTypes.Scalars['ID']['input'];
  revokeAssignments: Array<AdminTypes.Scalars['ID']['input']> | AdminTypes.Scalars['ID']['input'];
}>;


export type RevokeLocationRolesMutation = { companyLocationRevokeRoles?: AdminTypes.Maybe<(
    Pick<AdminTypes.CompanyLocationRevokeRolesPayload, 'revokedRoleAssignmentIds'>
    & { userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }
  )> };

export type AssignLocationRolesMutationVariables = AdminTypes.Exact<{
  locationId: AdminTypes.Scalars['ID']['input'];
  toAssign: Array<AdminTypes.CompanyLocationRoleAssign> | AdminTypes.CompanyLocationRoleAssign;
}>;


export type AssignLocationRolesMutation = { companyLocationAssignRoles?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }> };

export type AssignContactToCompanyMutationVariables = AdminTypes.Exact<{
  companyId: AdminTypes.Scalars['ID']['input'];
  customerId: AdminTypes.Scalars['ID']['input'];
}>;


export type AssignContactToCompanyMutation = { companyAssignCustomerAsContact?: AdminTypes.Maybe<{ companyContact?: AdminTypes.Maybe<Pick<AdminTypes.CompanyContact, 'id'>>, userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }> };

export type SendContactEmailMutationVariables = AdminTypes.Exact<{
  companyContactId: AdminTypes.Scalars['ID']['input'];
  email?: AdminTypes.InputMaybe<AdminTypes.EmailInput>;
}>;


export type SendContactEmailMutation = { companyContactSendWelcomeEmail?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.BusinessCustomerUserError, 'code' | 'field' | 'message'>> }> };

export type GetCustomerDisplayNameQueryVariables = AdminTypes.Exact<{
  customerId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetCustomerDisplayNameQuery = { customer?: AdminTypes.Maybe<Pick<AdminTypes.Customer, 'displayName'>> };

export type GetAdminCustomerEmailQueryVariables = AdminTypes.Exact<{
  customerId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetAdminCustomerEmailQuery = { customer?: AdminTypes.Maybe<Pick<AdminTypes.Customer, 'email'>> };

export type GetCustomerProfileQueryVariables = AdminTypes.Exact<{
  customerId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetCustomerProfileQuery = { customer?: AdminTypes.Maybe<Pick<AdminTypes.Customer, 'email' | 'displayName' | 'firstName' | 'lastName' | 'phone' | 'tags'>> };

export type CreateCustomerMutationVariables = AdminTypes.Exact<{
  input: AdminTypes.CustomerInput;
}>;


export type CreateCustomerMutation = { customerCreate?: AdminTypes.Maybe<{ customer?: AdminTypes.Maybe<Pick<AdminTypes.Customer, 'id'>>, userErrors: Array<Pick<AdminTypes.UserError, 'field' | 'message'>> }> };

export type GetAdminCustomerIdFromEmailQueryVariables = AdminTypes.Exact<{
  query: AdminTypes.Scalars['String']['input'];
}>;


export type GetAdminCustomerIdFromEmailQuery = { customers: { nodes: Array<Pick<AdminTypes.Customer, 'email' | 'id'>> } };

export type GetAllCompanyLocationsQueryVariables = AdminTypes.Exact<{
  companyId: AdminTypes.Scalars['ID']['input'];
  cursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
}>;


export type GetAllCompanyLocationsQuery = { company?: AdminTypes.Maybe<{ locations: { nodes: Array<(
        Pick<AdminTypes.CompanyLocation, 'id' | 'name' | 'phone'>
        & { billingAddress?: AdminTypes.Maybe<Pick<AdminTypes.CompanyAddress, 'id' | 'address1' | 'address2' | 'companyName' | 'firstName' | 'lastName' | 'phone' | 'city' | 'zoneCode' | 'zip' | 'recipient'>>, shippingAddress?: AdminTypes.Maybe<Pick<AdminTypes.CompanyAddress, 'id' | 'address1' | 'address2' | 'companyName' | 'firstName' | 'lastName' | 'phone' | 'city' | 'zoneCode' | 'zip' | 'recipient'>>, ordersCount?: AdminTypes.Maybe<Pick<AdminTypes.Count, 'count' | 'precision'>> }
      )>, pageInfo: Pick<AdminTypes.PageInfo, 'endCursor' | 'hasNextPage'> } }> };

export type GetContactLocationsViewQueryVariables = AdminTypes.Exact<{
  contactId: AdminTypes.Scalars['ID']['input'];
  cursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
}>;


export type GetContactLocationsViewQuery = { companyContact?: AdminTypes.Maybe<{ roleAssignments: { nodes: Array<(
        Pick<AdminTypes.CompanyContactRoleAssignment, 'id'>
        & { role: Pick<AdminTypes.CompanyContactRole, 'name'>, companyLocation: (
          Pick<AdminTypes.CompanyLocation, 'id' | 'name' | 'phone'>
          & { billingAddress?: AdminTypes.Maybe<Pick<AdminTypes.CompanyAddress, 'id' | 'address1' | 'address2' | 'companyName' | 'firstName' | 'lastName' | 'phone' | 'city' | 'zoneCode' | 'zip' | 'recipient'>>, shippingAddress?: AdminTypes.Maybe<Pick<AdminTypes.CompanyAddress, 'id' | 'address1' | 'address2' | 'companyName' | 'firstName' | 'lastName' | 'phone' | 'city' | 'zoneCode' | 'zip' | 'recipient'>>, ordersCount?: AdminTypes.Maybe<Pick<AdminTypes.Count, 'count' | 'precision'>> }
        ) }
      )> } }> };

export type GetCustomerCompaniesQueryVariables = AdminTypes.Exact<{
  customerId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetCustomerCompaniesQuery = { customer?: AdminTypes.Maybe<{ companyContactProfiles: Array<(
      Pick<AdminTypes.CompanyContact, 'id' | 'isMainContact'>
      & { company: Pick<AdminTypes.Company, 'name' | 'id'> }
    )> }> };

export type ContactHasLocationAssignmentQueryVariables = AdminTypes.Exact<{
  contactId: AdminTypes.Scalars['ID']['input'];
}>;


export type ContactHasLocationAssignmentQuery = { companyContact?: AdminTypes.Maybe<{ roleAssignments: { nodes: Array<Pick<AdminTypes.CompanyContactRoleAssignment, 'id'>> } }> };

export type GetContactLocationAssignmentQueryVariables = AdminTypes.Exact<{
  contactId: AdminTypes.Scalars['ID']['input'];
  query: AdminTypes.Scalars['String']['input'];
}>;


export type GetContactLocationAssignmentQuery = { companyContact?: AdminTypes.Maybe<{ roleAssignments: { nodes: Array<(
        Pick<AdminTypes.CompanyContactRoleAssignment, 'id'>
        & { companyLocation: Pick<AdminTypes.CompanyLocation, 'id'>, role: Pick<AdminTypes.CompanyContactRole, 'id' | 'name' | 'note'> }
      )> } }> };

export type GetContactLocationsQueryVariables = AdminTypes.Exact<{
  contactId: AdminTypes.Scalars['ID']['input'];
  cursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
}>;


export type GetContactLocationsQuery = { companyContact?: AdminTypes.Maybe<{ roleAssignments: { nodes: Array<(
        Pick<AdminTypes.CompanyContactRoleAssignment, 'id'>
        & { role: Pick<AdminTypes.CompanyContactRole, 'id' | 'name' | 'note'>, companyLocation: Pick<AdminTypes.CompanyLocation, 'id' | 'name'> }
      )> } }> };

export type GetCustomerAddressesQueryVariables = AdminTypes.Exact<{
  customerId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetCustomerAddressesQuery = { customer?: AdminTypes.Maybe<{ addresses: Array<Pick<AdminTypes.MailingAddress, 'id' | 'address1' | 'address2' | 'firstName' | 'lastName' | 'company' | 'phone' | 'city' | 'provinceCode' | 'province' | 'zip' | 'country'>> }> };

export type UpdateCustomerInfoMutationVariables = AdminTypes.Exact<{
  input: AdminTypes.CustomerInput;
}>;


export type UpdateCustomerInfoMutation = { customerUpdate?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.UserError, 'field' | 'message'>> }> };

export type UpdateCustomerAddressesMutationVariables = AdminTypes.Exact<{
  customerId: AdminTypes.Scalars['ID']['input'];
  addresses: Array<AdminTypes.MailingAddressInput> | AdminTypes.MailingAddressInput;
}>;


export type UpdateCustomerAddressesMutation = { customerUpdate?: AdminTypes.Maybe<{ customer?: AdminTypes.Maybe<{ addresses: Array<Pick<AdminTypes.MailingAddress, 'id'>> }>, userErrors: Array<Pick<AdminTypes.UserError, 'field' | 'message'>> }> };

export type UpdateCustomerDefaultAddressMutationVariables = AdminTypes.Exact<{
  customerId: AdminTypes.Scalars['ID']['input'];
  addressID: AdminTypes.Scalars['ID']['input'];
}>;


export type UpdateCustomerDefaultAddressMutation = { customerUpdateDefaultAddress?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.UserError, 'field' | 'message'>> }> };

export type FindDiscountsToAdvertiseQueryVariables = AdminTypes.Exact<{
  cursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
  query: AdminTypes.Scalars['String']['input'];
}>;


export type FindDiscountsToAdvertiseQuery = { automaticDiscountNodes: { pageInfo: Pick<AdminTypes.PageInfo, 'endCursor' | 'hasNextPage'>, nodes: Array<{ automaticDiscount: (
        Pick<AdminTypes.DiscountAutomaticBasic, 'title' | 'discountClass'>
        & { combinesWith: Pick<AdminTypes.DiscountCombinesWith, 'orderDiscounts' | 'productDiscounts'>, minimumRequirement: Pick<AdminTypes.DiscountMinimumQuantity, 'greaterThanOrEqualToQuantity'>, customerGets: { items: { products: { nodes: Array<Pick<AdminTypes.Product, 'id'>>, pageInfo: Pick<AdminTypes.PageInfo, 'hasNextPage'> } }, value: (
            { __typename: 'DiscountAmount' }
            & Pick<AdminTypes.DiscountAmount, 'appliesOnEachItem'>
            & { amount: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'> }
          ) | { __typename: 'DiscountOnQuantity' } | (
            { __typename: 'DiscountPercentage' }
            & Pick<AdminTypes.DiscountPercentage, 'percentage'>
          ) } }
      ) }> } };

export type SetMetafieldsMutationVariables = AdminTypes.Exact<{
  metafields: Array<AdminTypes.MetafieldsSetInput> | AdminTypes.MetafieldsSetInput;
}>;


export type SetMetafieldsMutation = { metafieldsSet?: AdminTypes.Maybe<{ metafields?: AdminTypes.Maybe<Array<Pick<AdminTypes.Metafield, 'id' | 'value'>>>, userErrors: Array<Pick<AdminTypes.MetafieldsSetUserError, 'code' | 'elementIndex' | 'field' | 'message'>> }> };

export type DeleteMetafieldsMutationVariables = AdminTypes.Exact<{
  identifiers: Array<AdminTypes.MetafieldIdentifierInput> | AdminTypes.MetafieldIdentifierInput;
}>;


export type DeleteMetafieldsMutation = { metafieldsDelete?: AdminTypes.Maybe<{ deletedMetafields?: AdminTypes.Maybe<Array<AdminTypes.Maybe<Pick<AdminTypes.MetafieldIdentifier, 'key' | 'namespace' | 'ownerId'>>>>, userErrors: Array<Pick<AdminTypes.UserError, 'field' | 'message'>> }> };

export type GetMetafieldQueryVariables = AdminTypes.Exact<{
  ownerId: AdminTypes.Scalars['ID']['input'];
  namespace: AdminTypes.Scalars['String']['input'];
  key: AdminTypes.Scalars['String']['input'];
}>;


export type GetMetafieldQuery = { node?: AdminTypes.Maybe<{ metafield?: AdminTypes.Maybe<Pick<AdminTypes.Metafield, 'value'>> } | { metafield?: AdminTypes.Maybe<Pick<AdminTypes.Metafield, 'value'>> }> };

export type GetJSONMetafieldQueryVariables = AdminTypes.Exact<{
  ownerId: AdminTypes.Scalars['ID']['input'];
  namespace: AdminTypes.Scalars['String']['input'];
  key: AdminTypes.Scalars['String']['input'];
}>;


export type GetJSONMetafieldQuery = { node?: AdminTypes.Maybe<{ metafield?: AdminTypes.Maybe<Pick<AdminTypes.Metafield, 'jsonValue'>> } | { metafield?: AdminTypes.Maybe<Pick<AdminTypes.Metafield, 'jsonValue'>> }> };

export type GetJSONMetafieldsQueryVariables = AdminTypes.Exact<{
  ownerId: AdminTypes.Scalars['ID']['input'];
  namespace: AdminTypes.Scalars['String']['input'];
  first?: AdminTypes.InputMaybe<AdminTypes.Scalars['Int']['input']>;
  last?: AdminTypes.InputMaybe<AdminTypes.Scalars['Int']['input']>;
  startCursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
  endCursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
}>;


export type GetJSONMetafieldsQuery = { node?: AdminTypes.Maybe<{ metafields: { nodes: Array<Pick<AdminTypes.Metafield, 'jsonValue' | 'key'>>, pageInfo: Pick<AdminTypes.PageInfo, 'hasNextPage' | 'hasPreviousPage' | 'endCursor' | 'startCursor'> } } | { metafields: { nodes: Array<Pick<AdminTypes.Metafield, 'jsonValue' | 'key'>>, pageInfo: Pick<AdminTypes.PageInfo, 'hasNextPage' | 'hasPreviousPage' | 'endCursor' | 'startCursor'> } }> };

export type GetShopMetafieldQueryVariables = AdminTypes.Exact<{
  namespace: AdminTypes.Scalars['String']['input'];
  key: AdminTypes.Scalars['String']['input'];
}>;


export type GetShopMetafieldQuery = { shop: { metafield?: AdminTypes.Maybe<Pick<AdminTypes.Metafield, 'value'>> } };

export type GetShopJSONMetafieldQueryVariables = AdminTypes.Exact<{
  namespace: AdminTypes.Scalars['String']['input'];
  key: AdminTypes.Scalars['String']['input'];
}>;


export type GetShopJSONMetafieldQuery = { shop: { metafield?: AdminTypes.Maybe<Pick<AdminTypes.Metafield, 'jsonValue'>> } };

export type FindMetafieldDefinitionIdQueryVariables = AdminTypes.Exact<{
  ownerType: AdminTypes.MetafieldOwnerType;
  namespace: AdminTypes.Scalars['String']['input'];
  key: AdminTypes.Scalars['String']['input'];
}>;


export type FindMetafieldDefinitionIdQuery = { metafieldDefinitions: { nodes: Array<Pick<AdminTypes.MetafieldDefinition, 'id'>> } };

export type UpdateDeviceHandleMFDMutationVariables = AdminTypes.Exact<{
  definition: AdminTypes.MetafieldDefinitionUpdateInput;
}>;


export type UpdateDeviceHandleMFDMutation = { metafieldDefinitionUpdate?: AdminTypes.Maybe<{ updatedDefinition?: AdminTypes.Maybe<Pick<AdminTypes.MetafieldDefinition, 'id'>>, userErrors: Array<Pick<AdminTypes.MetafieldDefinitionUpdateUserError, 'code' | 'elementIndex' | 'field' | 'message'>> }> };

export type CreateDeviceHandleMFDMutationVariables = AdminTypes.Exact<{
  definition: AdminTypes.MetafieldDefinitionInput;
}>;


export type CreateDeviceHandleMFDMutation = { metafieldDefinitionCreate?: AdminTypes.Maybe<{ createdDefinition?: AdminTypes.Maybe<Pick<AdminTypes.MetafieldDefinition, 'id'>>, userErrors: Array<Pick<AdminTypes.MetafieldDefinitionCreateUserError, 'code' | 'elementIndex' | 'field' | 'message'>> }> };

export type GetOrderADCCreateInputsQueryVariables = AdminTypes.Exact<{
  orderId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetOrderADCCreateInputsQuery = { order?: AdminTypes.Maybe<{ purchasingEntity?: AdminTypes.Maybe<(
      { __typename: 'Customer' }
      & Pick<AdminTypes.Customer, 'id' | 'email' | 'firstName' | 'lastName' | 'phone'>
      & { defaultAddress?: AdminTypes.Maybe<Pick<AdminTypes.MailingAddress, 'address1' | 'address2' | 'city' | 'countryCodeV2' | 'country' | 'phone' | 'provinceCode' | 'zip' | 'firstName' | 'lastName'>> }
    ) | (
      { __typename: 'PurchasingCompany' }
      & { company: Pick<AdminTypes.Company, 'id' | 'name'>, contact?: AdminTypes.Maybe<{ customer: Pick<AdminTypes.Customer, 'id' | 'email' | 'phone'> }>, location: (
        Pick<AdminTypes.CompanyLocation, 'id' | 'phone'>
        & { shippingAddress?: AdminTypes.Maybe<Pick<AdminTypes.CompanyAddress, 'address1' | 'address2' | 'city' | 'phone' | 'province' | 'zoneCode' | 'zip'>> }
      ) }
    )> }> };

export type GetOrderCustomerIdQueryVariables = AdminTypes.Exact<{
  orderId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetOrderCustomerIdQuery = { order?: AdminTypes.Maybe<{ customer?: AdminTypes.Maybe<Pick<AdminTypes.Customer, 'id' | 'email'>> }> };

export type GetOrderPurchasingEntityQueryVariables = AdminTypes.Exact<{
  orderId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetOrderPurchasingEntityQuery = { order?: AdminTypes.Maybe<{ purchasingEntity?: AdminTypes.Maybe<(
      { __typename: 'Customer' }
      & Pick<AdminTypes.Customer, 'id'>
    ) | (
      { __typename: 'PurchasingCompany' }
      & { company: Pick<AdminTypes.Company, 'id'>, location: Pick<AdminTypes.CompanyLocation, 'id'> }
    )> }> };

export type GetOrderAttributesQueryVariables = AdminTypes.Exact<{
  orderId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetOrderAttributesQuery = { order?: AdminTypes.Maybe<{ customAttributes: Array<Pick<AdminTypes.Attribute, 'key' | 'value'>> }> };

export type GetOrderLineItemsQueryVariables = AdminTypes.Exact<{
  orderId: AdminTypes.Scalars['ID']['input'];
  cursor?: AdminTypes.InputMaybe<AdminTypes.Scalars['String']['input']>;
}>;


export type GetOrderLineItemsQuery = { order?: AdminTypes.Maybe<{ lineItems: { nodes: Array<(
        Pick<AdminTypes.LineItem, 'quantity'>
        & { product?: AdminTypes.Maybe<(
          Pick<AdminTypes.Product, 'handle'>
          & { deviceHandle?: AdminTypes.Maybe<Pick<AdminTypes.Metafield, 'value'>> }
        )> }
      )>, pageInfo: Pick<AdminTypes.PageInfo, 'hasNextPage' | 'endCursor'> } }> };

export type GetShopIDQueryVariables = AdminTypes.Exact<{ [key: string]: never; }>;


export type GetShopIDQuery = { shop: Pick<AdminTypes.Shop, 'id'> };

export type GetSystemOwnerNameQueryVariables = AdminTypes.Exact<{
  ownerId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetSystemOwnerNameQuery = { node?: AdminTypes.Maybe<{ __typename: 'AbandonedCheckout' | 'AbandonedCheckoutLineItem' | 'Abandonment' | 'AddAllProductsOperation' | 'AdditionalFee' | 'App' | 'AppCatalog' | 'AppCredit' | 'AppInstallation' | 'AppPurchaseOneTime' | 'AppRevenueAttributionRecord' | 'AppSubscription' | 'AppUsageRecord' | 'Article' | 'BasicEvent' | 'Blog' | 'BulkOperation' | 'BusinessEntity' | 'CalculatedOrder' | 'CartTransform' } | { __typename: 'CashTrackingAdjustment' | 'CashTrackingSession' | 'CatalogCsvOperation' | 'Channel' | 'ChannelDefinition' | 'ChannelInformation' | 'CheckoutProfile' | 'Collection' | 'Comment' | 'CommentEvent' | 'CompanyAddress' | 'CompanyContact' | 'CompanyContactRole' | 'CompanyContactRoleAssignment' | 'CompanyLocation' | 'CompanyLocationCatalog' | 'CompanyLocationStaffMemberAssignment' | 'CustomerAccountAppExtensionPage' | 'CustomerAccountNativePage' | 'CustomerPaymentMethod' } | { __typename: 'CustomerSegmentMembersQuery' | 'CustomerVisit' | 'DeliveryCarrierService' | 'DeliveryCondition' | 'DeliveryCountry' | 'DeliveryCustomization' | 'DeliveryLocationGroup' | 'DeliveryMethod' | 'DeliveryMethodDefinition' | 'DeliveryParticipant' | 'DeliveryProfile' | 'DeliveryProfileItem' | 'DeliveryPromiseProvider' | 'DeliveryProvince' | 'DeliveryRateDefinition' | 'DeliveryZone' | 'DiscountAutomaticBxgy' | 'DiscountAutomaticNode' | 'DiscountCodeNode' | 'DiscountNode' } | { __typename: 'DiscountRedeemCodeBulkCreation' | 'Domain' | 'DraftOrder' | 'DraftOrderLineItem' | 'DraftOrderTag' | 'Duty' | 'ExchangeLineItem' | 'ExchangeV2' | 'ExternalVideo' | 'Fulfillment' | 'FulfillmentConstraintRule' | 'FulfillmentEvent' | 'FulfillmentHold' | 'FulfillmentLineItem' | 'FulfillmentOrder' | 'FulfillmentOrderDestination' | 'FulfillmentOrderLineItem' | 'FulfillmentOrderMerchantRequest' | 'GenericFile' | 'GiftCard' } | { __typename: 'GiftCardCreditTransaction' | 'GiftCardDebitTransaction' | 'InventoryAdjustmentGroup' | 'InventoryItem' | 'InventoryItemMeasurement' | 'InventoryLevel' | 'InventoryQuantity' | 'LineItem' | 'LineItemGroup' | 'Location' | 'MailingAddress' | 'Market' | 'MarketCatalog' | 'MarketRegionCountry' | 'MarketWebPresence' | 'MarketingActivity' | 'MarketingEvent' | 'MediaImage' | 'Menu' | 'Metafield' } | { __typename: 'MetafieldDefinition' | 'MetafieldStorefrontVisibility' | 'Metaobject' | 'MetaobjectDefinition' | 'Model3d' | 'OnlineStoreTheme' | 'Order' | 'OrderAdjustment' | 'OrderDisputeSummary' | 'OrderTransaction' | 'Page' | 'PaymentCustomization' | 'PaymentMandate' | 'PaymentSchedule' | 'PaymentTerms' | 'PaymentTermsTemplate' | 'PriceList' | 'PriceRule' | 'PriceRuleDiscountCode' | 'PrivateMetafield' } | { __typename: 'Product' | 'ProductBundleOperation' | 'ProductDeleteOperation' | 'ProductDuplicateOperation' | 'ProductFeed' | 'ProductOption' | 'ProductOptionValue' | 'ProductSetOperation' | 'ProductTaxonomyNode' | 'ProductVariant' | 'ProductVariantComponent' | 'Publication' | 'PublicationResourceOperation' | 'QuantityPriceBreak' | 'Refund' | 'RefundShippingLine' | 'Return' | 'ReturnLineItem' | 'ReturnableFulfillment' | 'ReverseDelivery' } | { __typename: 'ReverseDeliveryLineItem' | 'ReverseFulfillmentOrder' | 'ReverseFulfillmentOrderDisposition' | 'ReverseFulfillmentOrderLineItem' | 'SaleAdditionalFee' | 'SavedSearch' | 'ScriptTag' | 'Segment' | 'SellingPlan' | 'SellingPlanGroup' | 'ServerPixel' | 'Shop' | 'ShopAddress' | 'ShopPolicy' | 'ShopifyPaymentsAccount' | 'ShopifyPaymentsBalanceTransaction' | 'ShopifyPaymentsBankAccount' | 'ShopifyPaymentsDispute' | 'ShopifyPaymentsDisputeEvidence' | 'ShopifyPaymentsDisputeFileUpload' } | { __typename: 'ShopifyPaymentsDisputeFulfillment' | 'ShopifyPaymentsPayout' | 'ShopifyPaymentsVerification' | 'StaffMember' | 'StandardMetafieldDefinitionTemplate' | 'StoreCreditAccount' | 'StoreCreditAccountCreditTransaction' | 'StoreCreditAccountDebitRevertTransaction' | 'StoreCreditAccountDebitTransaction' | 'StorefrontAccessToken' | 'SubscriptionBillingAttempt' | 'SubscriptionContract' | 'SubscriptionDraft' | 'TaxonomyAttribute' | 'TaxonomyCategory' | 'TaxonomyChoiceListAttribute' | 'TaxonomyMeasurementAttribute' | 'TaxonomyValue' | 'TenderTransaction' | 'TransactionFee' } | { __typename: 'UnverifiedReturnLineItem' | 'UrlRedirect' | 'UrlRedirectImport' | 'Validation' | 'Video' | 'WebPixel' | 'WebhookSubscription' } | (
    { __typename: 'Company' }
    & Pick<AdminTypes.Company, 'name'>
  ) | (
    { __typename: 'Customer' }
    & Pick<AdminTypes.Customer, 'displayName'>
  )> };

export type GetTagsQueryVariables = AdminTypes.Exact<{
  ownerId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetTagsQuery = { node?: AdminTypes.Maybe<{ __typename: 'AbandonedCheckout' | 'AbandonedCheckoutLineItem' | 'Abandonment' | 'AddAllProductsOperation' | 'AdditionalFee' | 'App' | 'AppCatalog' | 'AppCredit' | 'AppInstallation' | 'AppPurchaseOneTime' | 'AppRevenueAttributionRecord' | 'AppSubscription' | 'AppUsageRecord' | 'Article' | 'BasicEvent' | 'Blog' | 'BulkOperation' | 'BusinessEntity' | 'CalculatedOrder' | 'CartTransform' } | { __typename: 'CashTrackingAdjustment' | 'CashTrackingSession' | 'CatalogCsvOperation' | 'Channel' | 'ChannelDefinition' | 'ChannelInformation' | 'CheckoutProfile' | 'Collection' | 'Comment' | 'CommentEvent' | 'Company' | 'CompanyAddress' | 'CompanyContact' | 'CompanyContactRole' | 'CompanyContactRoleAssignment' | 'CompanyLocation' | 'CompanyLocationCatalog' | 'CompanyLocationStaffMemberAssignment' | 'CustomerAccountAppExtensionPage' | 'CustomerAccountNativePage' } | { __typename: 'CustomerPaymentMethod' | 'CustomerSegmentMembersQuery' | 'CustomerVisit' | 'DeliveryCarrierService' | 'DeliveryCondition' | 'DeliveryCountry' | 'DeliveryCustomization' | 'DeliveryLocationGroup' | 'DeliveryMethod' | 'DeliveryMethodDefinition' | 'DeliveryParticipant' | 'DeliveryProfile' | 'DeliveryProfileItem' | 'DeliveryPromiseProvider' | 'DeliveryProvince' | 'DeliveryRateDefinition' | 'DeliveryZone' | 'DiscountAutomaticBxgy' | 'DiscountAutomaticNode' | 'DiscountCodeNode' } | { __typename: 'DiscountNode' | 'DiscountRedeemCodeBulkCreation' | 'Domain' | 'DraftOrder' | 'DraftOrderLineItem' | 'DraftOrderTag' | 'Duty' | 'ExchangeLineItem' | 'ExchangeV2' | 'ExternalVideo' | 'Fulfillment' | 'FulfillmentConstraintRule' | 'FulfillmentEvent' | 'FulfillmentHold' | 'FulfillmentLineItem' | 'FulfillmentOrder' | 'FulfillmentOrderDestination' | 'FulfillmentOrderLineItem' | 'FulfillmentOrderMerchantRequest' | 'GenericFile' } | { __typename: 'GiftCard' | 'GiftCardCreditTransaction' | 'GiftCardDebitTransaction' | 'InventoryAdjustmentGroup' | 'InventoryItem' | 'InventoryItemMeasurement' | 'InventoryLevel' | 'InventoryQuantity' | 'LineItem' | 'LineItemGroup' | 'Location' | 'MailingAddress' | 'Market' | 'MarketCatalog' | 'MarketRegionCountry' | 'MarketWebPresence' | 'MarketingActivity' | 'MarketingEvent' | 'MediaImage' | 'Menu' } | { __typename: 'Metafield' | 'MetafieldDefinition' | 'MetafieldStorefrontVisibility' | 'Metaobject' | 'MetaobjectDefinition' | 'Model3d' | 'OnlineStoreTheme' | 'OrderAdjustment' | 'OrderDisputeSummary' | 'OrderTransaction' | 'Page' | 'PaymentCustomization' | 'PaymentMandate' | 'PaymentSchedule' | 'PaymentTerms' | 'PaymentTermsTemplate' | 'PriceList' | 'PriceRule' | 'PriceRuleDiscountCode' | 'PrivateMetafield' } | { __typename: 'ProductBundleOperation' | 'ProductDeleteOperation' | 'ProductDuplicateOperation' | 'ProductFeed' | 'ProductOption' | 'ProductOptionValue' | 'ProductSetOperation' | 'ProductTaxonomyNode' | 'ProductVariant' | 'ProductVariantComponent' | 'Publication' | 'PublicationResourceOperation' | 'QuantityPriceBreak' | 'Refund' | 'RefundShippingLine' | 'Return' | 'ReturnLineItem' | 'ReturnableFulfillment' | 'ReverseDelivery' | 'ReverseDeliveryLineItem' } | { __typename: 'ReverseFulfillmentOrder' | 'ReverseFulfillmentOrderDisposition' | 'ReverseFulfillmentOrderLineItem' | 'SaleAdditionalFee' | 'SavedSearch' | 'ScriptTag' | 'Segment' | 'SellingPlan' | 'SellingPlanGroup' | 'ServerPixel' | 'Shop' | 'ShopAddress' | 'ShopPolicy' | 'ShopifyPaymentsAccount' | 'ShopifyPaymentsBalanceTransaction' | 'ShopifyPaymentsBankAccount' | 'ShopifyPaymentsDispute' | 'ShopifyPaymentsDisputeEvidence' | 'ShopifyPaymentsDisputeFileUpload' | 'ShopifyPaymentsDisputeFulfillment' } | { __typename: 'ShopifyPaymentsPayout' | 'ShopifyPaymentsVerification' | 'StaffMember' | 'StandardMetafieldDefinitionTemplate' | 'StoreCreditAccount' | 'StoreCreditAccountCreditTransaction' | 'StoreCreditAccountDebitRevertTransaction' | 'StoreCreditAccountDebitTransaction' | 'StorefrontAccessToken' | 'SubscriptionBillingAttempt' | 'SubscriptionContract' | 'SubscriptionDraft' | 'TaxonomyAttribute' | 'TaxonomyCategory' | 'TaxonomyChoiceListAttribute' | 'TaxonomyMeasurementAttribute' | 'TaxonomyValue' | 'TenderTransaction' | 'TransactionFee' | 'UnverifiedReturnLineItem' } | { __typename: 'UrlRedirect' | 'UrlRedirectImport' | 'Validation' | 'Video' | 'WebPixel' | 'WebhookSubscription' } | (
    { __typename: 'Customer' }
    & Pick<AdminTypes.Customer, 'tags'>
  ) | (
    { __typename: 'Order' }
    & Pick<AdminTypes.Order, 'tags'>
  ) | (
    { __typename: 'Product' }
    & Pick<AdminTypes.Product, 'tags'>
  )> };

export type AddTagMutationVariables = AdminTypes.Exact<{
  ownerId: AdminTypes.Scalars['ID']['input'];
  tags: Array<AdminTypes.Scalars['String']['input']> | AdminTypes.Scalars['String']['input'];
}>;


export type AddTagMutation = { tagsAdd?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.UserError, 'field' | 'message'>> }> };

export type RemoveTagMutationVariables = AdminTypes.Exact<{
  ownerId: AdminTypes.Scalars['ID']['input'];
  tags: Array<AdminTypes.Scalars['String']['input']> | AdminTypes.Scalars['String']['input'];
}>;


export type RemoveTagMutation = { tagsRemove?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.UserError, 'field' | 'message'>> }> };

export type TriggerFlowMutationVariables = AdminTypes.Exact<{
  handle: AdminTypes.Scalars['String']['input'];
  payload?: AdminTypes.InputMaybe<AdminTypes.Scalars['JSON']['input']>;
}>;


export type TriggerFlowMutation = { flowTriggerReceive?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.UserError, 'field' | 'message'>> }> };

interface GeneratedQueryTypes {
  "#graphql\n      query GetSystemOwnerContactInfo($ownerId: ID!) {\n        node(id: $ownerId) {\n          __typename\n          ... on Customer {\n            firstName,\n            lastName,\n            phone\n            email\n            defaultAddress {\n              id\n            }\n            addresses {\n              ...CustomerAddressView\n            }\n          }\n          ... on Company {\n            name\n            mainContact {\n              id\n              customer {\n                id\n                email\n                displayName\n                phone\n              }\n            }\n          }\n        }\n      }\n      #graphql\n  fragment CustomerAddressView on MailingAddress {\n    id\n    address1\n    address2\n    firstName\n    lastName\n    company\n    phone\n    city\n    provinceCode\n    province\n    zip\n    country\n  }\n\n      ": {return: GetSystemOwnerContactInfoQuery, variables: GetSystemOwnerContactInfoQueryVariables},
  "#graphql\n      query GetPrimaryContactCustomerId($companyId: ID!) {\n        company(id: $companyId) {\n          mainContact {\n            customer {\n              id\n            }\n          }\n        }\n      }": {return: GetPrimaryContactCustomerIdQuery, variables: GetPrimaryContactCustomerIdQueryVariables},
  "#graphql\n      query GetLocationContacts($locationId: ID!, $cursor: String) {\n        companyLocation(id: $locationId) {\n          roleAssignments(first: 3, after: $cursor) {\n            nodes {\n              id\n              companyContact {\n                id\n                customer {\n                  id\n                  displayName\n                  firstName\n                  lastName\n                  email\n                }\n              }\n              role {\n                name\n              }\n            }\n            pageInfo {\n              hasNextPage\n              endCursor\n            }\n          }\n        }\n      }": {return: GetLocationContactsQuery, variables: GetLocationContactsQueryVariables},
  "#graphql\n      query GetCompanyLocation($locationId: ID!) {\n        companyLocation(id: $locationId) {\n          externalId\n          name\n          company {\n            id\n          }\n        }\n      }": {return: GetCompanyLocationQuery, variables: GetCompanyLocationQueryVariables},
  "#graphql\n        query GetAdminCompanyLocations($companyId: ID!, $cursor: String) {\n          company(id: $companyId) {\n            locations(first: 3, after: $cursor) {\n              nodes {\n                id\n                name\n              }\n              pageInfo {\n                endCursor\n                hasNextPage\n              }\n            }\n          }\n        }": {return: GetAdminCompanyLocationsQuery, variables: GetAdminCompanyLocationsQueryVariables},
  "#graphql\n      query GetCompanyRoles($companyId: ID!) {\n        company(id: $companyId) {\n          contactRoles(first: 2) {\n            nodes {\n              id\n              name\n            }\n            pageInfo {\n              hasNextPage\n            }\n          }\n        }\n      }": {return: GetCompanyRolesQuery, variables: GetCompanyRolesQueryVariables},
  "#graphql\n        query GetCustomerDisplayName($customerId: ID!) {\n          customer(id: $customerId) {\n            displayName\n          }\n        }": {return: GetCustomerDisplayNameQuery, variables: GetCustomerDisplayNameQueryVariables},
  "#graphql\n        query GetAdminCustomerEmail($customerId: ID!) {\n          customer(id: $customerId) {\n            email\n          }\n        }": {return: GetAdminCustomerEmailQuery, variables: GetAdminCustomerEmailQueryVariables},
  "#graphql\n        query GetCustomerProfile($customerId: ID!) {\n          customer(id: $customerId) {\n            email\n            displayName\n            firstName\n            lastName\n            phone\n            tags\n          }\n        }": {return: GetCustomerProfileQuery, variables: GetCustomerProfileQueryVariables},
  "#graphql\n      query GetAdminCustomerIdFromEmail($query: String!) {\n        customers(first: 1, query: $query) {\n          nodes {\n            email\n            id\n          }\n        }\n      }": {return: GetAdminCustomerIdFromEmailQuery, variables: GetAdminCustomerIdFromEmailQueryVariables},
  "#graphql\n        query GetAllCompanyLocations($companyId: ID!, $cursor: String) {\n          company(id: $companyId) {\n            locations(first: 3, after: $cursor) {\n              nodes {\n                ...CompanyLocationView\n              }\n              pageInfo {\n                endCursor\n                hasNextPage\n              }\n            }\n          }\n        }\n        #graphql\n  fragment CompanyAddressView on CompanyAddress {\n    id\n    address1\n    address2\n    companyName\n    firstName\n    lastName\n    phone\n    city\n    zoneCode\n    zip\n    recipient\n  }\n  fragment CompanyLocationView on CompanyLocation {\n    id\n    billingAddress {\n      ...CompanyAddressView\n    }\n    shippingAddress {\n      ...CompanyAddressView\n    }\n    name\n    phone\n    ordersCount {\n      count\n      precision\n    }\n  }\n": {return: GetAllCompanyLocationsQuery, variables: GetAllCompanyLocationsQueryVariables},
  "#graphql\n        query GetContactLocationsView($contactId: ID!, $cursor: String) {\n          companyContact (id: $contactId) {\n            roleAssignments(first: 3, after: $cursor) {\n              nodes {\n                id\n                role {\n                  name\n                },\n                companyLocation {\n                  ... CompanyLocationView\n                }\n              }\n            }\n          }\n        }\n        #graphql\n  fragment CompanyAddressView on CompanyAddress {\n    id\n    address1\n    address2\n    companyName\n    firstName\n    lastName\n    phone\n    city\n    zoneCode\n    zip\n    recipient\n  }\n  fragment CompanyLocationView on CompanyLocation {\n    id\n    billingAddress {\n      ...CompanyAddressView\n    }\n    shippingAddress {\n      ...CompanyAddressView\n    }\n    name\n    phone\n    ordersCount {\n      count\n      precision\n    }\n  }\n": {return: GetContactLocationsViewQuery, variables: GetContactLocationsViewQueryVariables},
  "#graphql\n      query GetCustomerCompanies($customerId: ID!) {\n        customer(id: $customerId) {\n          companyContactProfiles {\n            id\n            company {\n              name\n              id\n            }\n            isMainContact\n          }\n        }\n      }": {return: GetCustomerCompaniesQuery, variables: GetCustomerCompaniesQueryVariables},
  "#graphql\n      query ContactHasLocationAssignment($contactId: ID!) {\n        companyContact(id: $contactId)  {\n          roleAssignments(first: 1) {\n            nodes {\n              id\n            }\n          }\n        }\n      }": {return: ContactHasLocationAssignmentQuery, variables: ContactHasLocationAssignmentQueryVariables},
  "#graphql\n      query GetContactLocationAssignment($contactId: ID!, $query: String!) {\n        companyContact(id: $contactId)  {\n          roleAssignments(first: 1, query: $query) {\n            nodes {\n              id\n              companyLocation {\n                id\n              }\n              role {\n                id\n                name\n                note\n              }\n            }\n          }\n        }\n      }": {return: GetContactLocationAssignmentQuery, variables: GetContactLocationAssignmentQueryVariables},
  "#graphql\n      query GetContactLocations($contactId: ID!, $cursor: String) {\n        companyContact (id: $contactId) {\n          roleAssignments(first: 3, after: $cursor, sortKey: LOCATION_NAME) {\n            nodes {\n              id\n              role {\n                id\n                name\n                note\n              },\n              companyLocation {\n                id\n                name\n              }\n            }\n          }\n        }\n      }": {return: GetContactLocationsQuery, variables: GetContactLocationsQueryVariables},
  "#graphql\n      query GetCustomerAddresses($customerId: ID!) {\n        customer(id: $customerId)  {\n          addresses{\n            ...CustomerAddressView\n          }\n        }\n      }\n      #graphql\n  fragment CustomerAddressView on MailingAddress {\n    id\n    address1\n    address2\n    firstName\n    lastName\n    company\n    phone\n    city\n    provinceCode\n    province\n    zip\n    country\n  }\n": {return: GetCustomerAddressesQuery, variables: GetCustomerAddressesQueryVariables},
  "#graphql\n        query FindDiscountsToAdvertise($cursor: String, $query: String!) {\n          automaticDiscountNodes(after: $cursor, first: 1, query: $query) {\n            pageInfo {\n              endCursor\n              hasNextPage\n            }\n            nodes {\n              automaticDiscount {\n                ... on DiscountAutomaticBasic {\n                  title\n                  combinesWith {\n                    orderDiscounts\n                    productDiscounts\n                  }\n                  discountClass\n                  minimumRequirement {\n                    ... on DiscountMinimumQuantity {\n                      greaterThanOrEqualToQuantity\n                    }\n                  }\n                  customerGets {\n                    items {\n                      ... on DiscountProducts {\n                        products(first: 1) {\n                          nodes {\n                            id\n                          }\n                          pageInfo {\n                            hasNextPage\n                          }\n                        }\n                      }\n                    }\n                    value {\n                      __typename\n                      ... on DiscountAmount {\n                        amount {\n                          amount\n                          currencyCode\n                        }\n                        appliesOnEachItem\n                      }\n                      ... on DiscountPercentage {\n                        percentage\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }": {return: FindDiscountsToAdvertiseQuery, variables: FindDiscountsToAdvertiseQueryVariables},
  "#graphql\n        query GetMetafield($ownerId: ID!, $namespace: String!, $key: String!) {\n            node(id: $ownerId) {\n                ... on HasMetafields {\n                    metafield(namespace: $namespace, key: $key) {\n                        value\n                    }\n                }\n            }\n        }\n    ": {return: GetMetafieldQuery, variables: GetMetafieldQueryVariables},
  "#graphql\n        query GetJSONMetafield($ownerId: ID!, $namespace: String!, $key: String!) {\n            node(id: $ownerId) {\n                ... on HasMetafields {\n                    metafield(namespace: $namespace, key: $key) {\n                        jsonValue\n                    }\n                }\n            }\n        }\n    ": {return: GetJSONMetafieldQuery, variables: GetJSONMetafieldQueryVariables},
  "#graphql\n      query GetJSONMetafields($ownerId: ID!, $namespace: String!, $first: Int, $last: Int, $startCursor: String, $endCursor: String) {\n        node(id: $ownerId) {\n          ... on HasMetafields {\n            metafields(namespace: $namespace, first: $first, last: $last, after: $startCursor, before: $endCursor) {\n              nodes {\n                jsonValue\n                key\n              }\n              pageInfo {\n                hasNextPage\n                hasPreviousPage\n                endCursor\n                startCursor\n              }\n            }\n          }\n        }\n      }": {return: GetJSONMetafieldsQuery, variables: GetJSONMetafieldsQueryVariables},
  "#graphql\n        query GetShopMetafield($namespace: String!, $key: String!) {\n            shop {\n                metafield(namespace: $namespace, key: $key) {\n                    value\n                }\n            }\n        }": {return: GetShopMetafieldQuery, variables: GetShopMetafieldQueryVariables},
  "#graphql\n        query GetShopJSONMetafield($namespace: String!, $key: String!) {\n            shop {\n                metafield(namespace: $namespace, key: $key) {\n                    jsonValue\n                }\n            }\n        }": {return: GetShopJSONMetafieldQuery, variables: GetShopJSONMetafieldQueryVariables},
  "#graphql\n      query FindMetafieldDefinitionId($ownerType: MetafieldOwnerType!, $namespace: String!, $key: String!) {\n        metafieldDefinitions(first: 1, ownerType: $ownerType, namespace: $namespace, key: $key) {\n          nodes {\n            id\n          }\n        }\n      }\n    ": {return: FindMetafieldDefinitionIdQuery, variables: FindMetafieldDefinitionIdQueryVariables},
  "#graphql\n        query GetOrderADCCreateInputs($orderId: ID!) {\n          order(id: $orderId) {\n            purchasingEntity {\n              __typename\n              ... on Customer {\n                id\n                defaultAddress {\n                  address1\n                  address2\n                  city\n                  countryCodeV2\n                  country\n                  phone\n                  provinceCode\n                  zip\n                  firstName\n                  lastName\n                }\n                email\n                firstName\n                lastName\n                phone\n              }\n              ... on PurchasingCompany {\n                __typename\n                company {\n                  id\n                  name\n                },\n                contact {\n                  customer {\n                    id\n                    email\n                    phone\n                  }\n                }\n                location {\n                  id\n                  phone\n                  shippingAddress {\n                    address1\n                    address2\n                    city\n                    phone\n                    province\n                    zoneCode\n                    zip\n                  }\n                }\n              }\n            }\n          }\n        }": {return: GetOrderADCCreateInputsQuery, variables: GetOrderADCCreateInputsQueryVariables},
  "#graphql\n      query GetOrderCustomerId($orderId: ID!) {\n        order(id: $orderId) {\n          customer {\n            id\n            email\n          }\n        }\n      }\n    ": {return: GetOrderCustomerIdQuery, variables: GetOrderCustomerIdQueryVariables},
  "#graphql\n      query GetOrderPurchasingEntity($orderId: ID!) {\n        order(id: $orderId) {\n          purchasingEntity {\n            __typename\n            ... on Customer {\n              id\n            }\n            ... on PurchasingCompany {\n              company {\n                id\n              }\n              location {\n                id\n              }\n            }\n          }\n        }\n      }\n    ": {return: GetOrderPurchasingEntityQuery, variables: GetOrderPurchasingEntityQueryVariables},
  "#graphql\n      query GetOrderAttributes($orderId: ID!) {\n        order(id: $orderId) {\n          customAttributes {\n            key\n            value\n          }\n        }\n      }\n    ": {return: GetOrderAttributesQuery, variables: GetOrderAttributesQueryVariables},
  "#graphql\n            query GetOrderLineItems($orderId: ID!, $cursor: String) {\n              order(id: $orderId) {\n                lineItems(first: 5, after: $cursor) {\n                  nodes {\n                    quantity\n                    product {\n                      handle\n                      deviceHandle: metafield(namespace: \"device\", key: \"handle\") {\n                        value\n                      }\n                    }\n                  }\n                  pageInfo {\n                    hasNextPage\n                    endCursor\n                  }\n                }\n              }\n            }": {return: GetOrderLineItemsQuery, variables: GetOrderLineItemsQueryVariables},
  "#graphql\n        query GetShopID {\n            shop {\n                id\n            }\n        }": {return: GetShopIDQuery, variables: GetShopIDQueryVariables},
  "#graphql\n      query GetSystemOwnerName($ownerId: ID!) {\n        node(id: $ownerId) {\n          __typename\n          ... on Customer {\n            displayName\n          }\n          ... on Company {\n            name\n          }\n        }\n      }": {return: GetSystemOwnerNameQuery, variables: GetSystemOwnerNameQueryVariables},
  "#graphql\n            query GetTags($ownerId: ID!) {\n                node(id: $ownerId) {\n                    __typename\n                    ... on Order {\n                        tags\n                    }\n                    ... on Customer {\n                        tags\n                    }\n                    ... on Product {\n                        tags\n                    }\n                }\n            }": {return: GetTagsQuery, variables: GetTagsQueryVariables},
}

interface GeneratedMutationTypes {
  "#graphql\n      mutation CreateCompany($input: CompanyCreateInput!) {\n        companyCreate(input: $input) {\n          company {\n            id\n            locations(first: 1) {\n              nodes {\n                id\n              }\n            }\n          }\n        }\n      }": {return: CreateCompanyMutation, variables: CreateCompanyMutationVariables},
  "#graphql\n      mutation SetCompanyName($companyId: ID!, $input: CompanyInput!) {\n        companyUpdate(companyId: $companyId, input: $input) {\n          userErrors {\n            code\n            field\n            message\n          }\n        } \n      }": {return: SetCompanyNameMutation, variables: SetCompanyNameMutationVariables},
  "#graphql\n      mutation SetCompanyMainContact($companyId: ID!, $companyContactId: ID!) {\n        companyAssignMainContact(companyId: $companyId, companyContactId: $companyContactId) {\n          userErrors {\n            code\n            field\n            message\n          }\n        }\n      }": {return: SetCompanyMainContactMutation, variables: SetCompanyMainContactMutationVariables},
  "#graphql\n      mutation UpdateCompanyLocation($companyLocationId: ID!, $input: CompanyLocationUpdateInput!) {\n        companyLocationUpdate(companyLocationId: $companyLocationId, input: $input) {\n          userErrors {\n            code\n            field\n            message\n          }\n        }\n      }": {return: UpdateCompanyLocationMutation, variables: UpdateCompanyLocationMutationVariables},
  "#graphql\n      mutation AssignCompanyLocationAddress($locationId: ID!, $address: CompanyAddressInput!, $addressTypes: [CompanyAddressType!]!) {\n        companyLocationAssignAddress(locationId: $locationId, address: $address, addressTypes: $addressTypes) {\n          userErrors {\n            code\n            field\n            message\n          }\n        }\n      }": {return: AssignCompanyLocationAddressMutation, variables: AssignCompanyLocationAddressMutationVariables},
  "#graphql\n      mutation DeleteCompanyContact($companyContactId: ID!) {\n        companyContactDelete(companyContactId: $companyContactId) {\n          userErrors {\n            code\n            field\n            message\n          }\n        }\n      }": {return: DeleteCompanyContactMutation, variables: DeleteCompanyContactMutationVariables},
  "#graphql\n      mutation RevokeLocationRoles($locationId: ID!, $revokeAssignments: [ID!]!) {\n        companyLocationRevokeRoles(companyLocationId: $locationId, rolesToRevoke: $revokeAssignments) {\n          revokedRoleAssignmentIds\n          userErrors {\n            code\n            field\n            message\n          }\n        }\n      }": {return: RevokeLocationRolesMutation, variables: RevokeLocationRolesMutationVariables},
  "#graphql\n      mutation AssignLocationRoles($locationId: ID!, $toAssign: [CompanyLocationRoleAssign!]!) {\n        companyLocationAssignRoles(companyLocationId: $locationId, rolesToAssign: $toAssign) {\n          userErrors {\n            code\n            field\n            message\n          }\n        }\n      }": {return: AssignLocationRolesMutation, variables: AssignLocationRolesMutationVariables},
  "#graphql\n      mutation AssignContactToCompany($companyId: ID!, $customerId: ID!) {\n        companyAssignCustomerAsContact(companyId: $companyId, customerId: $customerId) {\n          companyContact {\n            id\n          }\n          userErrors {\n            code\n            field\n            message\n          }\n        }\n      }": {return: AssignContactToCompanyMutation, variables: AssignContactToCompanyMutationVariables},
  "#graphql\n      mutation SendContactEmail($companyContactId: ID!, $email: EmailInput) {\n        companyContactSendWelcomeEmail(companyContactId: $companyContactId, email: $email) {\n          userErrors {\n            code\n            field\n            message\n          }\n        }\n      }": {return: SendContactEmailMutation, variables: SendContactEmailMutationVariables},
  "#graphql\n      mutation CreateCustomer($input: CustomerInput!) {\n        customerCreate(input: $input) {\n          customer {\n            id\n          }\n          userErrors {\n            field\n            message\n          }\n        }\n      }": {return: CreateCustomerMutation, variables: CreateCustomerMutationVariables},
  "#graphql\n      mutation UpdateCustomerInfo($input: CustomerInput!) {\n        customerUpdate(input: $input) {\n          userErrors {\n            field\n            message\n          }\n        }\n      }": {return: UpdateCustomerInfoMutation, variables: UpdateCustomerInfoMutationVariables},
  "#graphql\n      mutation UpdateCustomerAddresses($customerId: ID!,$addresses: [MailingAddressInput!]!) {\n        customerUpdate(input: {\n          id: $customerId\n          addresses: $addresses\n        }) {\n          customer {\n            addresses {\n              id\n            }\n          }\n          userErrors {\n            field\n            message\n          }\n        }\n      }": {return: UpdateCustomerAddressesMutation, variables: UpdateCustomerAddressesMutationVariables},
  "#graphql\n      mutation UpdateCustomerDefaultAddress($customerId: ID!, $addressID: ID!) {\n        customerUpdateDefaultAddress(customerId: $customerId, addressId: $addressID) {\n          userErrors {\n            field\n            message\n          }\n        }\n      }": {return: UpdateCustomerDefaultAddressMutation, variables: UpdateCustomerDefaultAddressMutationVariables},
  "#graphql\n            mutation SetMetafields($metafields: [MetafieldsSetInput!]!) {\n                metafieldsSet(metafields: $metafields) {\n                    metafields {\n                        id\n                        value\n                    }\n                    userErrors {\n                        code\n                        elementIndex\n                        field\n                        message\n                    }\n                }\n            }\n        ": {return: SetMetafieldsMutation, variables: SetMetafieldsMutationVariables},
  "#graphql\n      mutation DeleteMetafields($identifiers: [MetafieldIdentifierInput!]!) {\n        metafieldsDelete(metafields: $identifiers) {\n          deletedMetafields {\n            key\n            namespace\n            ownerId\n          }\n          userErrors {\n            field\n            message\n          }\n        }\n      }\n    ": {return: DeleteMetafieldsMutation, variables: DeleteMetafieldsMutationVariables},
  "#graphql\n        mutation UpdateDeviceHandleMFD($definition: MetafieldDefinitionUpdateInput!) {\n          metafieldDefinitionUpdate(definition: $definition) {\n            updatedDefinition {\n              id\n            },\n            userErrors {\n              code\n              elementIndex\n              field\n              message\n            }\n          }\n        }\n      ": {return: UpdateDeviceHandleMFDMutation, variables: UpdateDeviceHandleMFDMutationVariables},
  "#graphql\n        mutation CreateDeviceHandleMFD($definition: MetafieldDefinitionInput!) {\n          metafieldDefinitionCreate(definition: $definition) {\n            createdDefinition {\n              id\n            }\n            userErrors {\n              code\n              elementIndex\n              field\n              message\n            }\n          }\n        }": {return: CreateDeviceHandleMFDMutation, variables: CreateDeviceHandleMFDMutationVariables},
  "#graphql\n        mutation AddTag($ownerId: ID!, $tags: [String!]!) {\n            tagsAdd(id: $ownerId, tags: $tags) {\n                userErrors {\n                    field\n                    message\n                }\n            }\n        }": {return: AddTagMutation, variables: AddTagMutationVariables},
  "#graphql\n        mutation RemoveTag($ownerId: ID!, $tags: [String!]!) {\n            tagsRemove(id: $ownerId, tags: $tags) {\n                userErrors {\n                    field\n                    message\n                }\n            }\n        }": {return: RemoveTagMutation, variables: RemoveTagMutationVariables},
  "#graphql\n      mutation TriggerFlow($handle: String!, $payload: JSON) {\n        flowTriggerReceive(handle: $handle, payload: $payload) {\n          userErrors {\n            field\n            message\n          }\n        }\n      }": {return: TriggerFlowMutation, variables: TriggerFlowMutationVariables},
}
declare module '@shopify/admin-api-client' {
  type InputMaybe<T> = AdminTypes.InputMaybe<T>;
  interface AdminQueries extends GeneratedQueryTypes {}
  interface AdminMutations extends GeneratedMutationTypes {}
}
