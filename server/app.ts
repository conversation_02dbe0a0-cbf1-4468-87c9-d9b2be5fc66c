// Enable tSyringe
import '@abraham/reflection';
import 'react-router';
import { createRequestHandler } from '@react-router/express';
import express from 'express';
import { createStorefrontClient, InMemoryCache } from '@shopify/hydrogen';
import { createCookieSessionStorage } from 'react-router';
import { storefrontRedirect } from '@shopify/hydrogen';
import crypto from 'node:crypto';
import { createAppLoadContext } from '@/lib/context';
import { SessionClient } from '@/business/clients/session-client';
import { StorefrontClient } from '@/business/clients/storefront-client';
import { CustomerAccountClient } from '@/business/clients/accounts-client';

declare module 'react-router' {
  interface AppLoadContext {
    session: SessionClient;
    storefront: StorefrontClient;
    customerAccount: CustomerAccountClient;
    env: Env;
  }
}

export const app = express();

app.use(
  createRequestHandler({
    build: () => import('virtual:react-router/server-build'),
    getLoadContext: async (req) => {
      const context = await getContext(req);
      return context;
    },
  }),
);

async function getContext(req: express.Request) {
  const env = process.env as any as Env;
  
  if (!env?.SESSION_SECRET) {
    throw new Error('SESSION_SECRET environment variable is not set');
  }

  // Create a mock execution context for Node.js
  const executionContext = {
    waitUntil: () => {},
    passThroughOnException: () => {},
  } as ExecutionContext;

  // Convert Express request to Web API Request
  const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
  const headers = new Headers();
  Object.entries(req.headers).forEach(([key, value]) => {
    if (typeof value === 'string') {
      headers.set(key, value);
    } else if (Array.isArray(value)) {
      value.forEach(v => headers.append(key, v));
    }
  });

  const request = new Request(url, {
    method: req.method,
    headers,
    body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined,
  });

  const context = await createAppLoadContext(request, env, executionContext);
  const session = context.resolve(SessionClient);
  const storefront = context.resolve(StorefrontClient);
  const customerAccount = context.resolve(CustomerAccountClient);

  return {
    session,
    storefront,
    customerAccount,
    env,
  };
}
