{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yaml]": {"editor.formatOnSave": true}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "cSpell.words": ["METAFIELD", "multipass", "Revalidator", "Sendgrid", "tailwindcss"], "css.customData": [".vscode/tailwind.json"], "editor.wordWrap": "wordWrapColumn", "editor.wrappingIndent": "same", "editor.wordWrapColumn": 120, "typescript.tsdk": "node_modules/typescript/lib", "graphql-config.load.filePath": ".graphqlrc.ts"}