# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "27d9dff9f37b6aa012cb3014caed08a3"
name = "storefront-prod"
handle = "storefront-prod"
application_url = "https://h2-prod-a12f47db3018f82aae88.o2.myshopify.dev"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "all-aware-sandbox-dev-jm.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_discounts,write_customers,write_companies,write_products,write_orders"

[auth]
redirect_urls = [
  "https://h2-prod-a12f47db3018f82aae88.o2.myshopify.dev/auth/callback"
]

[webhooks]
api_version = "2024-10"

[pos]
embedded = false
