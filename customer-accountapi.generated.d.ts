/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
import type * as CustomerAccountAPI from '@shopify/hydrogen/customer-account-api-types';

export type GetCustomerAccountIdentifierQueryVariables = CustomerAccountAPI.Exact<{ [key: string]: never }>;

export type GetCustomerAccountIdentifierQuery = {
  customer: Pick<CustomerAccountAPI.Customer, 'id'> & {
    emailAddress?: CustomerAccountAPI.Maybe<Pick<CustomerAccountAPI.CustomerEmailAddress, 'emailAddress'>>;
  };
};

export type GetOrderTrackingInfoQueryVariables = CustomerAccountAPI.Exact<{
  orderId: CustomerAccountAPI.Scalars['ID']['input'];
}>;

export type GetOrderTrackingInfoQuery = {
  order?: CustomerAccountAPI.Maybe<{
    fulfillments: {
      nodes: Array<{
        trackingInformation: Array<Pick<CustomerAccountAPI.TrackingInformation, 'company' | 'number' | 'url'>>;
      }>;
    };
  }>;
};

interface GeneratedQueryTypes {
  '#graphql\n  query GetCustomerAccountIdentifier {\n    customer {\n      id\n      emailAddress {\n        emailAddress\n      }\n    }\n  }\n': {
    return: GetCustomerAccountIdentifierQuery;
    variables: GetCustomerAccountIdentifierQueryVariables;
  };
  '#graphql\n  query GetOrderTrackingInfo($orderId: ID!) {\n    order(id: $orderId) {\n      fulfillments (first: 1) {\n        nodes {\n          trackingInformation {\n            company,\n            number,\n            url\n          }\n        }\n      }\n    }\n  }\n': {
    return: GetOrderTrackingInfoQuery;
    variables: GetOrderTrackingInfoQueryVariables;
  };
}

interface GeneratedMutationTypes {}

declare module '@shopify/hydrogen' {
  interface CustomerAccountQueries extends GeneratedQueryTypes {}
  interface CustomerAccountMutations extends GeneratedMutationTypes {}
}
