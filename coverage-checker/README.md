# Coverage Checker

## How to Generate ZIP Bounds CSV

A ZIP Bounds CSV is used as a condensed version of the raw .shp file with only the relevant information. Shapefiles are .gitignore-d since they would take up too much space on bitbucket.

1. Download latest shapefiles from **https://www.census.gov/geographies/mapping-files.html**
2. Extract `.zip` file and place in `/data` folder.

   - _The default behavior is to find the first shp file and use that so make sure there is only one shp dataset_

3. Run the command `npm run shptocsv` in the `crons/coverage-checker` directory.

   - _`zip-bounds.csv` will be generated in `/data` folder._
   - Use `--csv` to specify the output csv path (defaults to `data/zip-bounds.csv`).

## How to Check Coverage

Make sure you have a `.env` file in the root directory `coverage-checker` with the following variables:

```
SHOPIFY_API_KEY=<your app api key>
SHOPIFY_API_SECRET=<your app api secret>
HOST=<your app host> (e.g. https://all-aware-testing.myshopify.com)
SCOPES=read_metafields,write_metafields
SHOPIFY_ACCESS_TOKEN=<your app access token> (get this from the app in the shopify admin)
```

Run the command `npm run start`.
