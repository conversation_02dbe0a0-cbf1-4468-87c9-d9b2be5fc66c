import { <PERSON><PERSON><PERSON>, Position } from 'geo<PERSON><PERSON>';
import <PERSON><PERSON> from 'jimp';
import { <PERSON>xe<PERSON>, <PERSON>xelRange } from './types.js';
import { ATT_COVERED, ATT_UNCOVERED, VZ_COVERED, VZ_UNCOVERED } from './env.js';
import { queryVerizon } from './query/verizon.js';
import { queryAtt } from './query/att.js';

function lerpComponent(from: number, to: number, t: number) {
  return Math.round(Math.max(Math.min(from + (to - from) * t, 255), 0));
}

function lerpPixel(from: Pixel, to: Pixel, t: number) {
  const newPixel: Pixel = [
    lerpComponent(from[0], to[0], t),
    lerpComponent(from[1], to[1], t),
    lerpComponent(from[2], to[2], t),
    lerpComponent(from[3], to[3], t),
  ];
  return newPixel;
}

function drawCircle(image: Jim<PERSON>, x: number, y: number, radius: number, color: Pixel) {
  const feather = 2.0;
  const minX = Math.floor(Math.max(x - radius - feather, 0));
  const minY = Math.floor(Math.max(y - radius - feather, 0));
  const maxX = Math.ceil(Math.min(x + radius + 1 + feather, image.bitmap.width - 1));
  const maxY = Math.ceil(Math.min(y + radius + 1 + feather, image.bitmap.height - 1));
  image.scan(minX, minY, maxX - minX, maxY - minY, (pX, pY, idx) => {
    const pixel = image.bitmap.data.subarray(idx, idx + 4) as unknown as Pixel;
    const dX = pX + 0.5 - x;
    const dY = pY + 0.5 - y;
    const d = Math.sqrt(dX * dX + dY * dY);

    const alpha = Math.max(Math.min((d - radius) / feather, 1), 0);
    const newPixel = lerpPixel(lerpPixel(pixel, color, color[3] / 255), pixel, alpha);
    image.setPixelColor(Jimp.rgbaToInt(newPixel[0], newPixel[1], newPixel[2], newPixel[3]), pX, pY);
  });
}

async function writeImage(image: Jimp, path: string, bbox: BBox, coordinates?: Position[]) {
  if (typeof coordinates != 'undefined') {
    const b2pX = image.bitmap.width / (bbox[2] - bbox[0]);
    const b2pY = image.bitmap.height / (bbox[3] - bbox[1]);
    for await (const pos of coordinates) {
      const pX = (pos[0] - bbox[0]) * b2pX;
      const pY = (pos[1] - bbox[1]) * b2pY;
      drawCircle(image, pX, pY, 0, [0, 255, 0, 200]);
    }
  }
  await image.writeAsync(path);
}

export async function getVerizonCoverage(bbox: BBox, coordinates?: Position[]) {
  const image = await queryVerizon(bbox);
  const coverage = computeCoverageFromImage(image, VZ_COVERED, VZ_UNCOVERED);

  await writeImage(image, 'vz.png', bbox, coordinates);

  return coverage;
}

export async function getAttCoverage(bbox: BBox, coordinates?: Position[]) {
  const image = await queryAtt(bbox);
  const coverage = computeCoverageFromImage(image, ATT_COVERED, ATT_UNCOVERED);

  await writeImage(image, 'att.png', bbox, coordinates);

  return coverage;
}

function pixelInRange(pixel: Pixel, { min, max }: PixelRange) {
  return (
    min[0] <= pixel[0] &&
    max[0] >= pixel[0] &&
    min[1] <= pixel[1] &&
    max[1] >= pixel[1] &&
    min[2] <= pixel[2] &&
    max[2] >= pixel[2] &&
    min[3] <= pixel[3] &&
    max[3] >= pixel[3]
  );
}

export function computeCoverageFromImage(image: Jimp, coveredRange: PixelRange, uncoveredRange: PixelRange) {
  let coveredCount = 0;
  let uncoveredCount = 0;
  image.scan(0, 0, image.bitmap.width, image.bitmap.height, (x, y, idx) => {
    const pixel = image.bitmap.data.subarray(idx, idx + 4) as unknown as Pixel;

    if (pixelInRange(pixel, coveredRange)) {
      coveredCount++;
    } else if (pixelInRange(pixel, uncoveredRange)) {
      uncoveredCount++;
    }
  });

  const total = coveredCount + uncoveredCount;
  if (total == 0) {
    console.warn(
      "Image had no covered or uncovered region... That's extremely unlikely and indicates that either the covered or uncovered range is incorrect!",
    );
    return 0.0;
  } else {
    return coveredCount / total;
  }
}

export enum NetworkCoverageLevel {
  Error = -1,
  NoCoverage = 0,
  PartiallyCovered = 1,
  MostlyCovered = 2,
  FullyCovered = 3,
}

export async function getCoverage(
  bbox: BBox,
  coordinates?: Position[],
): Promise<{ carrier: 'verizon' | 'att' | null; coverage: number }> {
  let coverageLevel = await getVerizonCoverage(bbox, coordinates);
  let coverage = NetworkCoverageLevel.Error;
  let carrier: 'verizon' | 'att' = 'verizon';

  if (coverageLevel < 0.25) {
    carrier = 'att';
    coverageLevel = await getAttCoverage(bbox, coordinates);
  }

  if (coverageLevel >= 0.75) {
    coverage = NetworkCoverageLevel.FullyCovered;
  } else if (coverageLevel >= 0.5) {
    coverage = NetworkCoverageLevel.MostlyCovered;
  } else if (coverageLevel >= 0.25) {
    coverage = NetworkCoverageLevel.PartiallyCovered;
  } else {
    coverage = NetworkCoverageLevel.NoCoverage;
  }

  return { carrier: coverage === NetworkCoverageLevel.NoCoverage ? null : carrier, coverage };
}
