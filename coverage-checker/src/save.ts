import '@shopify/shopify-api/adapters/node';
import { shopify, session } from './shopify.js';

const client = new shopify.clients.Graphql({ session });

async function getShopId(): Promise<string> {
  const shopResponse = await client.request(`
    query GetShopID {
      shop {
        id
      }
    }
  `);
  return shopResponse.data.shop.id;
}

export async function saveMetafields(metafieldData: Array<{ key: string; value: any }>): Promise<void> {
  const shopId = await getShopId();
  const metafieldInputs = metafieldData.map(item => ({
    namespace: 'coverage',
    key: item.key,
    value: JSON.stringify(item.value),
    type: 'json',
    ownerId: shopId,
  }));

  const res = await client.request(
    `
    mutation CreateMetafields($inputs: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $inputs) {
        metafields {
          id
          key
        }
        userErrors {
          field
          message
        }
      }
    }
    `,
    {
      variables: {
        inputs: metafieldInputs,
      },
    },
  );

  const hasErrors = res.data.metafieldsSet.userErrors.length > 0;
  if (hasErrors) {
    console.log('Request failed with errors:', res.data.metafieldsSet.userErrors);
    throw new Error('Failed to save metafields: ' + JSON.stringify(res.data.metafieldsSet.userErrors));
  } else {
    console.log('Request succeeded!');
  }
}
