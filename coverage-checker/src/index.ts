import '@shopify/shopify-api/adapters/node';
import { BBox } from 'geojson';
import * as fs from 'fs';
import { readFile } from 'fs/promises';
import { getCoverage } from './coverage.js';
import { saveMetafields } from './save.js';

/**
 * Checks if there was a previously crashed process and returns the last processed batch
 * @param currentBatchPath Path to the file containing the last processed batch
 * @returns The last processed batch index or undefined if no crashed process was found
 */
async function checkForCrashedProcess(currentBatchPath: string): Promise<number | undefined> {
  let lastProcessedBatch: number | undefined;
  if (fs.existsSync(currentBatchPath)) {
    const batchData = (await readFile(currentBatchPath, 'utf-8')).trim();
    lastProcessedBatch = parseInt(batchData, 10);
    if (isNaN(lastProcessedBatch)) {
      lastProcessedBatch = undefined;
    } else {
      console.log(`Resuming from interrupted process at batch: ${lastProcessedBatch}`);
    }
  }
  return lastProcessedBatch;
}

/**
 * Process a batch of ZIP codes
 * @param zipBatch Array of ZIP data to process
 * @param batchIndex Current batch index for reporting
 * @returns Object with successfully processed ZIPs and failed ZIPs
 */
async function processBatch(
  zipBatch: Array<{ zip: string; bbox: BBox }>,
  batchIndex: number,
): Promise<{ successful: string[]; failed: Array<{ zip: string; bbox: BBox }> }> {
  const successful: string[] = [];
  const failed: Array<{ zip: string; bbox: BBox }> = [];
  const batchCoverage: Record<string, any> = {};

  // Process each ZIP in the batch and collect coverage data
  for (const zipData of zipBatch) {
    try {
      const coverage = await getCoverage(zipData.bbox);
      batchCoverage[zipData.zip] = coverage;
      successful.push(zipData.zip);
    } catch (error) {
      console.error(`Error processing ZIP ${zipData.zip}:`, error);
      failed.push(zipData);
    }
  }

  if (failed.length === 0) {
    // Convert batchCoverage object to the array format expected by saveMetafields
    const metafieldData = Object.entries(batchCoverage).map(([zip, coverage]) => ({
      key: zip,
      value: coverage,
    }));

    await saveMetafields(metafieldData);
    console.log(`Saved coverage data for batch ${batchIndex} to Shopify`);
  }

  return { successful, failed };
}

export async function getCoverageForZipCodes(): Promise<void> {
  const csvData = await readFile('data/zip-bounds.csv', 'utf-8');
  const lines = csvData.split('\n').slice(1);
  const currentBatchPath = 'data/current-batch.txt';
  const BATCH_SIZE = 25; // 25 is max for Shopify

  // Parse all valid ZIP codes from CSV
  const allZips: Array<{ zip: string; bbox: BBox }> = [];
  for (const line of lines) {
    const data = line.trim().split(',');
    if (data.length === 1) continue; // Empty line

    const zip = data[0];
    const bbox: BBox = [
      Number.parseFloat(data[1]),
      Number.parseFloat(data[2]),
      Number.parseFloat(data[3]),
      Number.parseFloat(data[4]),
    ];

    if (bbox.some(x => Number.isNaN(x) || !Number.isFinite(x))) {
      throw new Error(`Zip #${zip} has an invalid bounding box!`);
    }

    allZips.push({ zip, bbox });
  }

  // Split into batches
  const batches: Array<Array<{ zip: string; bbox: BBox }>> = [];
  for (let i = 0; i < allZips.length; i += BATCH_SIZE) {
    batches.push(allZips.slice(i, i + BATCH_SIZE));
  }

  console.log(`Total ZIP codes: ${allZips.length}`);
  console.log(`Total batches: ${batches.length} (batch size: ${BATCH_SIZE})`);

  // Check if there's a crashed process to resume from
  const lastProcessedBatch = await checkForCrashedProcess(currentBatchPath);
  const startBatch = lastProcessedBatch !== undefined ? lastProcessedBatch : 0;

  try {
    for (let batchIndex = startBatch; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} ZIP codes)`);

      // Update current batch file before processing
      await fs.promises.writeFile(currentBatchPath, batchIndex.toString());

      // Process the batch
      const { successful, failed } = await processBatch(batch, batchIndex);

      console.log(`Batch ${batchIndex + 1} results: ${successful.length} successful, ${failed.length} failed`);

      // Fail the entire batch if any ZIP codes failed
      if (failed.length > 0) {
        console.error(`Batch ${batchIndex + 1} failed with ${failed.length} failed ZIP codes.`);
        process.exit(1);
      }
    }

    // Clean up the current-batch file after successful completion
    await fs.promises.unlink(currentBatchPath);
    console.log('Processing complete. All batches processed successfully.');

    process.exit(0);
  } catch (error) {
    console.error('Error processing ZIP batches:', error);
    process.exit(1);
  }
}

getCoverageForZipCodes();
