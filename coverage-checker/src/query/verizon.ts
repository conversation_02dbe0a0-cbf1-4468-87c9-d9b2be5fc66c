import { B<PERSON><PERSON> } from 'geo<PERSON><PERSON>';
import <PERSON><PERSON> from 'jimp';
import { MAP_RESOLUTION, VZ_STYLE, VZ_TOKEN } from '../env.js';

export async function queryVerizon(bbox: BBox) {
  const url = `https://api.mapbox.com/styles/v1/gismaps/${VZ_STYLE}/static/[${bbox[0]},${bbox[1]},${bbox[2]},${bbox[3]}]/${MAP_RESOLUTION}x${MAP_RESOLUTION}?logo=false&attribution=false&access_token=${VZ_TOKEN}`;

  try {
    const image = await Jim<PERSON>.read({
      url,
      headers: { Referer: 'https://gismaps.verizon.com/' },
    } as any);

    return image;
  } catch (e) {
    if (!(e as Error).message.startsWith('HTTP Status 500')) {
      //We can occasionally get status 500 errors from VZ so we will handel them differently
      throw e;
    }
  }

  const x = (bbox[0] + bbox[2]) * 0.5;
  const y = (bbox[1] + bbox[3]) * 0.5;

  //Flip-flop back and forth around the value 12 to try different zoom levels that might work.
  for (const Z of [12, 11, 13, 10, 14, 9, 15, 8]) {
    const fallbackUrl = `https://api.mapbox.com/styles/v1/gismaps/${VZ_STYLE}/static/${x},${y},${Z}/${MAP_RESOLUTION}x${MAP_RESOLUTION}?logo=false&attribution=false&access_token=${VZ_TOKEN}`;

    try {
      const image = await Jimp.read({
        url: fallbackUrl,
        headers: { Referer: 'https://gismaps.verizon.com/' },
      } as any);

      return image;
    } catch (e) {
      if (!(e as Error).message.startsWith('HTTP Status 500')) {
        //Again! We can occasionally get status 500 errors from VZ so we will handel them differently
        throw e;
      }
    }
  }
  throw Error(`Could not get Verizon coverage for bbox ${bbox}`);
}
