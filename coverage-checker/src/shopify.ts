import '@shopify/shopify-api/adapters/node';
import { HOST, SHOPIFY_ACCESS_TOKEN, SHOPIFY_API_KEY, SHOPIFY_API_SECRET } from './env.js';
import { ApiVersion, Session, shopifyApi } from '@shopify/shopify-api';
import { restResources } from '@shopify/shopify-api/rest/admin/2024-04';

export const shopify = shopifyApi({
  apiKey: SHOPIFY_API_KEY!,
  apiSecretKey: SHOPIFY_API_SECRET!,
  hostName: HOST!.replace(/https:\/\//, ''),
  isEmbeddedApp: true,
  apiVersion: ApiVersion.April24,
  restResources,
});

const shop = new URL(HOST!).hostname;

export const session = new Session({
  id: `offline_${shop}`,
  shop,
  state: 'authenticated',
  isOnline: false,
  accessToken: SHOPIFY_ACCESS_TOKEN!,
});
