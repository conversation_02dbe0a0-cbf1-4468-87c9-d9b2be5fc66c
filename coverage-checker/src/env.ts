import dotenv from 'dotenv';
import { Pixel } from './types.js';

dotenv.config();

export const { HOST, SCOPES, SHOPIFY_API_KEY, SHOPIFY_API_SECRET, SHOPIFY_ACCESS_TOKEN } = process.env;

export const MAP_RESOLUTION = 256;

// AT&T Settings
export const ATT_STYLE = 'attDomestic';

// Verizon Settings
export const VZ_TOKEN = 'pk.eyJ1IjoiZ2lzbWFwcyIsImEiOiJjbDZ5NnFld3kwdmZxM2JrMDIydnNxZzcwIn0.xoL82CLteOY9h_H7XmEXpQ';
export const VZ_STYLE = 'ckxgmvvkz0egj14l1mmpotzct';

// Helper function for creating Pixel arrays
function envColorToColor(envColor: string | undefined): Pixel {
  if (typeof envColor == 'undefined') {
    return [0, 0, 0, 0];
  }
  const components = envColor.split(',');
  if (components.length != 4) {
    throw new Error(`Env color doesn't have the correct number of components.
        Expected 4 got ${components.length}`);
  }

  const pixel: Pixel = [0, 0, 0, 0];

  for (let i = 0; i < 4; i++) {
    pixel[i] = Number.parseInt(components[i]);
  }

  return pixel;
}

// AT&T Coverage Colors
export const ATT_COVERED = {
  min: envColorToColor('0,0,130,220'),
  max: envColorToColor('150,240,255,255'),
};

export const ATT_UNCOVERED = {
  min: envColorToColor('0,0,0,0'),
  max: envColorToColor('255,255,255,5'),
};

// Verizon Coverage Colors
export const VZ_COVERED = {
  min: envColorToColor('180,0,0,220'),
  max: envColorToColor('246,210,215,255'),
};

export const VZ_UNCOVERED = {
  min: envColorToColor('246,246,246,255'),
  max: envColorToColor('246,246,246,255'),
};
