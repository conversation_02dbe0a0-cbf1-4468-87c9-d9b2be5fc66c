import { session, shopify } from './shopify.js';

async function deleteAllCoverageMetafields(): Promise<void> {
  try {
    let hasNextPage = true;
    let cursor: string | null = null;

    const client = new shopify.clients.Graphql({
      session,
    });
    while (hasNextPage) {
      const response: any = await client.request(
        `
            query GetMetafields($cursor: String) {
              shop {
                metafields(first: 100, after: $cursor, namespace: "coverage") {
                  edges {
                    node {
                      id
                      key
                    }
                  }
                  pageInfo {
                    hasNextPage
                    endCursor
                  }
                }
              }
            }
          `,
        {
          variables: { cursor },
        },
      );

      const data = response?.data?.shop?.metafields;
      const pageMetafields = data.edges.map((edge: any) => edge.node);

      const deletePromises = pageMetafields.map((metafield: any) =>
        client.request(
          `
              mutation DeleteMetafield($id: ID!) {
                metafieldDelete(input: { id: $id }) {
                  deletedId
                  userErrors {
                    field
                    message
                  }
                }
              }
            `,
          {
            variables: { id: metafield.id },
          },
        ),
      );

      await Promise.all(deletePromises);
      console.log(`Deleted ${pageMetafields.length} metafields from current page`);

      hasNextPage = data.pageInfo.hasNextPage;
      cursor = data.pageInfo.endCursor;
    }

    console.log('Successfully deleted all coverage metafields');
    process.exit(0);
  } catch (error) {
    console.error('Error deleting coverage metafields:', error);
    process.exit(1);
  }
}

deleteAllCoverageMetafields();
