import { BBox, Position } from 'geoj<PERSON>';

export type Pixel = [number, number, number, number];
export type PixelRange = { min: Pixel; max: Pixel };

export type ProcessZipBounds = (
  zip: string,
  bbox: BBox,
  coordinates?: Position[],
) => Promise<boolean | undefined | void>;

export type ZipBoundsProcessor = (path: string, process: ProcessZipBounds) => Promise<void>;

export interface CoverageBase {
  zip?: string;
  queriedAt?: Date;
}

export type Coverage = CoverageBase & ({ att: number } | { verizon: number });
