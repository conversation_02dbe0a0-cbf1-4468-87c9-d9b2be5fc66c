{"name": "coverage-checker-cron", "version": "1.0.0", "description": "", "main": "src/index.ts", "type": "module", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "prestart": "npm run build", "shptocsv": "ts-node . --shptocsv", "start": "node ./dist", "start:dev": "ts-node .", "delete": "node ./dist/delete.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20.8.10", "@types/shapefile": "^0.6.3", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"@shopify/app": "^3.58.2", "@shopify/cli": "^3.76.0", "@shopify/shopify-api": "^11.9.0", "csv": "^6.3.5", "dotenv": "^16.3.1", "geojson": "^0.5.0", "glob": "^10.3.10", "jimp": "^0.22.10"}}