import type { IGraphQLConfig } from 'graphql-config';
import { getSchema } from '@shopify/hydrogen-codegen';
import fs from 'fs';
//WARNING: Be careful! If you import something from app folder
// you must use relative paths! TS paths do not work.
// Hydrogen codegen will silently fail!!!
import { ADMIN_API_VERSION, STOREFRONT_API_VERSION } from './app/business/core/constants/config';
import { ApiType, shopifyApiProject } from '@shopify/api-codegen-preset';

console.log('Generating Admin GQL project...');

const admin = shopifyApiProject({
  apiType: ApiType.Admin,
  apiVersion: ADMIN_API_VERSION,
  documents: ['./app/business/core/**/*.{ts,tsx,js,jsx}'],
  declarations: false,
});

console.log('Generating Storefront GQL project...');

const storefront = shopifyApiProject({
  apiType: ApiType.Storefront,
  apiVersion: STOREFRONT_API_VERSION,
  documents: [
    './app/**/*.{ts,tsx,js,jsx}',
    '!./app/business/core/**/*.{ts,tsx,js,jsx}',
    '!./app/business/customer-account/**/*.{ts,tsx,js,jsx}',
  ],
  declarations: false,
  module: '@shopify/hydrogen',
});

/**
 * NECESSARY!...!! (this took 4 hours to solve ;(... KNOW MY PAIN!!!)
 * For some reason TitleCase is enforced on all the queries,
 * but not when they are aggregated together inGeneratedQueryTypes and GeneratedMutationTypes.
 * This causes types to be incorrectly inferred. To fix this I manually disable naming convention changes.
 */
//@ts-ignore
admin.extensions.codegen.generates['./admin.generated.ts'].config = { namingConvention: 'keep' };
//@ts-ignore
admin.extensions.codegen.generates['./admin.types.ts'].config = { namingConvention: 'keep' };
//@ts-ignore
storefront.extensions.codegen.generates['./storefront.generated.ts'].config = { namingConvention: 'keep' };
//@ts-ignore
storefront.extensions.codegen.generates['./storefront.types.ts'].config = { namingConvention: 'keep' };

function getSchemaExtensions() {
  console.log('Finding extension schemas...');
  let extensions: string[] = [];
  try {
    extensions = fs.readdirSync('./extensions');
  } catch {
    // ignore if no extensions
  }

  const projects: any = {};

  for (const entry of extensions) {
    const extensionPath = `./extensions/${entry}`;
    const schema = `${extensionPath}/schema.graphql`;
    if (!fs.existsSync(schema)) {
      continue;
    }
    projects[entry] = {
      schema,
      documents: [`${extensionPath}/**/*.graphql`],
    };
    console.log('Found', entry, 'extension schema');
  }
  console.log('Found', Object.keys(projects).length, 'extension schemas');
  return projects;
}

/**
 * GraphQL Config
 * @see https://the-guild.dev/graphql/config/docs/user/usage
 * @type {IGraphQLConfig}
 */
export default {
  projects: {
    //Should be empty, but we need to include the storefront schema to avoid errors
    default: {
      schema: getSchema('storefront'),
      documents: [],
    },
    // ...getSchemaExtensions(),
    customer: {
      schema: getSchema('customer-account'),
      documents: ['./app/business/customer-account/*.{ts,tsx,js,jsx}'],
    },
    admin,
    storefront,
  },
} as IGraphQLConfig;
