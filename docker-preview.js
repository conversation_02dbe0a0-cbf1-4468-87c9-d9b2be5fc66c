#!/usr/bin/env node

const { spawn } = require('child_process');
const process = require('process');
const fs = require('fs');
const path = require('path');

// Set default environment variables if not provided
process.env.NODE_ENV = process.env.NODE_ENV || 'production';
process.env.PORT = process.env.PORT || '3000';
process.env.HOST = process.env.HOST || '0.0.0.0';

console.log('Starting Shopify Hydrogen preview server...');
console.log(`Environment: ${process.env.NODE_ENV}`);
console.log(`Host: ${process.env.HOST}`);
console.log(`Port: ${process.env.PORT}`);
console.log(`Working directory: ${process.cwd()}`);
console.log(`Node version: ${process.version}`);

// Check if build directory exists
const buildDir = path.join(process.cwd(), 'dist');
if (!fs.existsSync(buildDir)) {
  console.error('Build directory not found. Make sure npm run build has been executed.');
  process.exit(1);
}

// Start the preview server with additional flags for Docker compatibility
// Try legacy runtime first as it might be more stable in Docker
const preview = spawn(
  'npx',
  [
    'shopify',
    'hydrogen',
    'preview',
    '--host',
    process.env.HOST,
    '--port',
    process.env.PORT,
    '--no-open', // Don't try to open browser in Docker
    '--legacy-runtime', // Use legacy Node.js runtime for better Docker compatibility
  ],
  {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: process.env,
    detached: false,
  },
);

// Forward stdout and stderr with timestamps
preview.stdout.on('data', data => {
  const timestamp = new Date().toISOString();
  const lines = data
    .toString()
    .split('\n')
    .filter(line => line.trim());
  lines.forEach(line => {
    console.log(`[${timestamp}] ${line}`);
  });

  // Clear timeout when server starts successfully
  if (data.toString().includes('Local:') || data.toString().includes('listening')) {
    clearTimeout(startupTimeout);
  }
});

preview.stderr.on('data', data => {
  const timestamp = new Date().toISOString();
  const lines = data
    .toString()
    .split('\n')
    .filter(line => line.trim());
  lines.forEach(line => {
    console.error(`[${timestamp}] ERROR: ${line}`);
  });
});

// Add a startup timeout
const startupTimeout = setTimeout(() => {
  console.error('Preview server failed to start within 60 seconds, falling back to simple server...');
  preview.kill('SIGTERM');

  // Start fallback server
  setTimeout(() => {
    console.log('Starting fallback server...');
    const fallback = spawn('node', ['docker-server.js'], {
      stdio: 'inherit',
      env: process.env,
    });

    fallback.on('error', error => {
      console.error('Fallback server failed:', error);
      process.exit(1);
    });
  }, 1000);
}, 60000);

// Handle process signals
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  clearTimeout(startupTimeout);
  preview.kill('SIGTERM');
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  clearTimeout(startupTimeout);
  preview.kill('SIGINT');
});

// Handle preview process events
preview.on('error', error => {
  console.error('Failed to start preview server:', error);
  clearTimeout(startupTimeout);

  // If it's an EPIPE error, start fallback server
  if (error.code === 'EPIPE' || error.message.includes('EPIPE')) {
    console.log('EPIPE error detected, starting fallback server...');
    setTimeout(() => {
      const fallback = spawn('node', ['docker-server.js'], {
        stdio: 'inherit',
        env: process.env,
      });

      fallback.on('error', fallbackError => {
        console.error('Fallback server failed:', fallbackError);
        process.exit(1);
      });
    }, 1000);
  } else {
    process.exit(1);
  }
});

preview.on('close', (code, signal) => {
  clearTimeout(startupTimeout);
  if (signal) {
    console.log(`Preview server terminated by signal: ${signal}`);
  } else {
    console.log(`Preview server exited with code: ${code}`);
  }

  // If the server exited with an error code, try fallback
  if (code && code !== 0) {
    console.log('Preview server failed, starting fallback server...');
    setTimeout(() => {
      const fallback = spawn('node', ['docker-server.js'], {
        stdio: 'inherit',
        env: process.env,
      });

      fallback.on('error', fallbackError => {
        console.error('Fallback server failed:', fallbackError);
        process.exit(1);
      });
    }, 1000);
  } else {
    process.exit(code || 0);
  }
});

preview.on('exit', (code, signal) => {
  clearTimeout(startupTimeout);
  if (signal) {
    console.log(`Preview server killed by signal: ${signal}`);
  } else {
    console.log(`Preview server exited with code: ${code}`);
  }

  // Only exit if it's a clean shutdown
  if (!code || code === 0) {
    process.exit(code || 0);
  }
});
