# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "43463a9cab092c99811f828dc92413d6"
name = "storefront-dev-cs"
handle = "storefront-dev-cs"
application_url = "https://allaware-hydrogen-cs.ngrok.io"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "allaware-sandbox.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_discounts,write_customers,write_companies,write_products,write_orders"

[auth]
redirect_urls = [
  "https://allaware-hydrogen-cs.ngrok.io/auth/callback",
  "https://allaware-hydrogen-cs.ngrok.io/auth/shopify/callback",
  "https://allaware-hydrogen-cs.ngrok.io/api/auth/callback"
]

[webhooks]
api_version = "2024-10"

  [[webhooks.subscriptions]]
  compliance_topics = [ "customers/data_request" ]
  uri = "/webhooks/customers/data_request"

  [[webhooks.subscriptions]]
  compliance_topics = [ "customers/redact" ]
  uri = "/webhooks/customers/redact"

  [[webhooks.subscriptions]]
  compliance_topics = [ "shop/redact" ]
  uri = "/webhooks/shop/redact"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

[pos]
embedded = false
