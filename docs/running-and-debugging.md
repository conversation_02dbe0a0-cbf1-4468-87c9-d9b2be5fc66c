# Running and Debugging

This project includes some convenient configurations for using the built in VSCode debugger.

Click on the debugger 🪲 tab in the VSCode sidebar. There you should see 3 options in the RUN AND DEBUG dropdown:

1. Run - simply runs the dev server (`npm run dev`)
2. Debug - attaches a debugger to a run
3. Run/Debug - runs and attaches debugger to that run in just one click. What will probably always want to use most of the time.
