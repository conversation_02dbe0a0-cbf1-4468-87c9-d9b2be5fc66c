# Localization with i18n

The main purpose of using localization in this project and at this time is to keep all of our copy strings in one place. We currently do not target non english speaking markets and therefore the current setup is purposefully lightweight.

## Resources

Although i18n recommends the approach of grouping translations together like so:

```bash
common.json -> Things that are reused everywhere, eg. Button labels 'save', 'cancel'
validation.json -> All validation texts
glossary.json -> Words we want to be reused consistently inside texts
```

We are going to take another common approach of defining namespaces for each page and one shared namespace called `root` which, as you guest it, is used in the for root translations related to root.tsx.

We can however keep the idea of a validation (form errors etc...) namespace if necessary.

### When do I put my strings in `root`?

Anytime it doesn't fit into a normal "page" context. For example and primarily for the Layout component which is used on all pages. Copy in the header or in the footer or in the side cart would be defined in here.

### What if several pages have the same repeating text?

For example, several pages might have an "Save" button. We purposefully want each page to have manage it's own copy within a namespace. This facilitates scenarios where A/B testing might determine that on one page it should say "Save" and another it should say "Save Info".

**All translations should be defined in the `app/locales/en.json` file.**

To speed up page loads, you can load only a specific namespace by adding `i18n` property to a `handle` export. For example this will only load the home namespace:

```js
export const handle = {
  i18n: 'home',
};
```

To use translations in your components:

```js
const { t } = useTranslation();

<p>{t('root:key')}</div>;
```
