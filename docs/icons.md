# Icons

This document explains how svg icons work with the Icon component.

## Overview

We use SVGR to convert our .svg icons into React components that can be lazy loaded.

## Adding a new icon

1. .svg files should be placed in the `/app/icons` directory. _File names should be pascal-case with spaces between words e.g. Arrow Chevron Down._
2. Run `npm run icons` to regenerate icon as a react component.
3. The icon should now be available to select from the `icon` prop on the `Icon` component in kebab-case, e.g. arrow-chevron-down.

## Static Loading

Icons are lazy loaded by default but it may be desirable to statically load them (e.g. a logo that is present on every page).
This can be accomplished by using the Icon's react component from the `/app/components/Icon/icons/<icon name>.tsx` file directly.

Setting the icon's width, height, and color is perfectly legitimate through `style` or `className` props.
