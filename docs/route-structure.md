# Route Folder Structure

This document describes the folder structure for routes to keep our application data fetching logic separate from our UI logic.

## Overview

Let's take the `cart` route as an example. A Remix route exports a UI component which is essentially the content of that route. It also exports an `action` which is what Remix uses for data fetching. At times, the route might also configure some extra html headers like `meta` or `links`. Here is how we will structure our routes in the `/routes` folder:

```bash
app/
└── routes/
    └── cart/
        ├── _cart.action.ts
        ├── _cart.tsx
        └── index.tsx
```

The folder name `cart` is used as the route name (ex: `localhost:3000/cart`).
The `_cart.action` file contains that routes action for data fetching.
The `_cart` file is the actual JSX component.
The `index` file exports any custom headers for that route and exports the action and component and anything else that the route needs.

Notice the `_` in front of `_cart` and `_cart.action` is used to let <PERSON> know that these files are not routes themselves.
