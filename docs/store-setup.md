## Store setup resources

1. Header & Footer menus
   - Header: a menu with the `main-menu` handle.
   - Footer:
     Support menu handle: `support`,
     Policy menu handle: `legal-policies`,
     Social menu handle: `social`,
2. Blogs:
   - Front page news: a blog with the handle of `news`.
   - Use cases: a blog with the handle of `use-cases`. _Articles should be tagged with case-sensitive product names to indicate they are a use case that is relevant to a specific product. e.g. `Flex Aware`._
