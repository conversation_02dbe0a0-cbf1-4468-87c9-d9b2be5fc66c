# Scenario 1: Customer buys on Shopify

## 1. Customer accesses /start link

We always start by finding out where the customer purchased. We pass a few URL params `?order&123`. If there is an order id we know it's a Shopify order and can use the orderId to look up the customer and facilitate the auth flow.

## 2. <PERSON><PERSON> or <PERSON>reate Account

Log user in and based on the provided `channel` param, create the `CustomerWizard` object.

## 3. Customer Wizard

If orders on the customer's account we automatically populate those devices. If purchased on Amazon, we need to have the customer populate them.

```js
const customerWizard = {
    cid: 1,
    steps: [
        congrats: true,
        downloadTheApp: true,
    ],
    devices: [
        {
            type: 'flex',
            channel: 'shopify',
            order: 123,
            subscribed: false,
            completed: false,
            started: false,
            serial: 1234
        },
        {type: 'flex', channel: 'shopify', order: 456, subscribed: true, completed: true, started: true},
        {type: 'camera', channel: 'shopify', order: 456, subscribed: true, completed: true, started: true},
        {type: 'car', channel: 'amazon', subscribed: false, completed: false, started: false}
    ],
    completedShopifyOrders: [456]
}
```

Steps are the initial, non device specific steps. we use these to track where the customer is at.

The "Choose devices" step is the one that user will always be directed too if no devices started and not completed and devices still need to be completed.

The "Choose Account" is shown at the beginning of each device setup step.

Started means the device wizard was started so we should pick back up on the device that is `started` but not `completed`.
Subscribed means the device subscription has been purchased.
Completed means the device has been installed or device wizard completed.

a. Shopify: If customer places a new Shopify order, we automatically update their CustomerWizard.

b. Amazon: If customer places an order on Amazon, we need a way for them to say they add devices within the wizard.

## Start wizard pipeline MVP steps:

### V1:

Only selling on Shopify so the Url will always be an existing customer.

1. should handle when order or customerId is present in URL
2. should handle when accessed from account page

### V2:

Allow multiple platforms (amazon, etc…)

### V3:

add pre-enrollment

To prioritize:
Builder customization of steps (if ever)
