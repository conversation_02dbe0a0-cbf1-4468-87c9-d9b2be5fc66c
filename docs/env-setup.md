# Setting up your dev environment

We use the store `All Aware Development (all-aware-development.myshopify.com)` for all development.

The post install script in package.j<PERSON> will setup the `h2` shortcut for the Shopify CLI for you by running `npx shopify hydrogen shortcut`.

To get started run:

```bash
h2 login
```

Login via the browser then come back to your terminal where you will be able to select the `All Aware Development (all-aware-development.myshopify.com)`.

Now that you are authenticated you must link your local repo with the development store so that you automatically get the latest environment variables:

```bash
h2 link
```

Make sure to select `AA Storefront DEV` and NOT create a new storefront.
