# Builder.io

We use Builder.io as a no-code solution for our non-developers to easily modify select pages on the allaware.com site.

See builder.io documentation at: https://www.builder.io/c/docs/how-builder-works

## Preview dev environment

1. Run storefront locally with port `:3000`
2. Access the builder.io `AllAware` organization
3. Enter the `All Aware DEV` space.
4. Select page or symbol to edit under `Content` section

## Register builder.io for route

1. Call `registerBuilder` function at head of file:

```js
import registerBuilder from '@/common/builder-registry';

registerBuilder();
```

2. Load builder content from builder api in `loader` function

```js
import { builder } from '@builder.io/react';
export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const page = await builder
    .get('page', {
      userAttributes: {
        urlPath: '/' + params['*'],
      },
      locale: params.locale,
    })
    .toPromise();

  const isPreviewing = new URL(request.url).searchParams.has('builder.preview');

  if (!page && !isPreviewing) {
    throw new Response('Page Not Found', {
      status: 404,
      statusText: "Hmmm... This page doesn't seem to exist. Please contact support if you think this is a mistake!",
    });
  }

  return { page };
};
```

3. Mount page content with `<BuilderComponent ... />` component:

```js
export default function Page() {
  const { page } = useLoaderData<typeof loader>();
  return <BuilderComponent model="page" content={page} />;
}
```

## Register a component with builder.io

1. Create component registration object at the bottom of the component file as follows:

```js
export const TYPOGRAPHY_COMPONENT: RegisteredComponent = {
  component: Button
  name: 'All Aware Button',
  inputs: [
    {
      name: 'title',
      required: false,
      type: 'string',
      defaultValue: 'Click here...',
    },
    ...
  ]
}
```

_(tip: reference input types from https://www.builder.io/c/docs/custom-components-input-types)_

2. Register component in /app/common/builder-registry.ts as follows:

```js
export default function registerBuilder() {
  builder.init(PUBLIC_BUILDER_KEY);
  ...
  registerComponent(TYPOGRAPHY_COMPONENT);
  ...
}
```
