/*
Some global styles that you might try to add to tailwind.css in order to override builder.io styles,
don't work, even with !important. Most likely this is caused by some style hierarchy in shadcn.
Placing those styles here works.
*/
.remove-arrow::-webkit-inner-spin-button,
.remove-arrow::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.remove-arrow {
  -moz-appearance: textfield;
}
div.builder-text > ul,
div.builder-text > ol {
  list-style-type: disc !important;
  list-style-position: inside;
}

div.builder-text ul li::marker,
div.builder-text ol li::marker {
  color: var(--icon-color);
}
/* fixes the very hard to reproduce bug where the qty selector shifts the page layout */
body {
  @apply !min-w-full;
}

/* Use :where to reduce specificity and allow for builder.io to override */
.fade-in {
  animation: fade-in 0.5s ease both;
}

.fade-in-up {
  animation: fade-in-up 0.5s ease both;
}

.fade-in-left {
  animation: fade-in-left 0.5s ease both;
}

.fade-in-right {
  animation: fade-in-right 0.5s ease both;
}

.animate-in-view.out-of-view {
  opacity: 0;
  animation: none;
}

@keyframes fade-in {
  from {
    opacity: 0;
    filter: blur(10px);
  }
  to {
    opacity: 1;
    filter: blur(0px);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(50%);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0px);
  }
}

@keyframes fade-in-left {
  from {
    opacity: 0;
    transform: translateX(-50%);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
    filter: blur(0px);
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(50%);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
    filter: blur(0px);
  }
}
