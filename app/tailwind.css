@import 'tailwindcss';
@import 'tw-animate-css';
@plugin '@tailwindcss/typography';

@theme inline {
  --breakpoint-xl: 1200px;

  --radius: var(--border-radius-sm);
  --radius-full: var(--border-radius-circle);
  --radius-lg: var(--border-radius-lg);
  --radius-md: var(--border-radius-md);
  --radius-sm: var(--border-radius-sm);
  --radius-xs: var(--border-radius-xs);

  --shadow-md: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);

  --breakpoint-3xl: 1920px;

  --background-image-hero-gradient-business: radial-gradient(
    ellipse at -40% 200%,
    var(--bg-radial-gradient-start) 0%,
    var(--bg-primary) 100%
  );
  --background-image-hero-gradient-personal: radial-gradient(
    circle at 0% 100%,
    var(--bg-primary) 0%,
    var(--bg-radial-gradient-start) 300%
  );

  --text-title-1: var(--font-size-title-1);
  --text-title-2: var(--font-size-title-2);
  --text-title-3: var(--font-size-title-3);
  --text-title-4: var(--font-size-title-4);
  --text-body-lg: var(--font-size-body-lg);
  --text-body-md: var(--font-size-body-md);
  --text-body-sm: var(--font-size-body-sm);
  --text-body-xs: var(--font-size-body-xs);
  --text-body-xxs: var(--font-size-body-xxs);

  --color-background: var(--bg-primary);
  --color-background-branded: var(--bg-branded);
  --color-background-secondary: var(--bg-secondary);
  --color-background-tertiary: var(--bg-tertiary);

  --color-foreground: hsl(var(--foreground));

  --color-heading-one: var(--heading-one);
  --color-heading-two: var(--heading-two);

  --color-gradient-start: var(--green-one);
  --color-gradient-end: var(--green-two);

  --color-section-meta: var(--emphasis);

  --color-body-normal: var(--body-text-normal);
  --color-body-highlight: var(--body-highlight);
  --color-body-emphasis: var(--emphasis);

  --color-emphasis: var(--emphasis);
  --color-green-one: var(--green-one);
  --color-green-two: var(--green-two);
  --color-green-three: var(--green-three);

  --color-text-button-primary: var(--button-text-primary);
  --color-text-button-outline: var(--button-text-outline);

  --color-icon-color: var(--icon-color);

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-warning: hsl(var(--warning));
  --color-warning-foreground: hsl(var(--warning-foreground));

  --color-success: hsl(var(--success));
  --color-success-foreground: hsl(var(--success-foreground));

  --color-border: var(--border);
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --color-sidebar: hsl(var(--sidebar-background));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));

  --animate-flash: flash 0.2s ease-out;
  --animate-fade-in: fade-in 0.2s ease-out;
  --animate-fade-in-out: fade-in-out 2s ease-in-out;
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-move-light: move-light 7.5s linear infinite;
  --animate-spotlight: move-spotlight 8s infinite cubic-bezier(0.45, 0.05, 0.55, 0.95);
  --animate-move-blur-1: move-blur-1 10s linear infinite;
  --animate-move-blur-2: move-blur-2 10s linear infinite;
  --animate-blob: blob 12s ease-in-out infinite;
  --animate-blob-rotate: blob-rotate 14s linear infinite alternate;
  --animate-blob-move: blob-move 16s ease-in-out infinite;

  @keyframes move-light {
    100% {
      background-position: 0% 0%;
    }
    0% {
      background-position: 200% 0%;
    }
  }
  @keyframes move-spotlight {
    0% {
      transform: translate(-50%, -50%);
    }
    25% {
      transform: translate(-50%, 50%);
    }
    50% {
      transform: translate(50%, 50%);
    }
    75% {
      transform: translate(50%, -50%);
    }
    100% {
      transform: translate(-50%, -50%);
    }
  }
  @keyframes move-blur-1 {
    0% {
      top: 5%;
      right: 5%;
    }
    12.5% {
      top: 5%;
      right: 0%;
    }
    25% {
      top: 5%;
      right: -5%;
    }
    50% {
      top: -5%;
      right: -5%;
    }
    75% {
      top: -5%;
      right: 0%;
    }
    87.5% {
      top: -5%;
      right: 5%;
    }
    100% {
      top: 5%;
      right: 5%;
    }
  }
  @keyframes move-blur-2 {
    0% {
      top: -5%;
      right: -5%;
    }
    12.5% {
      top: -5%;
      right: 0%;
    }
    25% {
      top: -5%;
      right: 5%;
    }
    50% {
      top: 5%;
      right: 5%;
    }
    75% {
      top: 5%;
      right: 0%;
    }
    87.5% {
      top: 5%;
      right: -5%;
    }
    100% {
      top: -5%;
      right: -5%;
    }
  }
  @keyframes dropShadow {
    glow {
      -d-e-f-a-u-l-t: glow 15s linear infinite;
    }
  }
  @keyframes flash {
    from {
      filter: brightness(2);
    }
    to {
      filter: brightness(1);
    }
  }
  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes fade-in {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes fade-in-out {
    0%,
    100% {
      opacity: 0;
      scale: 1;
    }
    25% {
      opacity: 1;
      scale: 1.2;
    }
    75% {
      opacity: 1;
    }
  }
  @keyframes blob {
    0%,
    100% {
      transform: scale(0.8, 2);
    }
    50% {
      transform: scale(1.4, 0.8);
    }
  }
  @keyframes blob-rotate {
    0% {
      transform: translate3d(-50%, -50%, 0) rotateZ(-28deg);
      transform-origin: 50% 100%;
    }
    100% {
      transform: translate3d(-50%, -50%, 0) rotateZ(28deg);
      transform-origin: 50% 0%;
    }
  }
  @keyframes blob-move {
    0%,
    100% {
      transform: translateX(30%);
    }
    50% {
      transform: translateX(-30%);
    }
  }
}

@layer base {
  :root {
    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
    --primary: 176 83% 31%;
    --primary-foreground: 0 0% 100%;
    --secondary: 189 84% 20%;
    --secondary-foreground: 0 0% 100%;
    --accent: 60 4.8% 95.9%;
    --accent-foreground: 24 9.8% 10%;
    --destructive: 356 100% 45%;
    --destructive-foreground: 60 9.1% 97.8%;
    --warning: 35 100% 50%;
    --warning-foreground: 60 9.1% 97.8%;
    --success: 124 84% 36%;
    --success-foreground: 60 9.1% 97.8%;
    --ring: 20 14.3% 4.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Shared Theme */
    --font-size-title-1: 7.1875rem; /* 115px */
    --font-size-title-2: 3.75rem; /* 60px */
    --font-size-title-3: 2.875rem; /* 46px */
    --font-size-title-4: 1.5rem; /* 24px */
    --font-size-body-lg: 1.25rem; /* 20px */
    --font-size-body-md: 1.125rem; /* 18px */
    --font-size-body-sm: 1rem; /* 16px */
    --font-size-body-xs: 0.875rem; /* 14px */
    --font-size-body-xxs: 0.75rem; /* 12px */

    --spacing-xs: 0.875rem; /* 14px */
    --spacing-sm: 1.5rem; /* 24px */
    --spacing-md: 2rem; /* 32px */
    --spacing-lg: 2.8125rem; /* 45px */
    --spacing-xl: 4.6875rem; /* 75px */

    --border-radius-xs: 0.25rem; /* 4px */
    --border-radius-sm: 0.9375rem; /* 15px */
    --border-radius-md: 1.875rem; /* 30px */
    --border-radius-lg: 2.5rem; /* 40px */
    --border-radius-circle: 99999px;

    --font-family-heading: 'Inter', sans-serif;
    --font-family-body: 'Inter', sans-serif;
  }

  .personal-theme {
    --bg-branded: #008d86;
    --bg-primary: #ffffff;
    --bg-radial-gradient-start: #f8f8f8;
    --bg-primary-business-lighter: #ffffff;
    --bg-primary-personal-darker: #f8f8f8;
    --bg-secondary: #f8f8f8;
    --bg-tertiary: #efefef;
    --foreground: 0 0% 0%;
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;

    --emphasis: #008d86;

    --heading-one: #2d2d2d;
    --heading-two: #2d2d2d;

    --body-text-normal: #525252;
    --body-highlight: #2d2d2d;

    --button-text-primary: #fff;
    --button-text-outline: #2d2d2d;
    --icon-color: #008d86;

    --green-one: #22e7d0;
    --green-two: #11a89e;
    --green-three: #074f5b;
    --border: rgba(45, 45, 45, 0.2);
  }

  .business-theme {
    --bg-branded: #1df9df;
    --bg-primary: #000000;
    --bg-radial-gradient-start: #3a3a3a;
    --bg-primary-business-lighter: #0e0e0e;
    --bg-primary-personal-darker: #000000;
    --bg-secondary: #0e0e0e;
    --bg-tertiary: #1a1a1a;
    --foreground: 0 0% 100%;
    --muted: 60 4.8% 14.1%;
    --muted-foreground: 25 5.3% 55.3%;

    --emphasis: #1df9df;

    --heading-one: #fff;
    --heading-two: #979797;

    --body-text-normal: #c3c3c3;
    --body-highlight: #fff;

    --button-text-primary: #000;
    --button-text-outline: #fff;
    --icon-color: #1df9df;

    --green-one: #074f5b;
    --green-two: #1df9df;
    --green-three: #1ac6b8;
    --border: rgba(255, 255, 255, 0.2);

    /* Shadcn stuff */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  /* Hide scrollbar for Chrome, Safari and Opera */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* Default header position */
  .aa-header {
    transition: transform 0.3s ease-in-out;
  }

  /* Hide header on scroll down */
  .aa-hide-header {
    transform: translateY(-100%);
  }

  * {
    @apply border-border;
  }
  body,
  .personal-theme,
  .business-theme {
    @apply overflow-x-clip bg-background text-foreground;
  }

  html {
    @apply overflow-x-clip;
  }

  .builder-text a {
    @apply underline;
  }
  a,
  button {
    @apply cursor-pointer;
  }
}
