import { AdvertisableDiscount } from '@/routes/($theme).$handle.product/loader';
import { useCart } from './use-cart';
import { formatMoney } from '@/lib/utils';

function getDiscountValueToString(discount: AdvertisableDiscount) {
  if ('percentage' in discount.customerGets.value)
    return Math.round(discount.customerGets.value.percentage * 100) + '%';
  if ('amount' in discount.customerGets.value) return formatMoney(discount.customerGets.value.amount.amount);
}

export default function useAdvertisedDiscounts(
  productId: string,
  discounts: AdvertisableDiscount[] | undefined | null,
) {
  const { cart } = useCart();

  const quantityInCart =
    cart?.lines?.nodes?.reduce(
      (acc, line) => acc + (line.merchandise.product.id == productId ? line.quantity : 0),
      0,
    ) || 0;

  const nextIndex = discounts
    ? discounts.findIndex(
        discount => quantityInCart < parseInt(discount.minimumRequirement.greaterThanOrEqualToQuantity),
      )
    : -1;

  const currentDiscount =
    nextIndex == -1 ? discounts?.[discounts.length - 1] : nextIndex == 0 ? null : discounts?.[nextIndex - 1];
  const nextDiscount = nextIndex == -1 || !discounts?.length ? null : discounts?.[nextIndex];

  const message = nextDiscount
    ? `Add ${
        nextDiscount.minimumRequirement.greaterThanOrEqualToQuantity - quantityInCart
      } more to save ${getDiscountValueToString(nextDiscount)} OFF`
    : currentDiscount
      ? `Max discount of ${getDiscountValueToString(currentDiscount)} applied!`
      : '';

  return { currentDiscount, nextDiscount, message };
}
