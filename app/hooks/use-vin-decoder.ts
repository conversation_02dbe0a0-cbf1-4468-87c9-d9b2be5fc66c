import { getNHTSAResponseToMMY } from '@/business/core/constants/vin';
import type { NHTSADecodeVINResponse, VINMakeModelYear } from '@/business/core/types/vin';
import { useCallback, useEffect, useRef, useState } from 'react';

export function useVINDecoder({
  onDecoded,
  queryOnLoad,
}: {
  onDecoded?: (MMY: VINMakeModelYear | undefined) => void;
  queryOnLoad?: string;
}) {
  const debounce = useRef(true);
  const [isDecoding, setIsDecoding] = useState(true);

  const decode = useCallback(
    async (VIN: string) => {
      setIsDecoding(true);

      fetch(`https://vpic.nhtsa.dot.gov/api/vehicles/decodevin/${VIN}?format=json`)
        .then(res => res.json<NHTSADecodeVINResponse>())
        .then(getNHTSAResponseToMMY)
        .then(MMY => {
          onDecoded?.(MMY);
        })
        .catch(error => {
          console.warn(error);
          onDecoded?.(undefined);
        })
        .finally(() => setIsDecoding(false));
    },
    [onDecoded],
  );

  useEffect(() => {
    if (queryOnLoad && debounce.current) {
      debounce.current = false;
      decode(queryOnLoad);
    }
  }, []);

  return { isDecoding, decode };
}
