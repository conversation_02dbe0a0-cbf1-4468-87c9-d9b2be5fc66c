import { CartSystemView } from '@/business/handlers/types/cart';
import { AugmentedCart } from '@/business/core/types/cart';
import { createContext, useContext } from 'react';
import { OptimisticCart } from '@shopify/hydrogen';
/**
 * Easy way to handle loading states for the cart and systems
 * Undefined means data is still loading
 * Null means data is loaded and is empty
 * Anything else is the loaded data
 */
export type CartContextType = {
  systemType: SystemType;
  accessToken: string | undefined | null;
  cart: OptimisticCart<AugmentedCart> | undefined | null;
  isLoadingCart: boolean;
  systems: CartSystemView[] | undefined | null;
  fallbackLocationId: string | undefined | null;
  orderingCustomerId: string | undefined | null;
};

export const CartContext = createContext<CartContextType>({
  cart: undefined,
  accessToken: undefined,
  isLoadingCart: true,
  systems: undefined,
  fallbackLocationId: undefined,
  orderingCustomerId: undefined,
  systemType: 'personal',
});

export function useCart() {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
