import { useFetcher, useLocation } from 'react-router';
import { useRemixForm } from 'remix-hook-form';
import { useFormOnSubmit } from '@/components/ui/form';
import { resolver, SchemaType } from '@/routes/otp/resolver';

export function useOTP({ onSuccess, ...props }: { onSuccess?: () => void } & SchemaType = {}) {
  const fetcher = useFetcher();
  const resendFetcher = useFetcher();

  const resendCode = (sendTo: string = props.email || '', blockUnknown = props.blockUnknown || false) => {
    const formData = new FormData();
    formData.append('email', sendTo);
    formData.append('blockUnknown', blockUnknown.toString());
    return resendFetcher.submit(formData, { action: '/otp', method: 'post' });
  };

  const form = useRemixForm<SchemaType>({
    resolver,
    defaultValues: props,
    submitConfig: {
      action: '/otp',
    },
    fetcher,
  });

  useFormOnSubmit(form, { onSuccess });

  return { form, resendCode, isResending: resendFetcher.state == 'submitting' };
}
