import { useRef, useState } from 'react';
import { useIsomorphicLayoutEffect } from './use-isomorphic-effect';

const watching: { target: Element; animate: boolean; run: () => void }[] = [];
let observer: IntersectionObserver;
const getObserver = () => {
  if (observer) return observer;
  observer = new IntersectionObserver(
    entries =>
      entries.forEach(e => {
        const found = watching?.find(w => w.target == e.target);
        if (!found) {
          observer.unobserve(e.target);
          return;
        }
        if (e.isIntersecting) {
          found.animate = true;
          requestAnimationFrame(found.run);
        } else {
          found.animate = false;
        }
      }),
    { threshold: 0.0 },
  );
  return observer;
};

export default function useAnimateOnView(animation: (root: Element) => void) {
  const [element, setElement] = useState<HTMLElement | null>(null);
  const animationRef = useRef(animation);
  animationRef.current = animation;

  useIsomorphicLayoutEffect(() => {
    if (!element) return;

    const watch = {
      target: element,
      animate: false,
      run,
    };

    function run() {
      if (!watch.animate) return;

      animationRef.current(element!);
      requestAnimationFrame(run);
    }

    watching.push(watch);
    const O = getObserver();
    O.observe(element);

    return () => {
      watch.animate = false;
      watching.splice(watching.indexOf(watch), 1);
      O.unobserve(element);
    };
  }, [element]);

  return { ref: setElement };
}
