import { loadStripe, Stripe } from '@stripe/stripe-js';

const stripeInstances: { [publishableKey: string]: Promise<Stripe | null> | undefined } = {};

export function getStripe(publishableKey: string) {
  if (!publishableKey) return null;
  if (stripeInstances[publishableKey]) return stripeInstances[publishableKey];

  stripeInstances[publishableKey] = loadStripe(publishableKey);

  return stripeInstances[publishableKey];
}
