import { startTransition, useEffect, useState } from 'react';

export const usePromiseEffect = <T, E = unknown>(promise: Promise<T | null | undefined> | null | undefined) => {
  const [value, setValue] = useState<T | undefined | null>(undefined);
  const [error, setError] = useState<E | undefined | null>(undefined);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    //Used to only apply state updates from the currently running effect.
    let isCurrent = true;

    startTransition(() => {
      if (!isCurrent) return;
      if (!promise) {
        setValue(null);
        setError(null);
        setIsLoading(false);
        return;
      }
      // setIsLoading(true);
      promise
        ?.then(value =>
          startTransition(() => {
            if (!isCurrent) return;
            setError(null);
            setValue(typeof value == 'undefined' ? null : value);
            // console.log(value);
            setIsLoading(false);
          }),
        )
        .catch(error =>
          startTransition(() => {
            if (!isCurrent) return;
            setError(typeof error == 'undefined' ? null : error);
            setValue(null);
            // console.error(error);
            setIsLoading(false);
          }),
        );
    });
    return () => {
      isCurrent = false;
    };
  }, [promise]);

  return { value, error, isLoading };
};
