import { useRootData } from '@/root';
import { useEffect, useState, useTransition } from 'react';

export function useIsMobileBreakpoint(breakpoint = 640) {
  const { isMobile: isMobileRequest, screenWidth: screenWidthRequest } = useRootData();
  const [_, startTransition] = useTransition();
  const [isMobile, setIsMobile] = useState<boolean>(() =>
    typeof screenWidthRequest !== 'undefined' ? screenWidthRequest < breakpoint : isMobileRequest,
  );

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const onChange = () => {
      startTransition(() => {
        setIsMobile(window.innerWidth < breakpoint);
      });
    };

    onChange();

    const mql = window.matchMedia(`(max-width: ${breakpoint - 1}px)`);
    mql.addEventListener('change', onChange);
    return () => mql.removeEventListener('change', onChange);
  }, [breakpoint]);

  return isMobile;
}

export function useIsMobileDevice() {
  const { isMobile } = useRootData();
  return isMobile;
}
