import { Fetcher<PERSON>ithComponents } from 'react-router';
import { SerializeFrom } from 'react-router';
import type { AppData } from '@react-router/server-runtime/dist/data';
import type { CartActionInput } from '@shopify/hydrogen';
import { useCallback } from 'react';
import { useIsBusinessSite } from './use-is-business-site';

export type CartFetcherSubmitOptions = Pick<
  NonNullable<Parameters<FetcherWithComponents<SerializeFrom<AppData>>['submit']>[1]>,
  'action' | 'flushSync' | 'preventScrollReset' | 'relative'
>;

export function useCartSubmit<TData = AppData>(fetcher: FetcherWithComponents<SerializeFrom<TData>>) {
  const isBusiness = useIsBusinessSite();
  const submit = useCallback(
    (args: CartActionInput | CartActionInput[], options?: CartFetcherSubmitOptions) => {
      fetcher.submit(
        {
          cartFormInput: JSON.stringify(args),
        },
        {
          method: 'POST',
          action: isBusiness ? '/business/cart' : '/home/<USER>',
          encType: 'multipart/form-data',
          ...options,
        },
      );
    },
    [fetcher],
  );

  return { submit, isLoading: fetcher.state === 'submitting' || fetcher.state === 'loading' };
}
