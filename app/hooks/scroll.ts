import { useEffect } from 'react';

export function useScroll(onScroll: () => void, deps: any[] = []) {
  useEffect(() => {
    if (!window) return;

    window.addEventListener('scroll', onScroll);
    window.addEventListener('load', onScroll);
    window.addEventListener('resize', onScroll);

    return () => {
      window.removeEventListener('scroll', onScroll);
      window.removeEventListener('load', onScroll);
      window.removeEventListener('resize', onScroll);
    };
  }, deps);
}
