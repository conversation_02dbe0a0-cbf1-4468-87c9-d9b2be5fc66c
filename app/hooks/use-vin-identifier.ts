import type { IdentifyVINsFromImageResponse, LicensePlate } from '@/business/core/types/vin';
import { useCallback, useState } from 'react';

export function useVINIdentifier({
  onSuccess,
  onError,
}: {
  onSuccess?: (response: IdentifyVINsFromImageResponse) => void;
  onError?: (error: unknown) => void;
}) {
  const [loading, setLoading] = useState(false);

  const identify = useCallback(
    async (value: File | Blob | LicensePlate | null) => {
      if (!value) return;

      setLoading(true);

      const formData = new FormData();

      if (value instanceof File || value instanceof Blob) {
        formData.append('image', value, 'prompt.jpg');
      } else if (value?.plate && value?.stateCode) {
        formData.append('plate', JSON.stringify(value));
      }

      try {
        const response = await fetch('/api/identify-vins', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json<IdentifyVINsFromImageResponse>();

        onSuccess?.(data);
        return data;
      } catch (error) {
        console.error(error);
        onError?.(error);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [onSuccess, onError],
  );

  return { identify, loading };
}
