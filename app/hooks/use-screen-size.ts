import { useRootData } from '@/root';
import { useState, useTransition } from 'react';
import { useIsomorphicLayoutEffect } from './use-isomorphic-effect';

export default function useScreenSize() {
  const { screenWidth, screenHeight } = useRootData();

  const [_, startTransition] = useTransition();
  const [screenWidthState, setScreenWidthState] = useState(screenWidth);
  const [screenHeightState, setScreenHeightState] = useState(screenHeight);

  useIsomorphicLayoutEffect(() => {
    if (typeof window === 'undefined') return;

    const onChange = () => {
      startTransition(() => {
        setScreenWidthState(window.innerWidth);
        setScreenHeightState(window.innerHeight);
      });
    };
    onChange();

    window.addEventListener('resize', onChange);
    return () => document.removeEventListener('resize', onChange);
  }, []);

  return { width: screenWidthState, height: screenHeightState };
}
