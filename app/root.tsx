import favicon from '@/assets/favicon.svg';
import { getShopAnalytics } from '@shopify/hydrogen';
import {
  isRouteErrorResponse,
  LoaderFunctionArgs,
  Outlet,
  ShouldRevalidateFunction,
  useRouteError,
  useRouteLoaderData,
} from 'react-router';
import { UAParser } from 'ua-parser-js';
import { SessionClient } from './business/clients/session-client';
import { StorefrontClient } from './business/clients/storefront-client';

/**
 * This is important to avoid re-fetching root queries on sub-navigations
 */
export const shouldRevalidate: ShouldRevalidateFunction = ({ formMethod, currentUrl, nextUrl }) => {
  // revalidate when a mutation is performed e.g add to cart, login...
  if (formMethod && formMethod !== 'GET') {
    return true;
  }

  // revalidate when manually revalidating via useRevalidator
  if (currentUrl.toString() === nextUrl.toString()) {
    return true;
  }

  return false;
};

export function links() {
  return [
    {
      rel: 'preconnect',
      href: 'https://cdn.shopify.com',
    },
    {
      rel: 'preconnect',
      href: 'https://shop.app',
    },
    { rel: 'icon', type: 'image/svg+xml', href: favicon },
  ];
}

const isMobileRequest = (request: Request) => {
  const CH_UA_Mobile = request.headers.get('Sec-Ch-Ua-Mobile');

  if (CH_UA_Mobile) return CH_UA_Mobile == '?1';

  const userAgent = request.headers.get('user-agent');
  const parser = new UAParser(userAgent || '');
  const type = parser.getDevice().type;

  return type == 'mobile' || type == 'tablet';
};

export async function loader({ context, request }: LoaderFunctionArgs) {
  const isMobile = isMobileRequest(request);
  const screenWidth = parseInt(request.headers.get('Sec-CH-Viewport-Width') || '') || undefined;
  const screenHeight = parseInt(request.headers.get('Sec-CH-Viewport-Height') || '') || undefined;
  const env = context.resolve<Env>('env');
  const storefront = context.resolve(StorefrontClient);
  const session = context.resolve(SessionClient);

  const url = new URL(request.url);
  const path = url.pathname;
  const isBusiness = path.includes('business');

  if (isBusiness && session.get('browsingSystemType') !== 'business') {
    session.set('browsingSystemType', 'business');
  }

  if (!isBusiness && session.get('browsingSystemType') !== 'personal') {
    session.set('browsingSystemType', 'personal');
  }

  return {
    time: Date.now(),
    isMobile,
    screenWidth,
    screenHeight,
    builderApiKey: env.PUBLIC_BUILDER_KEY,
    publicStoreDomain: env.PUBLIC_STORE_DOMAIN,
    shop: getShopAnalytics({
      storefront,
      publicStorefrontId: env.PUBLIC_STOREFRONT_ID,
    }),
    consent: {
      checkoutDomain: env.PUBLIC_CHECKOUT_DOMAIN,
      storefrontAccessToken: env.PUBLIC_STOREFRONT_API_TOKEN,
      withPrivacyBanner: false,
      // localize the privacy banner
      country: storefront.i18n.country,
      language: storefront.i18n.language,
    },
  };
}

export function useRootData() {
  const data = useRouteLoaderData<typeof loader>('root');

  return data!;
}

export default function App() {
  return <Outlet />;
}

export function ErrorBoundary() {
  const error = useRouteError();
  let errorMessage = 'Unknown error';
  let errorStatus = 500;

  if (isRouteErrorResponse(error)) {
    errorMessage = error?.data?.message ?? error.data;
    errorStatus = error.status;
  } else if (error instanceof Error) {
    errorMessage = error.message;
  }

  return (
    <div className="route-error">
      <h1>Oops</h1>
      <h2>{errorStatus}</h2>
      {errorMessage && (
        <fieldset>
          <pre>{errorMessage}</pre>
        </fieldset>
      )}
    </div>
  );
}
