import { Analytics, useNonce } from '@shopify/hydrogen';
import { useRootData } from './root';
import { useIsBusinessSite } from './hooks/use-is-business-site';
import { register } from '@builder.io/sdk-react/edge';
import useOnMount from './hooks/use-on-mount';
import { startTransition } from 'react';
import { initializeAnimation } from './lib/animation';
import globalStyles from '@/global.css?url';
import tailwindStyles from '@/tailwind.css?url';
import { Links, LoaderFunctionArgs, Meta, Outlet, Scripts, ScrollRestoration, useLoaderData } from 'react-router';
import ThemeProvider from './components/ThemeProvider';
import CookieAnnouncementBar from './components/CookieAnnouncementBar';
import { Toaster } from './components/ui/toaster';
import { HeadersFunction } from 'react-router';
import { TooltipProvider } from './components/ui/tooltip';
import { useIsMounted } from './hooks/use-mounted';
import { CartProvider } from './components/Cart/CartProvider';

const themeFontSizes = [
  { name: 'title-1', value: 'var(--font-size-title-1, 115px)' },
  { name: 'title-2', value: 'var(--font-size-title-2, 60px)' },
  { name: 'title-3', value: 'var(--font-size-title-3, 45px)' },
  { name: 'title-4', value: 'var(--font-size-title-4, 24px)' },
  { name: 'body-lg', value: 'var(--font-size-body-lg, 20px)' },
  { name: 'body-md', value: 'var(--font-size-body-md, 18px)' },
  { name: 'body-sm', value: 'var(--font-size-body-sm, 16px)' },
  { name: 'body-xs', value: 'var(--font-size-body-xs, 14px)' },
  { name: 'body-xxs', value: 'var(--font-size-body-xxs, 12px)' },
];

const themeSpacing = [
  { name: 'XS', value: 'var(--spacing-xs, 14px)' },
  { name: 'SM', value: 'var(--spacing-sm, 24px)' },
  { name: 'MD', value: 'var(--spacing-md, 32px)' },
  { name: 'LG', value: 'var(--spacing-lg, 45px)' },
  { name: 'XL', value: 'var(--spacing-xl, 75px)' },
];

const themeFontFamilies = [
  { name: 'heading', value: 'var(--font-family-heading, "Inter", sans-serif)' },
  { name: 'body', value: 'var(--font-family-body, "Inter", sans-serif)' },
];

const themeBorderRadius = [
  { name: 'sm', value: 'var(--border-radius-sm, 15px)' },
  { name: 'md', value: 'var(--border-radius-md, 30px)' },
  { name: 'lg', value: 'var(--border-radius-lg, 40px)' },
  { name: 'full', value: 'var(--border-radius-circle, 100%)' },
];

const themeColors = [
  { name: 'bg-branded', value: 'var(--bg-branded, #008d86)' },
  { name: 'bg-primary', value: 'var(--bg-primary, #FFFFFF)' },
  { name: 'bg-primary-business-lighter', value: 'var(--bg-primary-business-lighter, #ffffff)' },
  { name: 'bg-primary-personal-darker', value: 'var(--bg-primary-personal-darker, #f8f8f8)' },
  { name: 'bg-secondary', value: 'var(--bg-secondary, #f8f8f8)' },
  { name: 'bg-tertiary', value: 'var(--bg-tertiary, #efefef)' },

  { name: 'heading-one', value: 'var(--heading-one, #2d2d2d)' },
  { name: 'heading-two', value: 'var(--heading-two, #2d2d2d)' },
  { name: 'section.meta', value: 'var(--emphasis, #008d86)' },
  { name: 'body.normal', value: 'var(--body-text-normal, #525252)' },
  { name: 'body.highlight', value: 'var(--body-highlight, #2d2d2d)' },
  { name: 'icon.color', value: 'var(--icon-color, #008d86)' },
];

export const headers: HeadersFunction = () =>
  new Headers({
    'Accept-CH': 'Sec-CH-UA-Mobile, Sec-CH-Viewport-Width, Sec-CH-Viewport-Height',
  });

export async function loader({ context }: LoaderFunctionArgs) {
  const env = context.resolve<Env>('env');

  return { gtmId: env.GTM_CONTAINER_ID };
}

export default function Layout() {
  const nonce = useNonce();
  const isBusiness = useIsBusinessSite();

  register('editor.settings', {
    allowOverridingTokens: true,
    designTokens: {
      colors: themeColors,
      fontSize: themeFontSizes,
      fontFamily: themeFontFamilies,
      spacing: themeSpacing,
      borderRadius: themeBorderRadius,
    },
  });

  useOnMount(() => startTransition(initializeAnimation));

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <link rel="stylesheet" href={globalStyles} />
        <link rel="stylesheet" href={tailwindStyles} />
        <CustomHeaderScripts />
        <Meta />
        <Links />
      </head>
      <ThemeProvider theme={isBusiness ? 'business' : 'personal'}>
        {themeClassName => (
          <body className={themeClassName}>
            <CustomBodyScripts />
            <TooltipProvider>
              <CartProvider>
                <Outlet />
              </CartProvider>
            </TooltipProvider>
            <CookieAnnouncementBar />
            <Toaster />
            <ScrollRestoration nonce={nonce} />
            <Scripts nonce={nonce} />
          </body>
        )}
      </ThemeProvider>
    </html>
  );
}

function CustomHeaderScripts() {
  const { gtmId } = useLoaderData<typeof loader>();
  const nonce = useNonce();

  const mounted = useIsMounted();

  if (!mounted) return null;

  return (
    <>
      {/* Google Tag Manager */}
      <script
        nonce={nonce}
        // suppressHydrationWarning
        dangerouslySetInnerHTML={{
          __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${gtmId}');`,
        }}
      />
    </>
  );
}

function CustomBodyScripts() {
  const { gtmId } = useLoaderData<typeof loader>();

  return (
    <>
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${gtmId}`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        ></iframe>
      </noscript>
    </>
  );
}
