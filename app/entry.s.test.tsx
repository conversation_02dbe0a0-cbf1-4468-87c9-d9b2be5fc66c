import { ServerRouter } from 'react-router';
import { createContentSecurityPolicy } from '@shopify/hydrogen';
import type { EntryContext } from 'react-router';
import i18next from 'i18next';
import isbot from 'isbot';
import { renderToReadableStream } from 'react-dom/server';
import { I18nextProvider, initReactI18next } from 'react-i18next';
import i18n from './locales/i18n';
import { resources } from './locales/resources';

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  reactRouterContext: EntryContext,
) {
  await i18next.use(initReactI18next).init({
    ...i18n,
    resources,
  });

  const { nonce, header, NonceProvider } = createContentSecurityPolicy({
    defaultSrc: [
      "'self'",
      'data:',
      'blob:',
      'https://cdn.shopify.com',
      'https://shopify.com',
      'localhost:*',
      'https://www.youtube.com',
      'https://cdn.builder.io',
      'https://builder.io',
      'https://fonts.gstatic.com',
      'https://firebasestorage.googleapis.com',
      'https://js.stripe.com',
      'https://static.zdassets.com',
      'https://*.zendesk.com',
      'https://ekr.zdassets.com',
      'wss://*.zendesk.com',
      'https://*.googletagmanager.com',
      "'unsafe-eval'",
    ], //Add builder content support
    connectSrc: [
      "'self'",
      'https://monorail-edge.shopifysvc.com',
      'https://cdn.shopify.com',
      'localhost:*',
      'ws://localhost:*',
      'ws://127.0.0.1:*',
      'https://cdn.builder.io',
      'https://builder.io',
      'https://vpic.nhtsa.dot.gov',
      // 'wss://allaware-hydrogen-cs.ngrok.io:*',
      // 'wss://allawaredev-jm.ngrok.io:*',
      // 'https://all-aware-dev-cs.myshopify.com',
      // 'https://all-aware-testing.myshopify.com',
      'https://static.zdassets.com',
      'https://*.zendesk.com',
      'https://ekr.zdassets.com',
      'wss://*.zendesk.com',
    ], //Add builder content support
    frameAncestors: ['https://admin.shopify.com'],
  });

  const now = Date.now();
  const body = await renderToReadableStream(
    <NonceProvider>
      <I18nextProvider i18n={i18next}>
        <ServerRouter context={reactRouterContext} url={request.url} nonce={nonce} />
      </I18nextProvider>
    </NonceProvider>,
    {
      nonce,
      signal: request.signal,
      onError(error) {
        console.error(error);
        responseStatusCode = 500;
      },
    },
  );

  await body.allReady; //Force the stream to finish before measuring the time

  responseHeaders.append('Server-Timing', `rendering;dur=${Date.now() - now}`);

  // if (isbot(request.headers.get('user-agent'))) {
  //   await body.allReady;
  // }

  responseHeaders.set('Content-Type', 'text/html');
  responseHeaders.set('Content-Security-Policy', header);

  return new Response(body, {
    headers: responseHeaders,
    status: responseStatusCode,
  });
}
