import { inject, singleton } from 'tsyringe';

export const SENDGRID_EMAIL = '<EMAIL>';

export interface SendgridClientOptions {
  marketing?: boolean;
}

export interface SendgridEmailContent {
  subject: string;
  text?: string;
  html?: string;
}

@singleton()
export class SendgridClient {
  private readonly _marketingKey: string;
  private readonly _transactionalKey: string;
  constructor(@inject('env') env: Env) {
    this._marketingKey = env.SENDGRID_MARKETING_API_KEY;
    this._transactionalKey = env.SENDGRID_TRANSACTIONAL_API_KEY;
  }

  private resolveApiKey(options?: Pick<SendgridClientOptions, 'marketing'>) {
    return options?.marketing ? this._marketingKey : this._transactionalKey;
  }

  async sendTemplate(to: string, templateId: string, dynamicTemplateData?: any, options?: SendgridClientOptions) {
    const apiKey = this.resolveApiKey(options);

    const res = await fetch(`https://api.sendgrid.com/v3/mail/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        from: {
          email: SENDGRID_EMAIL,
          name: 'All Aware',
        },
        personalizations: [
          {
            to: [
              {
                email: to,
              },
            ],
            dynamic_template_data: dynamicTemplateData,
          },
        ],
        template_id: templateId,
      }),
    });

    if (!res.ok) {
      throw new Error(`SG ${res.status}: ${res.statusText}`);
    }
  }

  async sendRaw(to: string, content: SendgridEmailContent, options?: SendgridClientOptions) {
    const apiKey = this.resolveApiKey(options);

    if (!content.text && !content.html) {
      throw new Error('Either text or html content must be provided');
    }

    const payload: any = {
      from: {
        email: SENDGRID_EMAIL,
        name: 'All Aware',
      },
      personalizations: [
        {
          to: [
            {
              email: to,
            },
          ],
        },
      ],
      subject: content.subject,
      content: [],
    };

    if (content.text) {
      payload.content.push({
        type: 'text/plain',
        value: content.text,
      });
    }

    if (content.html) {
      payload.content.push({
        type: 'text/html',
        value: content.html,
      });
    }

    const res = await fetch(`https://api.sendgrid.com/v3/mail/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
    });

    if (!res.ok) {
      throw new Error(`SG ${res.status}: ${res.statusText}`);
    }
  }
}
