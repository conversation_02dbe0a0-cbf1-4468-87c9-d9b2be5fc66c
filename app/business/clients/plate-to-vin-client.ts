import { inject, singleton } from 'tsyringe';

interface PlateToVinConvertInput {
  state: string;
  plate: string;
}

interface PlateToVinConvertOutput {
  success: boolean;
  vin: {
    vin: string;
    fuel: string;
    make: string;
    name: string;
    trim: string;
    year: string;
    color: {
      name: string;
      abbreviation: string;
    };
    model: string;
    style: string;
    engine: string;
    driveType: string;
    transmission: string;
  };
}
interface PlateToVinLookupInput {
  vin: string;
}

@singleton()
export class PlateToVinClient {
  private readonly apiKey: string;
  constructor(@inject('env') env: Env) {
    this.apiKey = env.PLATE_TO_VIN_API_KEY;
  }

  async convert(input: PlateToVinConvertInput) {
    return await this.request<PlateToVinConvertOutput>('convert', input);
  }

  async lookup(input: PlateToVinLookupInput) {
    return await this.request<any>('vin-lookup', input);
  }

  private async request<R extends object = object, B extends object = object>(path: string, body: B): Promise<R> {
    const url = new URL(`https://platetovin.com/api/${path}`);

    const headers = {
      Authorization: this.apiKey,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    const data = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    }).then(response => response.json<R>());

    return data;
  }
}
