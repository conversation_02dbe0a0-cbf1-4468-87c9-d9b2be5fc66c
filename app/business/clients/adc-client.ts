import { MetafieldRepo } from '@/business/core/repositories/metafield';
import { ShopRepo } from '@/business/core/repositories/shop';
import { ADCAuthToken, ADCResourceOp } from '@/business/core/types/adc';
import { CacheStrategy } from '@/business/core/types/cache';
import { CacheNone, type WithCache } from '@shopify/hydrogen';
import { inject, singleton } from 'tsyringe';

const ADC_AUTH_URL = `https://alarmadmin.alarm.com/AdminApiAccess/token`;
const ADC_API_URL = `https://alarmadmin.alarm.com/PartnerApi/v1`;

//Allow a buffer of 2.5 minutes before token expires
const EXPIRATION_BUFFER = 2.5 * 60 * 1000;
//TODO: Find the official refresh-token expiration time
const REFRESH_EXPIRATION = 30 * 60 * 1000;

export class ADCError extends Error {
  constructor(message: string, error?: any) {
    super(message, { cause: error });
  }
}

@singleton()
export class ADCClient {
  private readonly username: string;
  private readonly password: string;
  private readonly clientId: string;
  private readonly twoFactorId: string;
  readonly adcDealerId: string;
  constructor(
    @inject('env') { ADC_AUTH_USERNAME, ADC_AUTH_PASSWORD, ADC_AUTH_CLIENT_ID, ADC_AUTH_2FA_ID, ADC_DEALER_ID }: Env,
    private readonly metafieldRepo: MetafieldRepo,
    private readonly shopRepo: ShopRepo,
    @inject('withCache') private readonly withCache: WithCache,
  ) {
    this.username = ADC_AUTH_USERNAME;
    this.password = ADC_AUTH_PASSWORD;
    this.clientId = ADC_AUTH_CLIENT_ID;
    this.twoFactorId = ADC_AUTH_2FA_ID;
    this.adcDealerId = ADC_DEALER_ID;
  }

  async updateResource(url: string, ops: ADCResourceOp[]) {
    return await this.request('PATCH', url, ops);
  }

  async request<T extends object = object, B extends object = object>(
    method: 'GET' | 'POST' | 'PATCH' | 'DELETE',
    endpoint: string,
    body?: B,
    cacheStrategy: CacheStrategy = CacheNone(),
  ): Promise<T> {
    const attemptCount = 10;
    for (let attempt = 0; attempt < attemptCount; attempt++) {
      const token = await this.getToken();

      const res = await this.withCache.fetch<T>(
        ADC_API_URL + endpoint,
        {
          method,
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Cache-Control': 'private, no-store, max-age=0',
          },
          body: body ? JSON.stringify(body) : undefined,
        },
        { cacheStrategy, cacheKey: ['ADC', method, endpoint], shouldCacheResponse: (_, res) => res.ok },
      );

      if (!res.response.ok && attempt < attemptCount - 1) {
        console.warn(
          `Retrying... ${method} ${endpoint}: ${res.response.status} ${res.response.statusText}
          ${body ? JSON.stringify(body) : '<no body>'}
          ${await res.response.text()}`,
        );
        continue;
      }

      if (!res.data || !res.response.ok)
        throw new ADCError(
          `${method} ${endpoint}: ${res.response.status} ${res.response.statusText}
          ${body ? JSON.stringify(body) : '<no body>'}
          ${await res.response.text()}`,
          res.data,
        );
      else if (attempt > 0) console.log(`Request succeeded after ${attempt} attempt(s)`);

      return res.data;
    }
    throw new ADCError(`Failed to get data from ADC: ${method} ${endpoint}`);
  }

  private async setToken(token: ADCAuthToken | undefined) {
    const shopId = await this.shopRepo.getShopID();

    if (!token?.access_token) throw new Error('Cannot set undefined ADC access token!');

    const metafields = await this.metafieldRepo.setMetafields({
      namespace: '$app:adc',
      key: 'token',
      ownerId: shopId,
      type: 'json',
      value: JSON.stringify(token),
    });

    return metafields?.[0]?.id;
  }

  private async getToken() {
    let token = await this.metafieldRepo.getShopJSONMetafield<ADCAuthToken>('$app:adc', 'token');

    if (token?.access_token) {
      if (Date.now() < Date.parse(token['.expires']) - EXPIRATION_BUFFER) {
        return token.access_token;
      } else if (Date.now() < Date.parse(token['.issued']) + REFRESH_EXPIRATION) {
        const refreshForm = new URLSearchParams({
          grant_type: 'refresh_token',
          client_id: this.clientId,
          refresh_token: token.refresh_token,
        });

        token = await fetch(ADC_AUTH_URL, { method: 'POST', body: refreshForm.toString() }).then(
          res => res.json() as Promise<ADCAuthToken>,
        );

        if (token?.access_token) {
          await this.setToken(token);

          return token.access_token;
        }
      }
    }

    const authForm = new URLSearchParams({
      username: this.username,
      password: this.password,
      grant_type: 'password',
      client_id: this.clientId,
      x_twofactor_device_id: this.twoFactorId,
    });
    token = await fetch(ADC_AUTH_URL, { method: 'POST', body: authForm.toString() }).then(
      res => res.json() as Promise<ADCAuthToken>,
    );

    await this.setToken(token);

    return token?.access_token;
  }
}
