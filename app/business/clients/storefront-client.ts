import type { Storefront } from '@shopify/hydrogen';
import { injectable, Lifecycle, scoped, singleton } from 'tsyringe';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class StorefrontClient implements Storefront {
  query: Storefront['query'];
  mutate: Storefront['mutate'];
  cache?: Storefront['cache'];
  CacheNone: Storefront['CacheNone'];
  CacheLong: Storefront['CacheLong'];
  CacheShort: Storefront['CacheShort'];
  CacheCustom: Storefront['CacheCustom'];
  generateCacheControlHeader: Storefront['generateCacheControlHeader'];
  getPublicTokenHeaders: Storefront['getPublicTokenHeaders'];
  getPrivateTokenHeaders: Storefront['getPrivateTokenHeaders'];
  getShopifyDomain: Storefront['getShopifyDomain'];
  getApiUrl: Storefront['getApiUrl'];
  i18n: Storefront['i18n'];

  constructor({
    query,
    mutate,
    cache,
    CacheNone,
    CacheLong,
    CacheShort,
    CacheCustom,
    generateCache<PERSON>ontrolHeader,
    getPublicTokenHeaders,
    getPrivateTokenHeaders,
    getShopifyDomain,
    getApiUrl,
    i18n,
  }: Storefront) {
    this.query = query;
    this.mutate = mutate;
    this.cache = cache;
    this.CacheNone = CacheNone;
    this.CacheLong = CacheLong;
    this.CacheShort = CacheShort;
    this.CacheCustom = CacheCustom;
    this.generateCacheControlHeader = generateCacheControlHeader;
    this.getPublicTokenHeaders = getPublicTokenHeaders;
    this.getPrivateTokenHeaders = getPrivateTokenHeaders;
    this.getShopifyDomain = getShopifyDomain;
    this.getApiUrl = getApiUrl;
    this.i18n = i18n;
  }
}
