import { ApiVersion, AppDistribution, shopifyApp } from '@brandboostinggmbh/shopify-app-react-router/server';
import { MemorySessionStorage } from '@shopify/shopify-app-session-storage-memory';
import { inject, injectable, Lifecycle, scoped } from 'tsyringe';
import { ADMIN_API_VERSION } from '../core/constants/config';
import type { SessionParams } from '@shopify/shopify-api';

function initializeShopify(env: Env) {
  const sessionStorage = new MemorySessionStorage();

  const shopify = shopifyApp({
    apiKey: env.SHOPIFY_API_KEY,
    apiSecretKey: env.SHOPIFY_API_SECRET,
    appUrl: env.HOST,
    future: {
      unstable_newEmbeddedAuthStrategy: true,
    },
    distribution: AppDistribution.SingleMerchant,
    isEmbeddedApp: true,
    useOnlineTokens: true,
    scopes: env.SCOPES.split(','),
    apiVersion: ADMIN_API_VERSION as ApiVersion,
    sessionStorage,
    customShopDomains: [env.PUBLIC_STORE_DOMAIN],
    hooks: {
      afterAuth: async ({ session }) => {
        const sessions = await sessionStorage.findSessionsByShop(env.PUBLIC_STORE_DOMAIN);

        const offline = sessions.find(sess => !sess.isOnline);

        if (offline && offline.accessToken != env.SHOPIFY_OFFLINE_ADMIN_ACCESS_TOKEN) {
          console.log(`Save offline access token: ${offline.accessToken}: ${offline.scope}`);
        }
      },
    },
  });

  return shopify;
}

type ShopifyInterface = ReturnType<typeof initializeShopify>;

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class ShopifyClient implements ShopifyInterface {
  sessionStorage: ShopifyInterface['sessionStorage'];
  addDocumentResponseHeaders: ShopifyInterface['addDocumentResponseHeaders'];
  registerWebhooks: ShopifyInterface['registerWebhooks'];
  authenticate: ShopifyInterface['authenticate'];
  unauthenticated: ShopifyInterface['unauthenticated'];
  login: ShopifyInterface['login'];

  constructor(@inject('env') env: Env) {
    const { sessionStorage, addDocumentResponseHeaders, registerWebhooks, authenticate, unauthenticated, login } =
      initializeShopify(env);

    this.sessionStorage = sessionStorage;
    this.addDocumentResponseHeaders = addDocumentResponseHeaders;
    this.registerWebhooks = registerWebhooks;
    this.authenticate = authenticate;
    this.unauthenticated = unauthenticated;
    this.login = login;

    this.sessionStorage.storeSession({
      id: `offline_${env.PUBLIC_STORE_DOMAIN}`,
      shop: env.PUBLIC_STORE_DOMAIN,
      state: 'active',
      isOnline: false,
      scope: env.SCOPES,
      accessToken: env.SHOPIFY_OFFLINE_ADMIN_ACCESS_TOKEN,
    } as SessionParams as any);
  }
}
