import type { CacheOptions } from '@/business/core/types/cache';
import {
  AdminApiClient,
  AdminMutations,
  AdminQueries,
  AllOperations,
  ApiClientRequestOptions,
  ClientResponse,
  createAdminApiClient,
  ReturnData,
} from '@shopify/admin-api-client';
import { CacheNone, type WithCache } from '@shopify/hydrogen';
import { DisplayableError } from 'admin.types';
import { inject, singleton } from 'tsyringe';
import { ADMIN_API_VERSION } from '../core/constants/config';

export type ApiClientRequestParams<Operation extends keyof Operations, Operations extends AllOperations> = [
  operation: Operation,
  options?: ApiClientRequestOptions<Operation, Operations>,
  cache?: CacheOptions,
];
export type ApiClientRequest<Operations extends AllOperations = AllOperations> = <
  TData = undefined,
  Operation extends keyof Operations = string,
>(
  ...params: ApiClientRequestParams<Operation, Operations>
) => Promise<Required<ClientResponse<TData extends undefined ? ReturnData<Operation, Operations> : TData>>['data']>;

export class GQLClientError extends Error {
  constructor(readonly errors: ClientResponse['errors']) {
    let message = errors?.message || 'GQLClientError';

    const lines = errors?.graphQLErrors?.map(err => err.message);
    if (lines?.length) {
      message += `\n${lines.join('\n')}`;
    }

    super(message);
  }
}

export class GQLUserError extends Error {
  constructor(readonly errors: DisplayableError[]) {
    let message = 'GQLUserError:';

    errors?.map(error => (message += `\n${error.field?.join('.')}: ${error.message}`));

    super(message);
  }
}

@singleton()
export class AdminClient {
  private readonly client: AdminApiClient;
  constructor(
    @inject('env') env: Env,
    @inject('withCache') private readonly withCache: WithCache,
  ) {
    this.client = createAdminApiClient({
      apiVersion: ADMIN_API_VERSION,
      accessToken: env.SHOPIFY_OFFLINE_ADMIN_ACCESS_TOKEN,
      storeDomain: env.PUBLIC_STORE_DOMAIN,
    });
  }

  get config() {
    return this.client.config;
  }

  request: ApiClientRequest<AdminQueries & AdminMutations> = async (operation, options, cache) =>
    this.withCache.run(
      {
        cacheStrategy: cache?.strategy || CacheNone(),
        cacheKey: cache?.key || [],
        shouldCacheResult: () => !!cache?.key && !!cache?.strategy,
      },
      async () => {
        const request = await this.client.request(operation, options);

        if (request?.errors) throw new GQLClientError(request?.errors);
        if (!request.data) throw new GQLClientError({ message: 'Missing data' });

        return request.data;
      },
    );
}

export function validateGQLUserError(errors?: DisplayableError[]) {
  if (errors?.length) throw new GQLUserError(errors);

  return true;
}
