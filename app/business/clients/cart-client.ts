import type { HydrogenCart } from '@shopify/hydrogen';
import { injectable, Lifecycle, scoped } from 'tsyringe';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class CartClient implements HydrogenCart {
  get: HydrogenCart['get'];
  getCartId: HydrogenCart['getCartId'];
  setCartId: HydrogenCart['setCartId'];
  create: HydrogenCart['create'];
  addLines: HydrogenCart['addLines'];
  updateLines: HydrogenCart['updateLines'];
  removeLines: HydrogenCart['removeLines'];
  updateDiscountCodes: HydrogenCart['updateDiscountCodes'];
  updateGiftCardCodes: HydrogenCart['updateGiftCardCodes'];
  updateBuyerIdentity: HydrogenCart['updateBuyerIdentity'];
  updateNote: HydrogenCart['updateNote'];
  updateSelectedDeliveryOption: HydrogenCart['updateSelectedDeliveryOption'];
  updateAttributes: HydrogenCart['updateAttributes'];
  setMetafields: HydrogenCart['setMetafields'];
  deleteMetafield: HydrogenCart['deleteMetafield'];
  addDeliveryAddresses: HydrogenCart['addDeliveryAddresses'];
  removeDeliveryAddresses: HydrogenCart['removeDeliveryAddresses'];
  updateDeliveryAddresses: HydrogenCart['updateDeliveryAddresses'];

  constructor({
    get,
    getCartId,
    setCartId,
    create,
    addLines,
    updateLines,
    removeLines,
    updateDiscountCodes,
    updateGiftCardCodes,
    updateBuyerIdentity,
    updateNote,
    updateSelectedDeliveryOption,
    updateAttributes,
    setMetafields,
    deleteMetafield,
    addDeliveryAddresses,
    removeDeliveryAddresses,
    updateDeliveryAddresses,
  }: HydrogenCart) {
    this.get = get;
    this.getCartId = getCartId;
    this.setCartId = setCartId;
    this.create = create;
    this.addLines = addLines;
    this.updateLines = updateLines;
    this.removeLines = removeLines;
    this.updateDiscountCodes = updateDiscountCodes;
    this.updateGiftCardCodes = updateGiftCardCodes;
    this.updateBuyerIdentity = updateBuyerIdentity;
    this.updateNote = updateNote;
    this.updateSelectedDeliveryOption = updateSelectedDeliveryOption;
    this.updateAttributes = updateAttributes;
    this.setMetafields = setMetafields;
    this.deleteMetafield = deleteMetafield;
    this.addDeliveryAddresses = addDeliveryAddresses;
    this.removeDeliveryAddresses = removeDeliveryAddresses;
    this.updateDeliveryAddresses = updateDeliveryAddresses;
  }
}
