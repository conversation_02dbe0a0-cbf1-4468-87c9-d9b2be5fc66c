import type { CustomerAccount } from '@shopify/hydrogen';
import { injectable, Lifecycle, scoped } from 'tsyringe';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class CustomerAccountClient implements CustomerAccount {
  getBuyer: CustomerAccount['getBuyer'];
  setBuyer: CustomerAccount['setBuyer'];
  UNSTABLE_getBuyer: CustomerAccount['UNSTABLE_getBuyer'];
  UNSTABLE_setBuyer: CustomerAccount['UNSTABLE_setBuyer'];
  authorize: CustomerAccount['authorize'];
  getAccessToken: CustomerAccount['getAccessToken'];
  getApiUrl: CustomerAccount['getApiUrl'];
  handleAuthStatus: CustomerAccount['handleAuthStatus'];
  isLoggedIn: CustomerAccount['isLoggedIn'];
  login: CustomerAccount['login'];
  logout: CustomerAccount['logout'];
  mutate: CustomerAccount['mutate'];
  query: CustomerAccount['query'];

  constructor({
    getBuyer,
    setBuyer,
    UNSTABLE_getBuyer,
    UNSTABLE_setBuyer,
    authorize,
    getAccessToken,
    getApiUrl,
    handleAuthStatus,
    isLoggedIn,
    login,
    logout,
    mutate,
    query,
  }: CustomerAccount) {
    this.getBuyer = getBuyer;
    this.setBuyer = setBuyer;
    this.UNSTABLE_getBuyer = UNSTABLE_getBuyer;
    this.UNSTABLE_setBuyer = UNSTABLE_setBuyer;
    this.authorize = authorize;
    this.getAccessToken = getAccessToken;
    this.getApiUrl = getApiUrl;
    this.handleAuthStatus = handleAuthStatus;
    this.isLoggedIn = isLoggedIn;
    this.login = login;
    this.logout = logout;
    this.mutate = mutate;
    this.query = query;
  }
}
