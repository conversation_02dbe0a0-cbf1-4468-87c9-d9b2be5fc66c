import { inject, singleton } from 'tsyringe';

const BLOCK_SIZE = 16;

export interface MultipassCustomer {
  sso_token?: boolean;
  email: string;
  created_at?: string;
  nonce?: string;
  first_name?: string;
  last_name?: string;
  tag_string?: string;
  identifier?: string;
  remote_ip?: string;
  return_to?: string;
  addresses?: {
    address1?: string;
    city?: string;
    country?: string;
    first_name?: string;
    last_name?: string;
    phone?: string;
    province?: string;
    zip?: string;
    province_code?: string;
    country_code?: string;
    default?: boolean;
  }[];
}

@singleton()
export class MultipassClient {
  private _encryptionKey: Uint8Array | null = null;
  private _signingKey: Uint8Array | null = null;
  private readonly secret: string;

  constructor(@inject('env') env: Env) {
    this.secret = env.MULTIPASS_SECRET;

    if (!(typeof this.secret == 'string' && this.secret.length > 0)) {
      throw new Error('Invalid Secret');
    }
  }

  private async generateKeys(): Promise<void> {
    const encoder = new TextEncoder();
    const data = encoder.encode(this.secret);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);

    const hashArray = new Uint8Array(hashBuffer);
    this._encryptionKey = hashArray.slice(0, 16);
    this._signingKey = hashArray.slice(16, 32);
  }

  async encode(obj: MultipassCustomer) {
    if (!obj) return;

    // Ensure keys are generated before encoding
    if (!this._encryptionKey || !this._signingKey) {
      await this.generateKeys();
    }

    // Store the current time in ISO8601 format.
    obj.created_at ||= new Date().toISOString();

    // Serialize the customer data to JSON and encrypt it
    const cipherText = await this.encrypt(JSON.stringify(obj));

    // Create a signature (message authentication code) of the ciphertext
    const signature = await this.sign(cipherText);

    // Concatenate the ciphertext and signature
    const tokenBuffer = new Uint8Array(cipherText.byteLength + signature.byteLength);
    tokenBuffer.set(new Uint8Array(cipherText), 0);
    tokenBuffer.set(new Uint8Array(signature), cipherText.byteLength);

    // Encode everything using URL-safe Base64 (RFC 4648)
    let token = btoa(String.fromCharCode(...tokenBuffer));
    token = token
      .replace(/\+/g, '-') // Replace + with -
      .replace(/\//g, '_'); // Replace / with _

    return token;
  }

  async decode(token: string): Promise<MultipassCustomer | null> {
    if (!token) return null;

    // Ensure keys are generated before decoding
    if (!this._encryptionKey || !this._signingKey) {
      await this.generateKeys();
    }

    // Decode the URL-safe Base64 token
    const tokenBufferString = token
      .replace(/-/g, '+') // Replace - with +
      .replace(/_/g, '/'); // Replace _ with /
    const tokenBuffer = new Uint8Array(
      atob(tokenBufferString)
        .split('')
        .map(c => c.charCodeAt(0)),
    );

    // Verify token buffer length
    if (tokenBuffer.byteLength <= BLOCK_SIZE) {
      return null; // Token is too short to contain IV and signature
    }

    // Split the token buffer into ciphertext and signature
    const signature = tokenBuffer.slice(-32); // Signature is 32 bytes (SHA-256 HMAC)
    const ciphertextWithIv = tokenBuffer.slice(0, -32);

    // Verify the signature
    const isValidSignature = await this.verify(ciphertextWithIv, signature);
    if (!isValidSignature) {
      return null; // Signature verification failed
    }

    // Decrypt the ciphertext
    const plaintext = await this.decrypt(ciphertextWithIv);
    if (!plaintext) {
      return null; // Decryption failed
    }

    // Parse the plaintext JSON to MultipassCustomer object
    try {
      const customer = JSON.parse(plaintext) as MultipassCustomer;
      return customer;
    } catch (e) {
      return null; // JSON parsing failed
    }
  }

  private async encrypt(plaintext: string) {
    if (!this._encryptionKey) {
      throw new Error('Encryption key not available. Call generateKeys() first.');
    }
    const encoder = new TextEncoder();
    const encodedPlaintext = encoder.encode(plaintext);

    const iv = crypto.getRandomValues(new Uint8Array(BLOCK_SIZE));

    const algorithm = { name: 'AES-CBC', iv };
    const key = await crypto.subtle.importKey('raw', this._encryptionKey, algorithm, false, ['encrypt']);

    const encrypted = await crypto.subtle.encrypt(algorithm, key, encodedPlaintext);

    // Concatenate the IV and ciphertext
    const result = new Uint8Array(iv.byteLength + encrypted.byteLength);
    result.set(iv, 0);
    result.set(new Uint8Array(encrypted), iv.byteLength);
    return result;
  }
  private async decrypt(ciphertextWithIv: Uint8Array): Promise<string | null> {
    if (!this._encryptionKey) {
      throw new Error('Encryption key not available. Call generateKeys() first.');
    }

    // Extract IV and ciphertext
    const iv = ciphertextWithIv.slice(0, BLOCK_SIZE);
    const ciphertext = ciphertextWithIv.slice(BLOCK_SIZE);

    if (ciphertext.byteLength === 0) {
      return null; // Ciphertext is empty
    }

    const algorithm = { name: 'AES-CBC', iv };
    const key = await crypto.subtle.importKey('raw', this._encryptionKey, algorithm, false, ['decrypt']);

    try {
      const decrypted = await crypto.subtle.decrypt(algorithm, key, ciphertext);
      const decoder = new TextDecoder();
      const plaintext = decoder.decode(decrypted);
      return plaintext;
    } catch (e) {
      return null; // Decryption failed
    }
  }

  private async sign(data: Uint8Array) {
    const algorithm = { name: 'HMAC', hash: 'SHA-256' };
    const key = await crypto.subtle.importKey('raw', this._signingKey!, algorithm, false, ['sign']);

    const signature = await crypto.subtle.sign(algorithm, key, data);

    return signature;
  }
  private async verify(data: Uint8Array, signature: Uint8Array): Promise<boolean> {
    if (!this._signingKey) {
      throw new Error('Signing key not available. Call generateKeys() first.');
    }
    const algorithm = { name: 'HMAC', hash: 'SHA-256' };
    const key = await crypto.subtle.importKey('raw', this._signingKey!, algorithm, false, ['verify']);

    try {
      return await crypto.subtle.verify(algorithm, key, signature, data);
    } catch (e) {
      return false; // Verification failed, possibly due to key mismatch or data corruption
    }
  }
}
