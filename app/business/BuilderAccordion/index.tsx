import { Accordion, AccordionItem, AccordionContent, AccordionTrigger } from '@/components/ui/accordion';
import { cn } from '@/lib/utils';
import { Blocks, BuilderBlock, isEditing } from '@builder.io/sdk-react/edge';

interface IProps {
  openAllItems: boolean;
  items: { title: string; itemBlocks: BuilderBlock[] }[];
  builderBlock: BuilderBlock;
}

export default function BuilderAccordion({ openAllItems, items, builderBlock }: IProps) {
  const openAllItemsInEditor = isEditing() && openAllItems;
  const itemsWithIds = items.map((item, idx) => ({ ...item, id: idx }));
  const allValues = itemsWithIds.map(item => item.id.toString());

  if (openAllItemsInEditor) {
    return (
      <Accordion type="multiple" defaultValue={allValues}>
        {itemsWithIds.map((item, idx) => (
          <AccordionItem key={item.title} value={item.id.toString()}>
            <AccordionTrigger>{item.title}</AccordionTrigger>
            <AccordionContent>
              <Blocks parent={builderBlock?.id} path={`items.${idx}.itemBlocks`} blocks={item.itemBlocks} />
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    );
  }

  return (
    <Accordion type="single" collapsible defaultValue={itemsWithIds[0].id.toString()}>
      {itemsWithIds.map((item, idx) => (
        <AccordionItem
          key={item.title}
          value={item.id.toString()}
          className={cn(idx === itemsWithIds.length - 1 && 'border-none')}
        >
          <AccordionTrigger>{item.title}</AccordionTrigger>
          <AccordionContent>
            <Blocks parent={builderBlock?.id} path={`items.${idx}.itemBlocks`} blocks={item.itemBlocks} />
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
