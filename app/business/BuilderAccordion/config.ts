import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import BuilderAccordion from '.';

export const BUILDER_ACCORDION_COMPONENT: RegisteredComponent = {
  name: 'Accordion',
  component: BuilderAccordion,
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  inputs: [
    {
      name: 'openAllItems',
      type: 'boolean',
      helperText:
        'In order to be able to edit all the items in the editor, you need to set this to true, otherwise they are not visible. This has no effect on the live page.',
      defaultValue: false,
    },
    {
      name: 'items',
      type: 'list',
      defaultValue: [
        {
          title: 'New Item Title',
          itemBlocks: [],
        },
      ],
      subFields: [
        {
          name: 'title',
          type: 'string',
          defaultValue: 'New Item Title',
        },
        {
          name: 'itemBlocks',
          type: 'uiBlocks',
          hideFromUI: true,
          defaultValue: [],
        },
      ],
    },
  ],
};
