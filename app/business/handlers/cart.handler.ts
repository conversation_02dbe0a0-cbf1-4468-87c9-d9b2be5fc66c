import { CartClient } from '@/business/clients/cart-client';
import { convertDevicesToFeatureQuantities } from '@/lib/utils';
import { CartActionInput, CartForm, CartQueryDataReturn } from '@shopify/hydrogen';
import { data, redirect } from 'react-router';
import { CartApiQueryFragment } from 'storefront.generated';
import { CartBuyerIdentityInput, CurrencyCode, MoneyV2 } from 'storefront.types';
import { inject, injectable, Lifecycle, scoped } from 'tsyringe';
import { CustomerAccountClient } from '../clients/accounts-client';
import {
  BUSINESS_ONLY_ERROR_CODE,
  COMPANY_LOCATION_ID_ATTRIBUTE_KEY,
  MAX_DEVICE_COUNT_ERROR_CODE,
  NEW_SYSTEM_KEY_ATTRIBUTE_VALUE,
  ORDERING_CUSTOMER_ID_ATTRIBUTE_KEY,
  SYSTEM_KEY_ATTRIBUTE_KEY,
  SYSTEM_NAME_ATTRIBUTE_KEY,
  SYSTEM_TYPE_ATTRIBUTE_KEY,
} from '../core/constants/cart';
import { SYSTEM_FEATURES } from '../core/constants/features';
import { ADCRepo } from '../core/repositories/adc';
import { CustomerRepo } from '../core/repositories/customer';
import { DeviceRepo } from '../core/repositories/device';
import DiscountRepo from '../core/repositories/discount';
import { ServiceRepo } from '../core/repositories/service';
import { SystemRepo } from '../core/repositories/system';
import { SystemFeatureHandle } from '../core/types';
import { AugmentedCart } from '../core/types/cart';
import { SystemDevice } from '../core/types/device';
import { AuthHandler } from './auth.handler';
import { ProductHandler } from './product.handler';
import { CartSystemView } from './types/cart';
import { System } from '../core/types/system';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class CartHandler {
  constructor(
    @inject('request') private readonly request: Request,
    private readonly cart: CartClient,
    private readonly authHandler: AuthHandler,
    private readonly customerAccountClient: CustomerAccountClient,
    private readonly customerRepo: CustomerRepo,
    private readonly systemRepo: SystemRepo,
    private readonly adcRepo: ADCRepo,
    private readonly discountRepo: DiscountRepo,
    private readonly serviceRepo: ServiceRepo,
    private readonly deviceRepo: DeviceRepo,
  ) {}

  async checkoutCart(systemType: SystemType, skipReauthenticate = false) {
    const cart = await this.cart.get();

    if (!cart?.lines?.nodes?.length) throw new Error('Cart does not exist.');

    const cartSystemKey = cart.attributes?.find(attribute => attribute.key === SYSTEM_KEY_ATTRIBUTE_KEY)?.value;
    const cartSystemType = cart.attributes?.find(attribute => attribute.key === SYSTEM_TYPE_ATTRIBUTE_KEY)
      ?.value! as SystemType;
    const cartSystemName = cart.attributes?.find(attribute => attribute.key === SYSTEM_NAME_ATTRIBUTE_KEY)?.value;

    if (!cartSystemKey) throw new Error('System not selected.');
    if (cartSystemType != systemType) throw new Error('System type does not match cart system type.');

    const buyerIdentity: CartBuyerIdentityInput = {
      customerAccessToken: (await this.customerAccountClient.getAccessToken()) || null,
    };
    const customerInfo = await this.authHandler.queryCustomerInfo();

    let locationId: string | undefined = undefined;

    let system: CartSystemView | undefined = undefined;

    if (systemType == 'business') {
      if (!customerInfo) throw redirect('/business/checkout');

      const businesses = await this.getSystemsForBusinessCart(customerInfo.id);

      //If assigned to no locations
      if (businesses.every(b => b?.roleAssignments?.length === 0)) throw redirect('/business/checkout');

      if (cartSystemKey == NEW_SYSTEM_KEY_ATTRIBUTE_VALUE) {
        locationId = businesses?.[0]?.roleAssignments?.[0]?.companyLocation?.id;
      } else {
        const business = businesses.find(b => b.systems.some(s => s.systemKey == cartSystemKey && s.locationId));

        if (!business) throw new Error('System not accessible to customer.');

        system = business.systems.find(s => s.systemKey == cartSystemKey);
        locationId = system?.locationId!;

        if (!locationId) throw new Error('System not accessible to customer.');
      }

      buyerIdentity.companyLocationId = locationId;
    } else if (customerInfo?.id) {
      const systems = await this.getOwnerSystems(customerInfo.id);

      system = systems.find(s => s.systemKey == cartSystemKey);

      if (!system) throw new Error('System not accessible to customer.');
    }

    if (system?.requiresMigration) throw redirect('/migration');

    await this.cart.updateAttributes(
      [
        {
          key: SYSTEM_KEY_ATTRIBUTE_KEY,
          value: cartSystemKey,
        },
        {
          key: SYSTEM_TYPE_ATTRIBUTE_KEY,
          value: systemType,
        },
        cartSystemKey == NEW_SYSTEM_KEY_ATTRIBUTE_VALUE && cartSystemName
          ? {
              key: SYSTEM_NAME_ATTRIBUTE_KEY,
              value: cartSystemName,
            }
          : null,
        customerInfo?.id
          ? {
              key: ORDERING_CUSTOMER_ID_ATTRIBUTE_KEY,
              value: customerInfo.id,
            }
          : null,
        locationId
          ? {
              key: COMPANY_LOCATION_ID_ATTRIBUTE_KEY,
              value: locationId,
            }
          : null,
      ].filter(Boolean),
    );
    const result = await this.cart.updateBuyerIdentity(buyerIdentity);

    // If the customer is not logged in, or if this method was called from the checkout callback, redirect to the checkout URL
    if (skipReauthenticate || !customerInfo?.email) {
      const checkoutURL = new URL(result?.cart?.checkoutUrl);

      checkoutURL.searchParams.append('logged_in', customerInfo?.email ? 'true' : 'false');

      return redirect(checkoutURL.toString());
    }

    // Otherwise, redirect to the login page with the checkout callback parameter
    // This will cause the loader to redirect to the checkout URL after login
    // This is used to skip the re-authentication step when the customer has already logged in
    const queryParams = new URLSearchParams({
      return_to: `/share-b2b-auth?${new URLSearchParams({
        return_to: `${systemType == 'business' ? '/business' : '/home'}/cart?checkout=true`,
      })}`,
    });

    return redirect(`/account/login?${queryParams.toString()}`);
  }

  async getOwnerSystems(ownerId: string): Promise<CartSystemView[]> {
    const systemSetups = await this.systemRepo.getAllSystems(ownerId);

    const systems: CartSystemView[] = await Promise.all(
      systemSetups.map(setup =>
        this.adcRepo.getCustomer(setup.adcCustomerId).then(adcCustomer => ({
          systemKey: setup.key,
          name: adcCustomer.unitDescription!,
          locationId: setup.locationId,
          subscriptionId: setup.subscriptionId,
          adcCustomerId: setup.adcCustomerId,
          ownerId,
          requiresMigration: setup.requiresMigration,
        })),
      ),
    );

    return systems;
  }

  async getSystemsForBusinessCart(customerId: string) {
    const contacts = (await this.customerRepo.getCustomerCompanies(customerId)) || [];

    const entries = await Promise.all(
      contacts.map(async contact => {
        const [roleAssignments, systems] = await Promise.all([
          this.customerRepo.getContactRoleAssignments(contact.id),
          this.getOwnerSystems(contact.company.id),
        ]);

        return {
          contact,
          roleAssignments,
          systems: systems.filter(system =>
            roleAssignments.some(assignment => system.locationId == assignment.companyLocation.id),
          ),
        };
      }),
    );

    return entries;
  }

  async handleAction(type: SystemType) {
    const { request, cart } = this;

    const formData = await request.formData();

    if (formData.get('checkout') == 'true') {
      return await this.checkoutCart(type);
    }

    const payload = JSON.parse(formData.get('cartFormInput') as string) as
      | CartActionInput
      | CartActionInput[]
      | undefined;

    if (!payload) {
      throw new Error('Missing cart payload!');
    }

    const actions = payload instanceof Array ? payload : [payload];

    if (!actions?.length) {
      throw new Error('Missing actions!');
    }

    // console.log('CART_ID:', cart.getCartId());
    let result: CartQueryDataReturn | undefined = undefined;
    for await (const { action, inputs } of actions) {
      if (!action) {
        throw new Error('No action provided');
      }

      console.log(action, JSON.stringify(inputs));

      switch (action) {
        case CartForm.ACTIONS.LinesAdd:
          result = await cart.addLines(inputs.lines);
          break;
        case CartForm.ACTIONS.LinesUpdate:
          result = await cart.updateLines(inputs.lines);
          break;
        case CartForm.ACTIONS.LinesRemove:
          result = await cart.removeLines(inputs.lineIds);
          break;
        case CartForm.ACTIONS.AttributesUpdateInput:
          result = await cart.updateAttributes(inputs.attributes);
          break;
        case CartForm.ACTIONS.DiscountCodesUpdate: {
          const formDiscountCode = inputs.discountCode;

          // User inputted discount code
          const discountCodes = (formDiscountCode ? [formDiscountCode] : []) as string[];

          // Combine discount codes already applied on cart
          discountCodes.push(...inputs.discountCodes);

          result = await cart.updateDiscountCodes(discountCodes);
          break;
        }
        case CartForm.ACTIONS.GiftCardCodesUpdate: {
          const formGiftCardCode = inputs.giftCardCode;

          // User inputted gift card code
          const giftCardCodes = (formGiftCardCode ? [formGiftCardCode] : []) as string[];

          // Combine gift card codes already applied on cart
          giftCardCodes.push(...inputs.giftCardCodes);

          result = await cart.updateGiftCardCodes(giftCardCodes);
          break;
        }
        case CartForm.ACTIONS.BuyerIdentityUpdate: {
          result = await cart.updateBuyerIdentity({
            ...inputs.buyerIdentity,
          });

          break;
        }
        default:
          throw new Error(`${action} cart action is not defined`);
      }
    }
    // console.log(JSON.stringify(result));

    const cartId = result?.cart?.id;
    const headers = cartId ? cart.setCartId(cartId) : new Headers();
    const { cart: cartResult, errors } = result ?? {};

    let status = 200;
    const redirectTo = formData.get('redirectTo') ?? null;
    if (typeof redirectTo === 'string') {
      status = 303;
      headers.set('Location', redirectTo);
    }

    return data(
      {
        cart: cartResult,
        errors,
        analytics: {
          cartId,
        },
      },
      { status, headers },
    );
  }

  async getSubscription(deviceHandle: string | undefined, quantity: number): Promise<MoneyV2> {
    if (!deviceHandle) return { currencyCode: CurrencyCode.USD, amount: '0.00' };

    const lineItems = await this.serviceRepo.calculateLineItemsFromFeatureQuantities({
      [deviceHandle ?? '']: quantity,
    });
    const serviceProducts = await this.serviceRepo.getServiceProductsByPriceId();
    const priceId = lineItems[0]?.price;
    const { monthlyRate } = serviceProducts[priceId] || {};

    if (!monthlyRate) return { currencyCode: CurrencyCode.USD, amount: '0.00' };

    return { currencyCode: CurrencyCode.USD, amount: monthlyRate.toString() };
  }

  private async getCartTotals(cart: CartApiQueryFragment) {
    const totalBeforeSavings = cart?.lines?.nodes?.reduce(
      (acc, node) => acc + Number(node?.cost?.amountPerQuantity?.amount ?? 0) * (node?.quantity ?? 0),
      0,
    );
    const totalAfterSavings = Number(cart.cost?.subtotalAmount?.amount || 0);

    return {
      totalBeforeSavings: { currencyCode: CurrencyCode.USD, amount: totalBeforeSavings.toString() },
      totalSavings: { currencyCode: CurrencyCode.USD, amount: (totalBeforeSavings - totalAfterSavings).toString() },
      subTotal: { currencyCode: CurrencyCode.USD, amount: totalAfterSavings.toString() },
    };
  }

  private async validateCartLines(cart: CartApiQueryFragment, isBusiness: boolean) {
    const ownerId = isBusiness ? cart.buyerIdentity.purchasingCompany?.company.id : cart.buyerIdentity.customer?.id;
    const systemId = cart.attributes?.find(attribute => attribute.key === SYSTEM_KEY_ATTRIBUTE_KEY)?.value;

    let existingDevices: SystemDevice[] = [];

    if (ownerId) {
      const devices = await this.deviceRepo.getDevices(ownerId);

      if (systemId === 'new') {
        existingDevices = [];
      } else {
        existingDevices = devices.filter(device => device.systemKey === systemId);
      }
    }

    const existingFeatures = convertDevicesToFeatureQuantities(existingDevices);

    const nodes = await Promise.all(
      cart.lines.nodes.map(async line => {
        const deviceHandle = line.merchandise.product?.deviceHandle?.value;
        const { limit, isBusinessOnly } = SYSTEM_FEATURES[deviceHandle as SystemFeatureHandle] || {};
        const existingFeatureCount = existingFeatures[deviceHandle as SystemFeatureHandle] || 0;

        let error;

        if (limit && limit < existingFeatureCount + line.quantity) {
          error = {
            message: 'Exceed max number of this device added for this system.',
            description: `The max number of this device for this system is ${limit}. If you need more devices, please contact support for a custom account setup.`,
            errorCode: MAX_DEVICE_COUNT_ERROR_CODE,
            link: 'https://adc.com/support',
          };
        }

        if (isBusinessOnly && !isBusiness) {
          error = {
            message: 'This device is not available for personal accounts.',
            description:
              'This is a business only device. Please remove this device from your cart to continue. Please contact support if you have any questions."',
            errorCode: BUSINESS_ONLY_ERROR_CODE,
            link: 'https://adc.com/support',
          };
        }

        const [discounts, subscription] = await Promise.all([
          this.discountRepo.findDiscountsToAdvertise(
            line.merchandise.product.advertisedDiscountSearchTerm?.value,
            line.merchandise.product.id,
          ),
          this.getSubscription(line.merchandise.product.deviceHandle?.value, line.quantity),
        ]);

        return {
          ...line,
          discounts,
          subscription,
          merchandise: {
            ...line.merchandise,
          },
          error,
        };
      }),
    );

    return nodes;
  }

  async getCartForDisplay(isBusiness: boolean): Promise<AugmentedCart> {
    const cart = (await this.cart.get()) as CartApiQueryFragment;

    if (!cart?.lines?.nodes?.length) return cart as unknown as AugmentedCart;

    const [nodes, totals] = await Promise.all([this.validateCartLines(cart, isBusiness), this.getCartTotals(cart)]);

    return {
      ...cart,
      cost: {
        ...cart.cost,
        ...totals,
      },
      lines: {
        nodes,
      },
    } as unknown as AugmentedCart;
  }
}
