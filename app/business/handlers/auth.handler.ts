import { CustomerAccountClient } from '@/business/clients/accounts-client';
import { MultipassClient, MultipassCustomer } from '@/business/clients/multipass-client';
import { SendgridClient } from '@/business/clients/sendgrid-client';
import { SessionClient } from '@/business/clients/session-client';
import { CustomerRepo } from '@/business/core/repositories/customer';
import { redirect } from 'react-router';
import { base64url, JWK, jwtVerify, SignJWT } from 'jose';
import { inject, injectable, Lifecycle, scoped } from 'tsyringe';
import { CUSTOMER_ACCOUNT_ID_QUERY } from '../customer-account/customer';

// OTP constants
export const GENERIC_ERROR = 'INVALID_REQUEST';
export const OTP_TTL_MS = 15 * 60 * 1000; // 15 Minutes in milliseconds
export const SSO_TTL_MS = 1 * 60 * 60 * 1000; // 1 hour
export const CODE_TTL_MS = 5 * 60 * 1000; // 5 minutes
export const ACCESS_TOKEN_TTL_S = 24 * 60 * 60; // 24 hours
export const REFRESH_TOKEN_TTL_S = 30 * 24 * 60 * 60; // 30 days

export interface OIDCAuthParams {
  nonce: string;
  state: string;
  clientId: string;
  redirectUrl: string;
  responseType: string;
  scope: string;
}

export interface OIDCTokenParams {
  code?: string;
  refreshToken?: string;
  redirectUri?: string;
  grantType: string;
  authorization: string;
}

export interface OIDCTokenResponse {
  access_token: string;
  id_token: string;
  token_type: string;
  scope: string;
  expires_in: number;
  refresh_token?: string;
}

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class AuthHandler {
  constructor(
    @inject('request') private readonly request: Request,
    private readonly session: SessionClient,
    private readonly customerAccountClient: CustomerAccountClient,
    private readonly multipassClient: MultipassClient,
    private readonly sendgridClient: SendgridClient,
    private readonly customerRepo: CustomerRepo,
    @inject('env') private readonly env: Env,
  ) {}

  // Session management methods
  setOIDCSession(data: Partial<OIDCState>): void {
    this.session.set('OIDC', data);
  }

  getOIDCSession(): OIDCState {
    return this.session.get('OIDC') || {};
  }

  clearOIDCSession(): void {
    this.session.unset('OIDC');
  }

  async queryCustomerInfo() {
    try {
      if (!(await this.customerAccountClient.isLoggedIn())) return null;

      const query = await this.customerAccountClient.query(CUSTOMER_ACCOUNT_ID_QUERY);

      const id = query?.data?.customer?.id;
      const email = query?.data?.customer?.emailAddress?.emailAddress;

      return id ? { id, email: email! } : null;
    } catch (error) {
      return null;
    }
  }

  async getLoggedInCustomerId<Redirect extends true | false | undefined>({
    redirectToLogin,
    requiredCustomerId,
    requiredEmail,
    returnUrl,
  }: {
    redirectToLogin?: Redirect;
    requiredCustomerId?: string;
    requiredEmail?: string;
    returnUrl?: string;
  } = {}): Promise<Redirect extends true ? string : string | undefined> {
    const customer = await this.queryCustomerInfo();

    if (
      !customer ||
      (requiredCustomerId && requiredCustomerId != customer.id) ||
      (requiredEmail && requiredEmail != customer.email)
    ) {
      this.session.unset('customerAccount');

      if (!redirectToLogin) return undefined!;

      const query: Record<string, string> = {};

      const { pathname } = new URL(this.request.url);
      query.return_to = returnUrl || pathname;

      if (requiredEmail) {
        this.setOIDCSession({ email: requiredEmail });
      } else {
        this.clearOIDCSession();
      }

      throw redirect(`/account/login?${new URLSearchParams(query)}`);
    }

    return customer.id;
  }

  // OIDC Methods
  private static assert(pass: unknown, warning?: string) {
    if (!pass) {
      console.warn(warning);
      throw new Response(GENERIC_ERROR, { status: 400 });
    }
  }

  /**
   * Generates a hash of a value using SHA-256
   */
  static async generateHashRS256(value: string): Promise<string> {
    const hash = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(value));
    const truncatedHash = hash.slice(0, 16); // First 128 bits (16 bytes)
    const uint8Array = new Uint8Array(truncatedHash);
    return base64url.encode(uint8Array);
  }

  async sendSSOLink(email: string, returnTo?: string) {
    const customerId = await this.customerRepo.getCustomerIdFromEmail(email);

    AuthHandler.assert(customerId, 'Customer not found');

    const customerProfile = await this.customerRepo.getCustomerProfile(customerId!);
    const expiresAt = Date.now() + SSO_TTL_MS;

    const token = await this.multipassClient.encode({
      email,
      return_to: returnTo,
      sso_token: true,
    });

    await this.sendgridClient.sendTemplate(email, this.env.SENDGRID_SSO_TEMPLATE_ID, {
      ...customerProfile,
      login_link: `${this.env.HOST}/sso?token=${token}`,
      expires_at: new Date(expiresAt).toLocaleString(),
    });
  }

  async handleSSOToken(token: string) {
    const payload = await this.multipassClient.decode(token);
    AuthHandler.assert(payload?.sso_token, 'Invalid SSO token');
    AuthHandler.assert(payload?.email, 'Email is missing from SSO token');
    AuthHandler.assert(Date.now() < new Date(payload?.created_at || 0).getTime() + SSO_TTL_MS, 'SSO token expired');

    const email = payload?.email!;

    this.setOIDCSession({
      email,
      multipassToken: await this.multipassClient.encode({ email }),
    });

    throw redirect(`/account/login?${new URLSearchParams({ return_to: payload?.return_to || '/account' })}`);
  }

  /**
   * Finishes the login process by generating a code and redirecting to the callback URL
   */
  async finishOIDCLogin(
    payload: MultipassCustomer,
    { nonce, state, redirectUrl }: Pick<OIDCAuthParams, 'nonce' | 'state' | 'redirectUrl'>,
  ) {
    AuthHandler.assert(!payload?.sso_token, 'Token is an SSO token not an OIDC token');
    AuthHandler.assert(Date.now() < new Date(payload.created_at || 0).getTime() + CODE_TTL_MS, 'Code expired');

    const fullPayload = { ...payload, nonce };
    const code = (await this.multipassClient.encode(fullPayload)) || '';

    const redirect_uri = new URL(redirectUrl);
    redirect_uri.searchParams.set('code', code);
    redirect_uri.searchParams.set('state', state);

    this.setOIDCSession({ email: payload.email });

    throw redirect(redirect_uri.toString());
  }

  /**
   * Generates a one-time password for authentication
   */
  static generateOTP(length: number): string {
    let OTP = '';
    for (let i = 0; i < length; i++) {
      const character = Math.min(Math.max(Math.floor(Math.random() * 10), 0), 9).toString()[0];
      OTP += character;
    }
    return OTP;
  }

  /**
   * Signs data with HMAC SHA-256
   */
  static async signData(data: string, secret: string): Promise<string> {
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey('raw', encoder.encode(secret), { name: 'HMAC', hash: 'SHA-256' }, false, [
      'sign',
    ]);

    const mac = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
    const base64Mac = btoa(String.fromCharCode(...new Uint8Array(mac)));

    return base64Mac;
  }

  /**
   * Verifies signed data with HMAC SHA-256
   */
  static async verifyData(data: string, signature: string, secret: string): Promise<boolean> {
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey('raw', encoder.encode(secret), { name: 'HMAC', hash: 'SHA-256' }, false, [
      'verify',
    ]);

    const hashValueBytes = Uint8Array.from(atob(signature), c => c.charCodeAt(0));
    return await crypto.subtle.verify('HMAC', key, hashValueBytes, encoder.encode(data));
  }

  getPrivateJWK(): JWK {
    const privateJWK = JSON.parse(atob(this.env.OIDC_PRIVATE_JWK_B64)) as JWK;
    AuthHandler.assert(privateJWK?.kid, 'Missing or invalid private JWK');
    return privateJWK;
  }

  getPublicJWKS(): JWK[] {
    const JWKS: any = JSON.parse(atob(this.env.OIDC_PUBLIC_JWKS_B64));
    const keys: JWK[] = [];

    if (JWKS instanceof Array) {
      keys.push(...JWKS);
    } else if (JWKS.keys instanceof Array) {
      keys.push(...JWKS.keys);
    } else if (JWKS.kid) {
      keys.push(JWKS);
    }

    AuthHandler.assert(keys.length > 0, 'No valid JWKs found');
    return keys;
  }

  /**
   * Processes OIDC authentication request
   */
  async handleOIDCAuth({
    nonce,
    state,
    redirectUrl,
    responseType,
    scope,
    clientId,
  }: OIDCAuthParams): Promise<OIDCState & { error?: string }> {
    AuthHandler.assert(nonce && state, 'Missing nonce or state');
    AuthHandler.assert(responseType === 'code', 'Invalid response type.');
    AuthHandler.assert(scope === 'openid email' || scope === 'email openid', 'Invalid scope.');
    AuthHandler.assert(clientId === this.env.OIDC_CLIENT_ID, 'Invalid client ID.');

    const shopID = parseInt(
      /^https:\/\/shopify.com\/(\d+)$/s.exec(this.env.PUBLIC_CUSTOMER_ACCOUNT_API_URL)?.[1] || '0',
    );
    AuthHandler.assert(
      `https://shopify.com/authentication/${shopID}/login/external/callback` === redirectUrl,
      'Invalid redirect URL.',
    );

    // Check if we have a valid multipass token in the session
    const oidc = this.getOIDCSession();
    if (oidc?.multipassToken) {
      const customerPayload = await this.multipassClient.decode(oidc.multipassToken);

      if (!customerPayload?.created_at || !customerPayload?.email) {
        console.warn('Multipass token existed but did not match email and timestamp! Clearing OIDC state...');

        this.clearOIDCSession();

        return {};
      }

      await this.finishOIDCLogin(customerPayload, { nonce, state, redirectUrl });
    }

    // Check if the user is already logged in
    const loggedInCustomer = await this.queryCustomerInfo();
    if (loggedInCustomer?.email) {
      console.warn('Logged in customer found, re-authenticating login...');
      await this.finishOIDCLogin({ email: loggedInCustomer.email }, { nonce, state, redirectUrl });
    }

    // If we have an email but no OTP info yet, send OTP
    if (oidc?.email && (!oidc.signature || !oidc.expiresAt)) {
      return await this.sendOTP(oidc.email);
    }

    return {};
  }

  async sendOTP(email: string, allowNonExistent = false) {
    const customerId = await this.customerRepo.getCustomerIdFromEmail(email);

    if (!customerId && !allowNonExistent) {
      this.clearOIDCSession();
    }

    const customerProfile = customerId ? await this.customerRepo.getCustomerProfile(customerId) : undefined;

    const OTP = AuthHandler.generateOTP(6);

    const expiresAt = Date.now() + OTP_TTL_MS;
    const signature = await AuthHandler.signData(`${email}.${OTP}.${expiresAt}`, this.env.MULTIPASS_SECRET);

    await this.sendgridClient.sendTemplate(email, this.env.SENDGRID_OTP_TEMPLATE_ID, {
      ...customerProfile,
      code: OTP,
      expiresAt: new Date(expiresAt).toLocaleString(),
    });

    console.log(`OTP ${OTP} sent to ${email}`);

    const newOIDC: OIDCState = {
      email,
      signature,
      expiresAt,
    };

    this.setOIDCSession(newOIDC);

    return newOIDC;
  }

  /**
   * Verifies OTP and creates multipass token
   */
  async verifyOTP(otp: string): Promise<{ success: boolean; error?: string }> {
    const oidc = this.getOIDCSession();

    if (!oidc?.email || !oidc?.signature || !oidc?.expiresAt) {
      return { success: false, error: 'One-time passcode was never sent.' };
    }

    if (Date.now() > oidc.expiresAt) {
      this.clearOIDCSession();

      return { success: false, error: 'One-time passcode expired.' };
    }

    const verified = await AuthHandler.verifyData(
      `${oidc.email}.${otp}.${oidc.expiresAt}`,
      oidc.signature,
      this.env.MULTIPASS_SECRET,
    );

    if (!verified) {
      return { success: false, error: 'Invalid one-time passcode.' };
    }

    const multipassToken = await this.multipassClient.encode({ email: oidc.email });
    this.setOIDCSession({ email: oidc.email, multipassToken });

    return { success: true };
  }

  /**
   * Centralized method for generating all OIDC tokens
   *
   * This method creates a consistent set of OIDC tokens (access token, ID token,
   * and refresh token) based on the provided parameters. It abstracts the token
   * generation logic that is common between authorization code flow and refresh token flow.
   *
   * @param {Object} params - Token generation parameters
   * @param {string} params.email - User's email address
   * @param {string} [params.nonce] - Optional nonce value for ID token (required for initial auth)
   * @param {string} [params.tid] - Optional transaction ID to link tokens together
   * @param {string} [params.code] - Optional code for ID token's c_hash claim
   * @returns {Promise<OIDCTokenResponse>} Object containing all generated tokens and metadata
   */
  private async generateTokens({
    email,
    nonce,
    code,
    tid = crypto.randomUUID(),
  }: {
    email: string;
    nonce?: string;
    tid?: string;
    code?: string;
  }): Promise<OIDCTokenResponse> {
    email = email.toLowerCase().trim();
    const subject = base64url.encode(email);
    const privateJWK = this.getPrivateJWK();

    const iat = Math.floor(Date.now() / 1000);
    const exp = iat + ACCESS_TOKEN_TTL_S;

    // Generate access token
    const access_token = await new SignJWT({
      scope: 'openid email',
      tid,
    })
      .setIssuedAt(iat)
      .setExpirationTime(exp)
      .setIssuer(this.env.HOST)
      .setAudience(this.env.OIDC_CLIENT_ID)
      .setJti(crypto.randomUUID())
      .setSubject(subject)
      .setProtectedHeader({ alg: 'RS256', kid: privateJWK.kid })
      .sign(privateJWK);

    const refresh_token = await new SignJWT({
      scope: 'openid email',
      email,
      tid,
      token_type: 'refresh',
    })
      .setIssuedAt(iat)
      .setExpirationTime(iat + REFRESH_TOKEN_TTL_S)
      .setIssuer(this.env.HOST)
      .setAudience(this.env.OIDC_CLIENT_ID)
      .setJti(crypto.randomUUID())
      .setSubject(subject)
      .setProtectedHeader({ alg: 'RS256', kid: privateJWK.kid })
      .sign(privateJWK);

    // Prepare ID token claims
    const idTokenClaims: any = {
      email_verified: true,
      email,
      scope: 'openid email',
      at_hash: await AuthHandler.generateHashRS256(access_token),
      tid,
    };

    // Add optional claims
    if (code) {
      idTokenClaims.c_hash = await AuthHandler.generateHashRS256(code);
    }

    if (nonce) {
      idTokenClaims.nonce = nonce;
    }

    // Generate ID token
    const id_token = await new SignJWT(idTokenClaims)
      .setIssuedAt(iat)
      .setExpirationTime(exp)
      .setIssuer(this.env.HOST)
      .setAudience(this.env.OIDC_CLIENT_ID)
      .setJti(crypto.randomUUID())
      .setSubject(subject)
      .setProtectedHeader({ alg: 'RS256', kid: privateJWK.kid })
      .sign(privateJWK);

    return {
      access_token,
      id_token,
      refresh_token,
      token_type: 'Bearer',
      scope: 'openid email',
      expires_in: ACCESS_TOKEN_TTL_S,
    };
  }

  /**
   * Generates OIDC tokens from an authorization code
   */
  async generateTokensFromCode(code: string): Promise<OIDCTokenResponse> {
    AuthHandler.assert(code, 'No code provided');

    const customerPayload = (await this.multipassClient.decode(code))!;

    AuthHandler.assert(typeof customerPayload === 'object', 'Code is not a valid multipass token');
    const { email, nonce, created_at } = customerPayload;

    AuthHandler.assert(email && nonce && created_at, 'Code does not contain valid customer information');

    const expiresAt = new Date(created_at!).getTime() + CODE_TTL_MS;
    AuthHandler.assert(Date.now() < expiresAt, 'Code expired');

    return this.generateTokens({
      email,
      nonce,
      code,
    });
  }

  /**
   * Generates new tokens from a refresh token
   */
  async generateTokensFromRefreshToken(refreshToken: string): Promise<OIDCTokenResponse> {
    AuthHandler.assert(refreshToken, 'No refresh token provided');

    // First, we need to extract the header to get the key ID (kid)
    const headerPart = refreshToken.split('.')[0];
    const decodedHeader = JSON.parse(atob(headerPart)) as { kid?: string; alg?: string };
    const tokenKid = decodedHeader.kid;
    AuthHandler.assert(tokenKid, 'Refresh token missing key ID (kid)');

    // Find the matching public key from JWKS
    const publicKeys = this.getPublicJWKS();
    const matchingKey = publicKeys.find(key => key.kid === tokenKid)!;
    AuthHandler.assert(matchingKey, `No matching public key found for kid: ${tokenKid}`);

    let payload: any;

    // Use jwtVerify with the matching public key to verify the refresh token
    try {
      ({ payload } = await jwtVerify(refreshToken, matchingKey, {
        issuer: this.env.HOST,
        audience: this.env.OIDC_CLIENT_ID,
      }));
    } catch (error: any) {
      AuthHandler.assert(false, `Invalid refresh token: ${error.code}`);
    }

    // Verify token type
    AuthHandler.assert(payload.token_type === 'refresh', 'Invalid token type');
    AuthHandler.assert(typeof payload.email === 'string', 'Email is missing in refresh token');

    const email = payload.email as string;
    const tid = payload.tid as string | undefined;

    return this.generateTokens({
      email,
      tid,
    });
  }

  /**
   * Processes an OIDC token request
   */
  async handleTokenRequest({
    code,
    refreshToken,
    grantType,
    redirectUri,
    authorization,
  }: OIDCTokenParams): Promise<OIDCTokenResponse> {
    AuthHandler.assert(grantType === 'authorization_code' || grantType === 'refresh_token', 'Unsupported grant type');

    // Validate client credentials
    AuthHandler.assert(
      authorization === `Basic ${btoa(`${this.env.OIDC_CLIENT_ID}:${this.env.OIDC_CLIENT_SECRET}`)}`,
      'Invalid client credentials',
    );

    switch (grantType) {
      case 'authorization_code':
        AuthHandler.assert(redirectUri, 'No redirect URI provided');
        // Validate redirect URL
        const shopID = parseInt(
          /^https:\/\/shopify.com\/(\d+)$/s.exec(this.env.PUBLIC_CUSTOMER_ACCOUNT_API_URL)?.[1] || '0',
        );
        AuthHandler.assert(
          `https://shopify.com/authentication/${shopID}/login/external/callback` === redirectUri,
          'Invalid redirect URL',
        );

        return await this.generateTokensFromCode(code!);
      case 'refresh_token':
        return await this.generateTokensFromRefreshToken(refreshToken!);
      default:
        throw new Error('Unsupported grant type');
    }
  }
}
