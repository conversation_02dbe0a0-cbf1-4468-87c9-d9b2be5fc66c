import { StorefrontClient } from '@/business/clients/storefront-client';
import { injectable, Lifecycle, scoped } from 'tsyringe';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class StorefrontCustomerHandler {
  constructor(private readonly storefront: StorefrontClient) {}
  async getCustomerEmail(customerAccessToken: string) {
    const query = await this.storefront.query(
      `#graphql
                query GetCustomerEmail(
                  $customerAccessToken: String!
                  $country: CountryCode
                  $language: LanguageCode
                ) @inContext(country: $country, language: $language) {
                  customer(customerAccessToken: $customerAccessToken) {
                    email
                  }
                }
              `,
      { variables: { customerAccessToken } },
    );

    return query?.customer?.email;
  }
  async getCustomerID(customerAccessToken: string) {
    const query = await this.storefront.query(
      `#graphql
                query GetCustomerId(
                  $customerAccessToken: String!
                  $country: CountryCode
                  $language: LanguageCode
                ) @inContext(country: $country, language: $language) {
                  customer(customerAccessToken: $customerAccessToken) {
                    id
                  }
                }
              `,
      { variables: { customerAccessToken } },
    );

    return query?.customer?.id;
  }
}
