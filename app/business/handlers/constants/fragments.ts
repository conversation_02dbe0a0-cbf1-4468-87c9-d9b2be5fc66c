// NOTE: https://shopify.dev/docs/api/storefront/latest/queries/cart
export const CART_QUERY_FRAGMENT = `#graphql
  fragment Money on MoneyV2 {
    currencyCode
    amount
  }
  fragment CartProduct on Product {
    handle
    title
    id
    vendor
    deviceHandle: metafield(namespace: "device", key: "handle") {
      value
    }
    advertisedDiscountSearchTerm: metafield(namespace: "marketing", key: "advertised-discount-search-term") {
      value
    }
  }
  fragment CartLine on CartLine {
    id
    quantity
    attributes {
      key
      value
    }
    cost {
      totalAmount {
        ...Money
      }
      subtotalAmount {
        ...Money
      }
      amountPerQuantity {
        ...Money
      }
      compareAtAmountPerQuantity {
        ...Money
      }
    }
    merchandise {
      ... on ProductVariant {
        id
        availableForSale
        compareAtPrice {
          ...Money
        }
        price {
          ...Money
        }
        requiresShipping
        title
        image {
          id
          url
          altText
          width
          height
        }
        product {
          ...CartProduct
        }
        selectedOptions {
          name
          value
        }
      }
    }
  }
  fragment CartLineComponent on ComponentizableCartLine {
    id
    quantity
    attributes {
      key
      value
    }
    cost {
      totalAmount {
        ...Money
      }
      subtotalAmount {
        ...Money
      }
      amountPerQuantity {
        ...Money
      }
      compareAtAmountPerQuantity {
        ...Money
      }
    }
    merchandise {
      ... on ProductVariant {
        id
        availableForSale
        compareAtPrice {
          ...Money
        }
        price {
          ...Money
        }
        requiresShipping
        title
        image {
          id
          url
          altText
          width
          height
        }
        product {
          ...CartProduct
        }
        selectedOptions {
          name
          value
        }
      }
    }
  }
  fragment CartApiQuery on Cart {
    updatedAt
    id
    appliedGiftCards {
      lastCharacters
      amountUsed {
        ...Money
      }
    }
    checkoutUrl
    totalQuantity
    buyerIdentity {
      countryCode
      customer {
        id
        email
        firstName
        lastName
        displayName
      }
      purchasingCompany {
        company {
          id
        }
        location {
          id
        }
      }
      email
      phone
    }
    lines(first: $numCartLines) {
      nodes {
        ...CartLine
      }
      nodes {
        ...CartLineComponent
      }
    }
    cost {
      subtotalAmount {
        ...Money
      }
      totalAmount {
        ...Money
      }
      totalDutyAmount {
        ...Money
      }
      totalTaxAmount {
        ...Money
      }
    }
    note
    attributes {
      key
      value
    }
    discountCodes {
      code
      applicable
    }
  }
` as const;

export const ARTICLE_ITEM = `#graphql
    fragment ArticleItem on Article {
        author: authorV2 {
        name
        }
        contentHtml
        handle
        id
        image {
        id
        altText
        url
        width
        height
        }
        excerptHtml
        publishedAt
        title
        blog {
        handle
        }
    }
` as const;

export const IMAGE_FRAGMENT = `#graphql
  fragment Image on Image {
    altText
    url
    width
    height
    id
  }
`;

export const CUSTOMER_FRAGMENT = `#graphql
fragment Customer on Customer {
  acceptsMarketing
  addresses(first: 6) {
    nodes {
      ...Address
    }
  }
  defaultAddress {
    ...Address
  }
  email
  firstName
  lastName
  numberOfOrders
  phone
}
fragment Address on MailingAddress {
  id
  formatted
  firstName
  lastName
  company
  address1
  address2
  country
  province
  city
  zip
  phone
}
` as const;
