import { OrderRepo } from '@/business/core/repositories/order';
import { ServiceRepo } from '@/business/core/repositories/service';
import { SystemRepo } from '@/business/core/repositories/system';
import { OrderService } from '@/business/core/services/order';
import { convertIdToGid } from '@/lib/utils';
import { data } from 'react-router';
import { inject, injectable, Lifecycle, scoped } from 'tsyringe';
import { SessionClient } from '../clients/session-client';
import { StripeClient } from '../clients/stripe-client';
import { SubscriptionRepo } from '../core/repositories/subscription';
import { OrderSetupFlow } from '../core/types/order';
import { AuthHandler } from './auth.handler';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class StartHandler {
  constructor(
    @inject('env') private readonly env: Env,
    private readonly session: SessionClient,
    private readonly stripeClient: StripeClient,
    private readonly authHandler: AuthHandler,
    private readonly systemRepo: SystemRepo,
    private readonly serviceRepo: ServiceRepo,
    private readonly orderRepo: OrderRepo,
    private readonly orderService: OrderService,
    private readonly subscriptionRepo: SubscriptionRepo,
  ) {}

  async handleStart(orderNumber: string | undefined) {
    const orderId = convertIdToGid('Order', orderNumber);

    if (!orderId) throw new Response('Missing order id!', { status: 400 });

    const orderCustomer = await this.orderRepo.getCustomer(orderId);
    if (!orderCustomer) throw new Response('Order not found!', { status: 404 });

    const loggedInCustomerId = await this.authHandler.getLoggedInCustomerId({
      requiredCustomerId: orderCustomer?.id,
    });

    const checkoutSessionId = this.session.get('stripeCheckoutId');
    const checkoutSession = checkoutSessionId
      ? await this.stripeClient.checkout.sessions.retrieve(checkoutSessionId)
      : undefined;
    if (checkoutSession?.status == 'complete') {
      await this.orderService.trySetSystemIdFromCheckoutSession(checkoutSession, orderId);
      await this.session.unset('stripeCheckoutId');
    }

    const orderState = await this.orderRepo.getSetupState(orderId);
    if (!orderState) throw new Response('Order setup not found!', { status: 404 });

    const system = await this.systemRepo.getSystem(orderState?.ownerId!, orderState?.systemKey!);
    if (!system) throw new Response('System not found!', { status: 404 });

    // if (!orderState.servicePaid && system.subscriptionId) {
    //   throw redirect(`/account?setup_order_id=${orderNumber}`);
    // }

    const products = await this.serviceRepo.getServiceProductsByPriceId();
    const monthlyRates = Object.fromEntries(
      await Promise.all(
        Object.entries(orderState.featureQuantities).map(async ([handle, qty]) => {
          const lineItems = await this.serviceRepo.calculateLineItemsFromFeatureQuantities({ [handle]: qty });
          return [
            handle,
            lineItems.reduce((acc, item) => acc + item.quantity * (products[item.price]?.monthlyRate || 0), 0),
          ] as const;
        }),
      ),
    );

    const maskedEmail = orderCustomer.email
      ?.split('@', 2)
      .map((part, index) => (index == 0 ? part.slice(0, 2) + '*'.repeat(part.length - 2) : part))
      .join('@');

    const flow: OrderSetupFlow = !system.subscriptionId || system.setupOrderId == orderId ? 'subscribe' : 'upgrade';

    return {
      flow,
      loggedInCustomerId,
      orderCustomerId: orderCustomer.id,
      maskedEmail,
      orderState,
      system,
      monthlyRates,
      checkoutClientSecret: checkoutSession?.client_secret,
      stripePublishableKey: checkoutSession?.client_secret ? this.env.STRIPE_PUBLISHABLE_KEY : undefined,
      previewInvoice: system.subscriptionId
        ? await this.subscriptionRepo.previewUpcomingInvoice({ systemSubscriptionId: system.subscriptionId })
        : undefined,
    };
  }

  async validateAccessToOrder(orderId: string) {
    const orderCustomer = await this.orderRepo.getCustomer(orderId);
    const loggedInCustomerId = await this.authHandler.getLoggedInCustomerId();

    if (!orderCustomer?.id || loggedInCustomerId != orderCustomer.id)
      throw data({ error: 'Unauthorized customer' }, 401);
  }

  async quoteUpgradeServiceInvoice(orderNumber: string | undefined) {
    const orderId = convertIdToGid('Order', orderNumber)!;

    await this.validateAccessToOrder(orderId);

    const orderState = await this.orderRepo.getSetupState(orderId);
    if (!orderState) throw new Error('Order setup state not found!');

    const system = await this.systemRepo.getSystem(orderState.ownerId!, orderState.systemKey!);
    if (!system) throw new Error('System not found!');

    if (!system.subscriptionId) throw new Error('System has no subscription!');

    const systemSubscription = await this.subscriptionRepo.getSystemSubscription(system.subscriptionId);

    const changedLines = await this.subscriptionRepo.calculateFeatureQuantityChangeServiceLineItems({
      systemSubscription,
      featuresDelta: orderState.featureQuantities,
    });

    const invoice = await this.subscriptionRepo.previewUpdateSystemLineItems({
      systemSubscription,
      ...changedLines,
    });

    // const paymentMethod = await this.subscriptionRepo.getSystemSubscriptionPaymentMethod(systemSubscription);

    return { invoice };
  }

  async startNewServiceCheckout(orderNumber: string | undefined) {
    const orderId = convertIdToGid('Order', orderNumber)!;

    await this.validateAccessToOrder(orderId);

    const orderState = (await this.orderRepo.getSetupState(orderId))!;
    const system = (await this.systemRepo.getSystem(orderState.ownerId!, orderState.systemKey!))!;
    const ownerId = await this.orderRepo.getPurchasingEntityId(orderId);
    const lineItems = await this.serviceRepo.calculateLineItemsFromFeatureQuantities(orderState.featureQuantities);

    const checkout = await this.subscriptionRepo.newSystemCheckout({
      ownerId,
      orderId,
      lineItems,
      adcCustomerId: system.adcCustomerId,
      systemKey: system.key,
      locationId: system.locationId,
      returnUrl: this.env.HOST + `/start/${orderNumber}`,
    });

    this.session.set('stripeCheckoutId', checkout.id);

    return { checkout };
  }

  async upgradeService(orderNumber: string | undefined, consent: number) {
    const orderId = convertIdToGid('Order', orderNumber)!;

    await this.validateAccessToOrder(orderId);

    if (!consent) return { errors: { consent: { message: 'Please consent to upgrade your service!' } } };

    const now = Date.now();
    if (consent > now || consent < now - 15 * 60 * 1000)
      return { errors: { consent: { message: 'Your consent has expired, please consent and try again!' } } };

    await this.orderService.upgradeAndChargeService(orderId, consent);

    return { success: true };
  }
}
