export type CartSystemView = {
  systemKey: string;
  name: string;
  locationId?: string;
  subscriptionId?: string;
  adcCustomerId?: number;
  ownerId: string;
  //Temporary field to flag systems that need to be migrated from SMM
  requiresMigration?: boolean;
};

export type CartContactProfile = {
  email: string;
  firstName: string;
  lastName: string;
};

export type CartCompanyProfile = {
  name: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
};
