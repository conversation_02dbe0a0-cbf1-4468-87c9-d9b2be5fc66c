import { isPreviewing, fetchOneEntry, getBuilderSearchParams } from '@builder.io/sdk-react/edge';
import { inject, injectable, Lifecycle, scoped } from 'tsyringe';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class BuilderHandler {
  private readonly builderApiKey: string;

  constructor(
    @inject('env') private readonly env: Env,
    @inject('request') private readonly request: Request,
  ) {
    this.builderApiKey = env.PUBLIC_BUILDER_KEY;
  }

  async handlePageFetch({
    request = this.request,
    model = 'page',
    pathnamePrefix = '',
  }: {
    request?: Request;
    model?: string;
    pathnamePrefix?: string;
  }) {
    const url = new URL(request.url);

    if (pathnamePrefix && !url.pathname.startsWith(pathnamePrefix))
      throw new Response('Page Not Found', {
        status: 404,
      });

    const urlPath = url.pathname.slice(pathnamePrefix.length) || '/';

    const page = await fetchOneEntry({
      model,
      apiKey: this.builderApiKey,
      enrich: true,
      options: getBuilderSearchParams(url.searchParams),
      userAttributes: { urlPath },
    });

    if (!page && !isPreviewing(url.search)) {
      throw new Response('Page Not Found', {
        status: 404,
      });
    }

    return page;
  }

  async handleModelFetch({ request = this.request, model }: { request: Request; model: string }) {
    const url = new URL(request.url);
    const pathname = url.pathname;

    const modelRes = await fetchOneEntry({
      model,
      apiKey: this.builderApiKey,
      options: getBuilderSearchParams(url.searchParams),
      userAttributes: { urlPath: pathname },
    });

    return modelRes;
  }

  get PUBLIC_BUILDER_KEY() {
    return this.env.PUBLIC_BUILDER_KEY;
  }
}
