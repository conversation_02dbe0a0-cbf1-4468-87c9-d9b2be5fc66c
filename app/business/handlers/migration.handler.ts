import { injectable, Lifecycle, scoped } from 'tsyringe';
import { AuthHand<PERSON> } from './auth.handler';
import { SystemRepo } from '../core/repositories/system';
import { redirect } from 'react-router';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class MigrationHandler {
  constructor(
    private readonly authHandler: AuthHandler,
    private readonly systemRepo: SystemRepo,
  ) {}

  async checkIfCustomerNeedsToMigrate() {
    const customerId = await this.authHandler.getLoggedInCustomerId();
    if (!customerId) return undefined;

    const systems = await this.systemRepo.getAllSystems(customerId);

    if (systems?.find(system => system.requiresMigration)) throw redirect('/migration');
  }
}
