import { queryAllConnections } from '@/lib/utils';
import { singleton } from 'tsyringe';
import { DeviceTask, SystemDevice } from '../types/device';
import { MetafieldRepo } from './metafield';
import { ShopRepo } from './shop';

@singleton()
export class DeviceRepo {
  constructor(
    private readonly metafieldRepo: MetafieldRepo,
    private readonly shopRepo: ShopRepo,
  ) {}

  async setDevices(ownerId: string, devicesDictionary: Record<string, SystemDevice>): Promise<void>;
  async setDevices(ownerId: string, deviceArray: SystemDevice[]): Promise<void>;
  async setDevices(ownerId: string, devices: SystemDevice[] | Record<string, SystemDevice>): Promise<void> {
    if (!devices?.length) return;

    const array: SystemDevice[] =
      devices instanceof Array ? devices : Object.entries(devices).map(([key, device]) => ({ ...device, key }));

    array.forEach(device => (device.ownerId = ownerId));

    await this.metafieldRepo.setMetafields(
      array.map(device => ({
        ownerId,
        namespace: 'devices',
        key: `${device.key}`,
        value: JSON.stringify(device),
        type: 'json',
      })),
    );
  }

  async getDevices(ownerId: string) {
    const devices = await queryAllConnections(cursor =>
      this.metafieldRepo.getJSONMetafields<SystemDevice>(ownerId, 'devices', { first: 10, startCursor: cursor }),
    );

    devices.forEach(device => (device.ownerId = ownerId));

    return devices;
  }

  async getDevicesDictionary(ownerId: string) {
    const devices = await this.getDevices(ownerId);

    return Object.fromEntries(devices.map(device => [device.key, device]));
  }
}
