import { AdminClient } from '@/business/clients/admin-client';
import { queryAllConnections } from '@/lib/utils';
import { singleton } from 'tsyringe';
import { SystemAdapter } from '../adapters/system';
import { CreateSystemADCCustomerInput } from '../types';
import { System } from '../types/system';
import { ADCRepo } from './adc';
import { MetafieldRepo } from './metafield';

@singleton()
export class SystemRepo {
  constructor(
    private readonly adapter: SystemAdapter,
    private readonly adc: ADCRepo,
    private readonly admin: AdminClient,
    private readonly metafieldRepo: MetafieldRepo,
  ) {}

  async getAllSystems(ownerId: string) {
    const systems = (
      await queryAllConnections(async cursor =>
        this.metafieldRepo.getJSONMetafields<System>(ownerId, 'systems', {
          first: 3,
          startCursor: cursor,
        }),
      )
    )?.filter(system => system.adcCustomerId);

    systems.forEach(system => (system.ownerId = ownerId));

    return systems;
  }

  async getSystem(ownerId: string, key: string): Promise<System | undefined> {
    const system = await this.metafieldRepo.getJSONMetafield<System>(ownerId, 'systems', key);

    if (!system) return;

    system.ownerId = ownerId;
    system.key = key;

    return system;
  }

  async deleteSystemMetafield(ownerId: string, key: string) {
    return await this.metafieldRepo.deleteMetafields([{ ownerId, key, namespace: 'systems' }]);
  }

  async setSystem(ownerId: string, key: string, system: Omit<System, 'ownerId' | 'key'>) {
    const value: System = {
      ...system,
      ownerId,
      key,
    };

    const data = await this.metafieldRepo.setMetafields({
      key,
      ownerId,
      namespace: 'systems',
      value: JSON.stringify(value),
      type: 'json',
    });

    return JSON.parse(data?.[0]?.value) as System;
  }

  async getSystemOwnerName(ownerId: string) {
    const query = await this.admin.request(
      `#graphql
      query GetSystemOwnerName($ownerId: ID!) {
        node(id: $ownerId) {
          __typename
          ... on Customer {
            displayName
          }
          ... on Company {
            name
          }
        }
      }`,
      {
        variables: {
          ownerId,
        },
      },
    );

    const node = query?.node;

    if (node?.__typename == 'Customer') {
      return node?.displayName;
    } else if (node?.__typename == 'Company') {
      return node?.name;
    }

    return undefined;
  }

  async createADCCustomerForSystem(input: CreateSystemADCCustomerInput) {
    const adcInput = this.adapter.createSystemADCCustomerInputToADCCustomerCreateInput(input);

    const adcCustomer = await this.adc.createCustomer(adcInput);

    await this.adc.setCustomerLockout(adcCustomer.customerId, true, 'Customer has not setup their subscription yet.');

    return adcCustomer;
  }

  async getSystemName(system: Pick<System, 'adcCustomerId'>) {
    const adcCustomer = await this.adc.getCustomer(system.adcCustomerId);

    return adcCustomer?.unitDescription || 'Unnamed System';
  }
}
