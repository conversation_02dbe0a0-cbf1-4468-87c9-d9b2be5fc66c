import { ADCClient } from '@/business/clients/adc-client';
import { CacheLong } from '@shopify/hydrogen';
import { singleton } from 'tsyringe';
import { AddonQuantities } from '../types';
import {
  ADCCarConnectorInput,
  ADCCarConnectorOutput,
  ADCCustomer,
  ADCCustomerCreated,
  ADCCustomerCreateInput,
  ADCDealerTemplate,
  ADCEquipment,
  ADCLogin,
  ADCResourceOp,
  ADCServicePackage,
  ADCServicePlanInfo,
} from '../types/adc';
import { BUSINESS_SERVICE_PACKAGE, RESIDENTIAL_SERVICE_PACKAGE } from '../constants/service-packages';

@singleton()
export class ADCRepo {
  constructor(private readonly adc: ADCClient) {}

  async getServicePackages(cacheStrategy = CacheLong()) {
    const servicePackages = await this.adc.request<ADCServicePackage[]>(
      'GET',
      `/dealers/${this.adc.adcDealerId}/packages`,
      undefined,
      cacheStrategy,
    );

    return servicePackages;
  }

  async getDealerTemplates(cacheStrategy = CacheLong()) {
    const customTemplates: ADCDealerTemplate[] = await this.adc.request(
      'GET',
      `/dealers/${this.adc.adcDealerId}/packages/custom-templates`,
      undefined,
      cacheStrategy,
    );

    return customTemplates.filter(
      template =>
        template.showTemplateInAccountCreation &&
        (template.basePackage?.id == RESIDENTIAL_SERVICE_PACKAGE.id ||
          template.basePackage?.id == BUSINESS_SERVICE_PACKAGE.id),
    );
  }

  async getCustomer(adcId: number) {
    return this.adc.request<ADCCustomer>('GET', `/customers/${adcId}`);
  }

  async createCustomer(input: ADCCustomerCreateInput): Promise<ADCCustomerCreated> {
    const res = await this.adc.request<ADCCustomerCreated>('POST', `/dealers/${this.adc.adcDealerId}/customers`, input);

    if (!res.customerId)
      throw new Error('Unknown error!... Could not create ADC customer for ' + JSON.stringify(input));

    return res;
  }

  async getCustomerEquipment(adcId: number, invalidateCache = false) {
    const equipment: ADCEquipment[] = await this.adc.request(
      'GET',
      `/customers/${adcId}/equipment/identifiers?invalidateCache=${invalidateCache}&paging.pageSize=1000`,
    );

    return equipment;
  }

  async getCustomerLogins(adcId: number) {
    const logins = await this.adc.request<ADCLogin[]>('GET', `/customers/${adcId}/logins`);

    return logins;
  }

  async updateServicePlan(adcId: number, ops: ADCResourceOp[]): Promise<ADCServicePlanInfo> {
    return await this.adc.updateResource(`/customers/${adcId}/service-plans/current`, ops);
  }

  async tryApplyAddonQuantities(adcCustomerId: number, from: AddonQuantities, to: AddonQuantities) {
    const commonIds = Object.keys({ ...from, ...to });

    const settled = await Promise.allSettled(
      commonIds.map(async addonIdStr => {
        const addonId = parseInt(addonIdStr);
        const fromQty = from[addonId] || 0;
        const toQty = to[addonId] || 0;

        if (toQty != fromQty) {
          await this.updateServicePlan(adcCustomerId, [
            {
              op: 'replace',
              path: 'AddOn',
              value: addonId,
            },
            {
              op: 'replace',
              path: 'Quantity',
              value: toQty.toString(),
            },
          ]);
        }
      }),
    );

    return settled;
  }

  async applyAddonQuantityUpdates(
    adcCustomerId: number,
    from: AddonQuantities,
    to: AddonQuantities,
    operation: 'increase' | 'decrease',
  ) {
    const commonIds = Object.keys({ ...from, ...to });

    const increase = operation == 'increase';
    const settled = await Promise.allSettled(
      commonIds.map(async addonIdStr => {
        const addonId = parseInt(addonIdStr);
        const fromQty = from[addonId] || 0;
        const toQty = to[addonId] || 0;

        if ((increase && toQty > fromQty) || (!increase && toQty < fromQty)) {
          await this.updateServicePlan(adcCustomerId, [
            {
              op: 'replace',
              path: 'AddOn',
              value: addonId,
            },
            {
              op: 'replace',
              path: 'Quantity',
              value: toQty.toString(),
            },
          ]);
        }
      }),
    );

    const rejected = settled.find(result => result.status == 'rejected');

    if (rejected) {
      throw new Error(`Failed to update service: ${rejected.reason}`, { cause: rejected.reason });
    }
  }

  async updateCustomer(adcId: number, ops: ADCResourceOp[]) {
    return await this.adc.updateResource(`/customers/${adcId}`, ops);
  }

  async deleteCustomer(adcId: number): Promise<{ message: string }> {
    return await this.adc.request('DELETE', `/customers/${adcId}`);
  }

  async setCustomerLockout(
    adcId: number,
    isInteractiveLockoutEnabled: boolean,
    reason?: string,
  ): Promise<{ outputMessage?: string; customerId?: number; customerInfo?: ADCCustomer }> {
    const ops: ADCResourceOp[] = [
      {
        op: 'replace',
        path: 'IsInteractiveLockoutEnabled',
        value: isInteractiveLockoutEnabled,
      },
    ];

    if (reason) {
      ops.push({
        op: 'replace',
        path: 'InteractiveLockoutChangeReason',
        value: reason,
      });
    }

    return await this.adc.updateResource(`/customers/${adcId}`, ops);
  }

  async addCarConnector(adcId: number, input: ADCCarConnectorInput) {
    return await this.adc.request<ADCCarConnectorOutput>('POST', `/customers/${adcId}/equipment/car-connector`, input);
  }
}
