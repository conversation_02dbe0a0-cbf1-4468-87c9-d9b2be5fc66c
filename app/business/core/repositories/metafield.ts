import { AdminClient, validateG<PERSON>UserError } from '@/business/clients/admin-client';
import { Met<PERSON>ield, MetafieldDefinitionInput, MetafieldIdentifierInput, MetafieldsSetInput } from 'admin.types';
import { singleton } from 'tsyringe';
import { CacheOptions } from '../types/cache';
import { PaginationVariables } from '../types';

@singleton()
export class MetafieldRepo {
  constructor(private readonly admin: AdminClient) {}
  async setMetafields(metafields: MetafieldsSetInput | MetafieldsSetInput[]) {
    const array = metafields instanceof Array ? metafields : [metafields];

    const results: Pick<Metafield, 'value' | 'id'>[] = [];
    while (array.length > 0) {
      const part = array.splice(0, Math.min(25, array.length));
      const data = await this.admin.request(
        `#graphql
            mutation SetMetafields($metafields: [MetafieldsSetInput!]!) {
                metafieldsSet(metafields: $metafields) {
                    metafields {
                        id
                        value
                    }
                    userErrors {
                        code
                        elementIndex
                        field
                        message
                    }
                }
            }
        ` as const,
        {
          variables: {
            metafields: part,
          },
        },
      );

      validateGQLUserError(data?.metafieldsSet?.userErrors);

      results.push(...(data?.metafieldsSet?.metafields || []));
    }

    return results;
  }

  async deleteMetafields(identifiers: MetafieldIdentifierInput[]) {
    const data = await this.admin.request(
      `#graphql
      mutation DeleteMetafields($identifiers: [MetafieldIdentifierInput!]!) {
        metafieldsDelete(metafields: $identifiers) {
          deletedMetafields {
            key
            namespace
            ownerId
          }
          userErrors {
            field
            message
          }
        }
      }
    `,
      {
        variables: {
          identifiers,
        },
      },
    );

    validateGQLUserError(data?.metafieldsDelete?.userErrors);

    return data?.metafieldsDelete?.deletedMetafields;
  }

  async getMetafield(ownerId: string, namespace: string, key: string, cache?: CacheOptions) {
    const data = await this.admin.request(
      `#graphql
        query GetMetafield($ownerId: ID!, $namespace: String!, $key: String!) {
            node(id: $ownerId) {
                ... on HasMetafields {
                    metafield(namespace: $namespace, key: $key) {
                        value
                    }
                }
            }
        }
    ` as const,
      {
        variables: {
          ownerId,
          namespace,
          key,
        },
      },
      cache,
    );

    return data?.node?.metafield?.value;
  }

  async getJSONMetafield<T extends object>(ownerId: string, namespace: string, key: string, cache?: CacheOptions) {
    const data = await this.admin.request(
      `#graphql
        query GetJSONMetafield($ownerId: ID!, $namespace: String!, $key: String!) {
            node(id: $ownerId) {
                ... on HasMetafields {
                    metafield(namespace: $namespace, key: $key) {
                        jsonValue
                    }
                }
            }
        }
    ` as const,
      {
        variables: {
          ownerId,
          namespace,
          key,
        },
      },
      cache,
    );

    return data?.node?.metafield?.jsonValue as T | undefined;
  }

  async getJSONMetafields<T extends object>(ownerId: string, namespace: string, page: PaginationVariables) {
    const data = await this.admin.request(
      `#graphql
      query GetJSONMetafields($ownerId: ID!, $namespace: String!, $first: Int, $last: Int, $startCursor: String, $endCursor: String) {
        node(id: $ownerId) {
          ... on HasMetafields {
            metafields(namespace: $namespace, first: $first, last: $last, after: $startCursor, before: $endCursor) {
              nodes {
                jsonValue
                key
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                endCursor
                startCursor
              }
            }
          }
        }
      }` as const,
      {
        variables: {
          ownerId,
          namespace,
          ...page,
        },
      },
    );

    const metafields = data?.node?.metafields;

    return {
      nodes: metafields?.nodes.map(node => ({ ...node.jsonValue, key: node.key })) as (T & { key: string })[],
      pageInfo: metafields?.pageInfo,
    };
  }

  async getShopMetafield(namespace: string, key: string, cache?: CacheOptions) {
    const data = await this.admin.request(
      `#graphql
        query GetShopMetafield($namespace: String!, $key: String!) {
            shop {
                metafield(namespace: $namespace, key: $key) {
                    value
                }
            }
        }` as const,
      {
        variables: {
          namespace,
          key,
        },
      },
      cache,
    );

    return data?.shop?.metafield?.value;
  }

  async getShopJSONMetafield<T extends object>(namespace: string, key: string, cache?: CacheOptions) {
    const data = await this.admin.request(
      `#graphql
        query GetShopJSONMetafield($namespace: String!, $key: String!) {
            shop {
                metafield(namespace: $namespace, key: $key) {
                    jsonValue
                }
            }
        }` as const,
      {
        variables: {
          namespace,
          key,
        },
      },
      cache,
    );

    return data?.shop?.metafield?.jsonValue as T | undefined;
  }

  async upsertMetafieldDefinition(definition: MetafieldDefinitionInput) {
    const findDefinition = await this.admin.request(
      `#graphql
      query FindMetafieldDefinitionId($ownerType: MetafieldOwnerType!, $namespace: String!, $key: String!) {
        metafieldDefinitions(first: 1, ownerType: $ownerType, namespace: $namespace, key: $key) {
          nodes {
            id
          }
        }
      }
    `,
      { variables: { ownerType: definition.ownerType, namespace: definition.namespace!, key: definition.key } },
    );

    const definitionId = findDefinition?.metafieldDefinitions?.nodes?.[0]?.id;

    if (definitionId) {
      const data = await this.admin.request(
        `#graphql
        mutation UpdateDeviceHandleMFD($definition: MetafieldDefinitionUpdateInput!) {
          metafieldDefinitionUpdate(definition: $definition) {
            updatedDefinition {
              id
            },
            userErrors {
              code
              elementIndex
              field
              message
            }
          }
        }
      `,
        {
          variables: {
            definition: {
              name: definition.name,
              ownerType: definition.ownerType,
              namespace: definition.namespace,
              key: definition.key,
              description: definition.description,
              pin: definition.pin,
              validations: definition.validations,
              access: definition.access?.admin
                ? {
                    admin: definition.access.admin,
                    storefront: definition.access?.storefront,
                  }
                : undefined,
              capabilities: definition.capabilities,
            },
          },
        },
      );

      validateGQLUserError(data?.metafieldDefinitionUpdate?.userErrors);

      return data?.metafieldDefinitionUpdate?.updatedDefinition?.id;
    } else {
      const data = await this.admin.request(
        `#graphql
        mutation CreateDeviceHandleMFD($definition: MetafieldDefinitionInput!) {
          metafieldDefinitionCreate(definition: $definition) {
            createdDefinition {
              id
            }
            userErrors {
              code
              elementIndex
              field
              message
            }
          }
        }`,
        { variables: { definition } },
      );

      validateGQLUserError(data?.metafieldDefinitionCreate?.userErrors);

      return data?.metafieldDefinitionCreate?.createdDefinition?.id;
    }
  }
}
