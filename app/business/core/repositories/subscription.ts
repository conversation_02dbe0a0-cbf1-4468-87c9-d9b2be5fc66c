import { StripeClient } from '@/business/clients/stripe-client';
import {
  addQuantities,
  convertDevicesToFeatureQuantities,
  formatMoney,
  maxQuantities,
  partition,
  toOrdinal,
} from '@/lib/utils';
import Stripe from 'stripe';
import { injectable, Lifecycle, scoped } from 'tsyringe';
import { SubscriptionAdapter } from '../adapters/subscription';
import {
  ADC_CUSTOMER_ID_METADATA_KEY,
  COMPANY_LOCATION_ID_METADATA_KEY,
  OWNER_ID_METADATA_KEY,
  SYSTEM_KEY_METADATA_KEY,
} from '../constants/subscription';
import { FeatureQuantities, StripePaginationVariables, SubscriptionLineItem } from '../types';
import { SystemDevice } from '../types/device';
import { System } from '../types/system';
import { AccountRepo } from './account';
import { MetafieldRepo } from './metafield';
import { ServiceRepo } from './service';
import { SystemRepo } from './system';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class SubscriptionRepo {
  constructor(
    private readonly adapter: SubscriptionAdapter,
    private readonly stripe: StripeClient,
    private readonly accountRepo: AccountRepo,
    private readonly serviceRepo: ServiceRepo,
    private readonly systemRepo: SystemRepo,
    private readonly metafieldRepo: MetafieldRepo,
  ) {}

  async getSystemSubscription(systemSubscription: string | Stripe.Subscription, expand?: string[]) {
    const subscription =
      typeof systemSubscription == 'string'
        ? await this.stripe.subscriptions.retrieve(systemSubscription, { expand })
        : systemSubscription;

    return subscription;
  }

  async cancelSystemSubscription(systemSubscriptionId: string) {
    const subscription = await this.stripe.subscriptions.cancel(systemSubscriptionId);

    return subscription;
  }

  getSystemSubscriptionMetadata(subscription: Stripe.Subscription) {
    return {
      ownerId: subscription.metadata[OWNER_ID_METADATA_KEY],
      systemKey: subscription.metadata[SYSTEM_KEY_METADATA_KEY],
      adcCustomerId: parseInt(subscription.metadata[ADC_CUSTOMER_ID_METADATA_KEY]),
      locationId: subscription.metadata[COMPANY_LOCATION_ID_METADATA_KEY],
    };
  }

  async getSystemSubscriptionSchedule(systemSubscription: Stripe.Subscription) {
    const schedule =
      typeof systemSubscription.schedule == 'string'
        ? await this.stripe.subscriptionSchedules.retrieve(systemSubscription.schedule)
        : systemSubscription.schedule;

    return schedule || undefined;
  }

  async getSystemSubscriptionPaymentMethod(systemSubscription: Stripe.Subscription) {
    const paymentMethod =
      typeof systemSubscription.default_payment_method == 'string'
        ? await this.stripe.paymentMethods.retrieve(systemSubscription.default_payment_method)
        : systemSubscription.default_payment_method;

    return paymentMethod;
  }

  async getSystemFromSubscription(systemSubscription: string | Stripe.Subscription) {
    const subscription = await this.getSystemSubscription(systemSubscription);
    const { ownerId, systemKey } = this.getSystemSubscriptionMetadata(subscription);

    return this.systemRepo.getSystem(ownerId, systemKey);
  }

  async getOwnedSubscriptions(ownerId: string) {
    const systems = await this.stripe.subscriptions
      .search({
        query: `metadata["${OWNER_ID_METADATA_KEY}"]:"${ownerId}"`,
        limit: 10,
      })
      .autoPagingToArray({ limit: 100 });

    return systems;
  }

  async getSystemSubscriptionBySystemKey(systemKey: string) {
    const search = await this.stripe.subscriptions.search({
      query: `metadata["${SYSTEM_KEY_METADATA_KEY}"]:"${systemKey}"`,
      limit: 1,
    });

    return search.data?.[0];
  }

  async getSystemSubscriptionByADCId(adcId: number) {
    const search = await this.stripe.subscriptions.search({
      query: `metadata["${ADC_CUSTOMER_ID_METADATA_KEY}"]:"${adcId}"`,
      limit: 1,
    });

    return search.data?.[0];
  }

  async getPastInvoices(systemSubscriptionId: string, pageQuery: StripePaginationVariables) {
    const invoices = await this.stripe.invoices.list({
      subscription: systemSubscriptionId,
      ...pageQuery,
    });

    return invoices;
  }

  async previewUpcomingInvoice({ systemSubscriptionId }: { systemSubscriptionId: string }) {
    return await this.stripe.invoices.createPreview({
      subscription: systemSubscriptionId,
    });
  }

  getSubscriptionLineItems(subscription: Stripe.Subscription) {
    const lineItems: SubscriptionLineItem[] = subscription.items.data.map(item => ({
      price: item.price.id,
      quantity: item.quantity || 0,
    }));

    return lineItems;
  }

  getSubscriptionScheduleLineItems(schedule: Stripe.SubscriptionSchedule | undefined) {
    const scheduledLineItems: SubscriptionLineItem[] | undefined = schedule?.phases
      ?.find(phase => phase.start_date >= (schedule.current_phase?.end_date || Date.now() / 1000))
      ?.items.map(item => ({
        price: (item.price as Stripe.Price)?.id || (item.price as string),
        quantity: item.quantity || 0,
      }));

    return scheduledLineItems;
  }

  async getFeatureQuantities(systemSubscription: Stripe.Subscription | string) {
    const subscription = await this.getSystemSubscription(systemSubscription);

    const lineItems = this.getSubscriptionLineItems(subscription);

    return this.serviceRepo.calculateFeatureQuantitiesFromLineItems({ lineItems });
  }

  async getCurrentAndScheduledFeatureQuantities(systemSubscription: string | Stripe.Subscription) {
    const subscription = await this.getSystemSubscription(systemSubscription, ['schedule']);
    const schedule = await this.getSystemSubscriptionSchedule(subscription);

    const lineItems = this.getSubscriptionLineItems(subscription);
    const scheduledLineItems = this.getSubscriptionScheduleLineItems(schedule);

    const features = await this.serviceRepo.calculateFeatureQuantitiesFromLineItems({
      lineItems,
    });
    const scheduledFeatures = await this.serviceRepo.calculateFeatureQuantitiesFromLineItems({
      lineItems: scheduledLineItems || lineItems,
    });

    return { features, scheduledFeatures };
  }

  async previewUpdateSystemLineItems({
    systemSubscription,
    lineItems,
    scheduledLineItems,
  }: {
    systemSubscription: string | Stripe.Subscription;
    lineItems: SubscriptionLineItem[];
    scheduledLineItems?: SubscriptionLineItem[];
  }) {
    const subscription = await this.getSystemSubscription(systemSubscription);

    let schedule = await this.getSystemSubscriptionSchedule(subscription);
    if (
      !schedule &&
      scheduledLineItems &&
      (scheduledLineItems.length != lineItems.length ||
        scheduledLineItems.some(
          (A, index) => A.price != lineItems[index].price || A.quantity != lineItems[index].quantity,
        ))
    ) {
      schedule = await this.stripe.subscriptionSchedules.create({ from_subscription: subscription.id });
    }

    const { lineUpdates, finalLineItems } = await this.calculateImmediateSystemLineItemUpgrades({
      systemSubscription: subscription,
      lineItems,
    });

    if (schedule) {
      return await this.stripe.invoices.createPreview({
        schedule: schedule.id,
        schedule_details: {
          end_behavior: 'release',
          proration_behavior: 'create_prorations',
          phases: [
            {
              items: finalLineItems,
              start_date: schedule.current_phase?.start_date,
              end_date: schedule.current_phase?.end_date,
            },
            {
              items: scheduledLineItems || lineItems,
              iterations: 1,
            },
          ],
        },
      });
    } else {
      return await this.stripe.invoices.createPreview({
        subscription: subscription.id,
        subscription_details: {
          proration_behavior: 'create_prorations',
          items: lineUpdates.map(update => ({
            id: update.id,
            price: update.price,
            quantity: update.quantity,
            deleted: update.quantity == 0,
          })),
        },
      });
    }
  }

  async updateSystemLineItems({
    systemSubscription,
    lineItems,
    scheduledLineItems,
  }: {
    systemSubscription: string | Stripe.Subscription;
    lineItems: SubscriptionLineItem[];
    scheduledLineItems?: SubscriptionLineItem[];
  }) {
    scheduledLineItems ||= [...lineItems];

    const subscription = await this.getSystemSubscription(systemSubscription, ['schedule']);
    const { lineUpdates } = await this.calculateImmediateSystemLineItemUpgrades({
      systemSubscription: subscription,
      lineItems,
    });

    const pause = scheduledLineItems.length == 0;

    let schedule: Stripe.SubscriptionSchedule | undefined;
    let currentItems: Stripe.SubscriptionItem[];

    //If there are line updates, apply them.
    if (lineUpdates.length) {
      const update = await this.stripe.subscriptions.update(subscription.id, {
        items: lineUpdates,
        payment_behavior: 'error_if_incomplete',
        proration_behavior: 'always_invoice',
        pause_collection: pause ? { behavior: 'void' } : '',
        expand: ['schedule'],
      });

      //Reload the new schedule and current items.
      schedule = update.schedule as Stripe.SubscriptionSchedule | undefined;
      currentItems = update.items.data;
    } else {
      schedule = await this.getSystemSubscriptionSchedule(subscription);
      currentItems = [...subscription.items.data];

      const isPaused = !!subscription.pause_collection?.behavior;
      if (pause != isPaused) {
        await this.stripe.subscriptions.update(subscription.id, {
          pause_collection: pause ? { behavior: 'void' } : '',
        });
      }
    }

    //After the upgrade has applied, if there are any line items that are different from the current line items then those must be downgrades and should be handled in the schedule.
    const hasDowngrade =
      scheduledLineItems.length != currentItems.length ||
      scheduledLineItems.some(
        (A, index) => A.price != currentItems[index].price.id || A.quantity != currentItems[index].quantity,
      );

    //Create or release schedule as necessary
    if (hasDowngrade && !schedule) {
      schedule = await this.stripe.subscriptionSchedules.create({ from_subscription: subscription.id });
    } else if (!hasDowngrade && schedule) {
      await this.stripe.subscriptionSchedules.release(schedule.id);
    }

    //If a downgrade is necessary, update the current schedule to reflect the current items for the current phase, and the input items for the subsequent phase.
    if (hasDowngrade && schedule) {
      //TODO: Investigate more sophisticated phase updates.
      await this.stripe.subscriptionSchedules.update(schedule.id, {
        phases: [
          {
            items: currentItems.map(item => ({ price: item.price.id, quantity: item.quantity })),
            ...schedule.current_phase,
          },
          {
            items: pause
              ? currentItems.map(item => ({ price: item.price.id, quantity: 0 }))
              : scheduledLineItems.map(item => ({ price: item.price, quantity: item.quantity })),
            iterations: 1,
          },
        ],
      });
    }
  }

  async calculateImmediateSystemLineItemUpgrades({
    systemSubscription,
    lineItems,
  }: {
    systemSubscription: string | Stripe.Subscription;
    lineItems: SubscriptionLineItem[];
  }) {
    const serviceProducts = await this.serviceRepo.getServiceProductsByPriceId();
    const subscription = await this.getSystemSubscription(systemSubscription);

    const finalLineItems: SubscriptionLineItem[] = [];
    const lineUpdates: Stripe.SubscriptionUpdateParams.Item[] = [];
    // Find and record all lines that represent upgrades, respecting service groups.
    let sourceLines = [...subscription.items.data];
    let inGroupLines: Stripe.SubscriptionItem[];
    for (const line of lineItems) {
      const product = serviceProducts[line.price];

      if (!product) continue;

      [inGroupLines, sourceLines] = partition(
        sourceLines,
        item => serviceProducts[item.price.id]?.group == product.group,
      );
      const targetRate = product.monthlyRate * line.quantity;
      const sourceRate = inGroupLines.reduce(
        (prev, item) => prev + (serviceProducts[item.price.id]?.monthlyRate || 0) * (item.quantity || 0),
        0,
      );

      const lineToReplace = inGroupLines.pop();
      if (!lineToReplace || targetRate > sourceRate) {
        //Upgrade or add line
        lineUpdates.push({
          id: lineToReplace?.id,
          price: line.price,
          quantity: line.quantity,
        });
        finalLineItems.push({
          price: line.price,
          quantity: line.quantity,
        });
      } else {
        finalLineItems.push({
          price: lineToReplace.price.id,
          quantity: lineToReplace.quantity || 0,
        });
      }

      lineUpdates.push(
        ...inGroupLines.map((source): Stripe.SubscriptionUpdateParams.Item => ({ id: source.id, deleted: true })),
      );
    }

    return { lineUpdates, finalLineItems };
  }

  async calculateFeatureQuantityChangeServiceLineItems({
    featuresDelta,
    ...args
  }: {
    featuresDelta: FeatureQuantities;
  } & (
    | {
        systemSubscription: string | Stripe.Subscription;
      }
    | {
        lineItems: SubscriptionLineItem[];
        scheduledLineItems?: SubscriptionLineItem[];
      }
  )) {
    let lineItems: SubscriptionLineItem[];
    let scheduledLineItems: SubscriptionLineItem[] | undefined;
    if ('systemSubscription' in args) {
      const subscription = await this.getSystemSubscription(args.systemSubscription, ['schedule']);
      const schedule = await this.getSystemSubscriptionSchedule(subscription);

      lineItems = this.getSubscriptionLineItems(subscription);
      scheduledLineItems = this.getSubscriptionScheduleLineItems(schedule);
    } else {
      ({ lineItems, scheduledLineItems } = args);
    }

    //Only allow upgrades to current lines.
    const newLineItems = await this.serviceRepo.changeLineItemsByFeatures({
      lineItems,
      change: features => addQuantities(features, maxQuantities(featuresDelta, {})),
    });

    //Allow both upgrades and downgrades for scheduled lines.
    const newScheduledLineItems = await this.serviceRepo.changeLineItemsByFeatures({
      lineItems: scheduledLineItems || lineItems,
      change: features => addQuantities(features, featuresDelta),
    });

    return { lineItems: newLineItems, scheduledLineItems: newScheduledLineItems };
  }

  async pauseSystemSubscription({ systemSubscriptionId }: { systemSubscriptionId: string }) {
    const subscription = await this.getSystemSubscription(systemSubscriptionId, ['schedule']);

    const updatedSubscription = await this.stripe.subscriptions.update(systemSubscriptionId, {
      pause_collection: { behavior: 'void' },
    });

    const schedule =
      (await this.getSystemSubscriptionSchedule(subscription)) ||
      (await this.stripe.subscriptionSchedules.create({ from_subscription: systemSubscriptionId }));

    const updatedSchedule = await this.stripe.subscriptionSchedules.update(schedule.id, {
      phases: [
        {
          items: subscription.items.data.map(item => ({ price: item.price.id, quantity: item.quantity })),
          ...schedule.current_phase,
        },
        {
          items: subscription.items.data.map(item => ({ price: item.price.id, quantity: 0 })),
          iterations: 1,
        },
      ],
    });

    return { subscription: updatedSubscription, schedule: updatedSchedule };
  }

  async getOrCreateStripeCustomer(ownerId: string): Promise<Stripe.Customer> {
    try {
      const metafield = await this.metafieldRepo.getMetafield(ownerId, 'account', 'stripe_customer_id');
      if (metafield) {
        const found = await this.stripe.customers.retrieve(metafield);
        if (!found?.deleted) return found;
      }
    } catch (error) {
      console.error('Error getting stripe customer', error);
    }

    const contactInfo = await this.accountRepo.getSystemOwnerContactInfo(ownerId);

    if (!contactInfo?.__typename) {
      throw new Error(`Contact info not found for owner ${ownerId}`);
    }

    const input = this.adapter.convertOwnerContactInfoToStripeCustomerInput(ownerId, contactInfo);
    const stripeCustomer = await this.stripe.customers.create(input);

    await this.metafieldRepo.setMetafields({
      ownerId,
      namespace: 'account',
      key: 'stripe_customer_id',
      value: stripeCustomer.id,
      type: 'single_line_text_field',
    });

    return stripeCustomer;
  }

  async newSystemCheckout({
    ownerId,
    orderId,
    lineItems,
    returnUrl,
    adcCustomerId,
    locationId,
    systemKey,
  }: {
    ownerId: string;
    orderId: string;
    lineItems: SubscriptionLineItem[];
    returnUrl: string;
    adcCustomerId: number;
    locationId?: string;
    systemKey: string;
  }) {
    const stripeCustomer = await this.getOrCreateStripeCustomer(ownerId);

    const total = await this.serviceRepo.calculateLineItemsTotal({ lineItems });
    const date = new Date();
    const monthDay = toOrdinal(date.getDate());

    const session = await this.stripe.checkout.sessions.create({
      client_reference_id: orderId,
      customer: stripeCustomer.id,
      mode: 'subscription',
      ui_mode: 'embedded',
      line_items: lineItems,
      allow_promotion_codes: true,
      subscription_data: {
        metadata: {
          [OWNER_ID_METADATA_KEY]: ownerId,
          [ADC_CUSTOMER_ID_METADATA_KEY]: adcCustomerId,
          [COMPANY_LOCATION_ID_METADATA_KEY]: locationId || null,
          [SYSTEM_KEY_METADATA_KEY]: systemKey,
        },
      },
      consent_collection: {
        terms_of_service: 'required',
        payment_method_reuse_agreement: { position: 'hidden' },
      },
      custom_text: {
        terms_of_service_acceptance: {
          message: `By clicking this box, you agree that your monthly subscription will automatically renew on the **${monthDay} of every month,** and you will be charged the same fee of **${formatMoney(
            total,
          )} per month** to your credit card or payment method on file until you cancel. See the auto-renewal service [Terms and Conditions](https://www.allaware.com/policies/terms-of-service) for more information. You can cancel any time by visiting the "Subscriptions" tab on your account or by calling us at (855) 71-AWARE. For more information on how to cancel your monthly subscription, please visit [Cancellation Policy](https://www.allaware.com/policies/subscription-policy).`,
        },
      },
      return_url: returnUrl,
      redirect_on_completion: 'always',
    });

    return session;
  }

  async createMigrationCheckoutSession({
    system,
    devices,
    returnUrl,
  }: {
    customerId: string;
    system: System;
    devices: SystemDevice[];
    returnUrl: string;
  }) {
    const { ownerId, firstOrderId, adcCustomerId, locationId, key: systemKey } = system;

    const stripeCustomer = await this.getOrCreateStripeCustomer(system.ownerId);

    const activeDevices = devices.filter(device => device.state == 'active');
    const features = convertDevicesToFeatureQuantities(activeDevices);
    const inactiveAccount = !features['flex-aware'];
    const lineItems = await this.serviceRepo.calculateLineItemsFromFeatureQuantities(
      inactiveAccount ? { 'flex-aware': 1 } : features,
    );
    //Set quantity to 0 for inactive accounts
    if (inactiveAccount) lineItems.forEach(lineItem => (lineItem.quantity = 0));

    const priceId = lineItems[0]?.price;
    const serviceProduct = (await this.serviceRepo.getServiceProductsByPriceId())[priceId];
    const monthlyRate = serviceProduct?.monthlyRate || 12;

    const discountPercentage = parseFloat(
      devices
        .reduce(
          (acc, device) => Math.max(acc, ((monthlyRate - (device.legacyPrice || monthlyRate)) / monthlyRate) * 100),
          0,
        )
        .toFixed(2),
    );

    const total = (await this.serviceRepo.calculateLineItemsTotal({ lineItems })) * (1 - discountPercentage / 100);

    let couponId: string | undefined = undefined;
    if (discountPercentage > 0) {
      const coupons = await this.stripe.coupons.list().autoPagingToArray({ limit: 100 });

      const foundCoupon = coupons.find(c => c.percent_off == discountPercentage);
      if (foundCoupon) couponId = foundCoupon.id;
      else {
        const createdCoupon = await this.stripe.coupons.create({
          name: `Legacy Migration - Flex ${Math.ceil(discountPercentage)}% OFF`,
          applies_to: {
            products: [serviceProduct.id],
          },
          percent_off: discountPercentage,
          duration: 'forever',
        });

        couponId = createdCoupon.id;
      }
    }

    const date = new Date();
    const nextFirstOfMonth = new Date(
      Date.UTC(
        date.getUTCFullYear(),
        date.getUTCMonth() + 1, // Go to the next month
        1, // Set the day to the 1st
        18, // Set UTC hour to 18 (for GMT-0600 noon)
        0, // Minute
        0, // Second
        0, // Millisecond
      ),
    );

    const nextDay = new Date(
      Date.UTC(
        date.getUTCFullYear(),
        date.getUTCMonth(),
        date.getUTCDate() + 1,
        6, // Set UTC hour to 18 (for GMT-0600 midnight)
        0, // Minute
        0, // Second
        0, // Millisecond
      ),
    );

    const session = await this.stripe.checkout.sessions.create({
      client_reference_id: firstOrderId,
      customer: stripeCustomer.id,
      mode: 'subscription',
      ui_mode: 'embedded',
      line_items: lineItems,
      expires_at: nextDay.getTime() / 1000,
      discounts: [
        {
          coupon: couponId,
        },
      ],
      subscription_data: {
        metadata: {
          [OWNER_ID_METADATA_KEY]: ownerId,
          [ADC_CUSTOMER_ID_METADATA_KEY]: adcCustomerId,
          [COMPANY_LOCATION_ID_METADATA_KEY]: locationId || null,
          [SYSTEM_KEY_METADATA_KEY]: systemKey,
        },
        billing_cycle_anchor: nextFirstOfMonth.getTime() / 1000,
        proration_behavior: 'none',
      },
      consent_collection: {
        terms_of_service: 'required',
        payment_method_reuse_agreement: { position: 'hidden' },
      },
      custom_text: {
        terms_of_service_acceptance: {
          message: `By clicking this box, you agree that your monthly subscription will automatically renew on the **1st of every month,** and you will be charged the same fee of **${formatMoney(
            total,
          )} per month** to your credit card or payment method on file until you cancel. See the auto-renewal service [Terms and Conditions](https://www.allaware.com/home/<USER>/terms-of-service) for more information. You can cancel any time by visiting the "Subscriptions" tab on your account or by calling us at (855) 71-AWARE. For more information on how to cancel your monthly subscription, please visit [Cancellation Policy](https://www.allaware.com/home/<USER>/terms-of-service#cancel).`,
        },
      },
      return_url: returnUrl,
      redirect_on_completion: 'always',
    });

    return session;
  }
}
