import { AdminClient } from '@/business/clients/admin-client';
import { queryAllConnections } from '@/lib/utils';
import { injectable, Lifecycle, scoped } from 'tsyringe';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export default class DiscountRepo {
  constructor(private readonly admin: AdminClient) {}

  /**
   * Finds discounts that match the search term and are applied to the product.
   * Currently this only supports fixed price or percentage automatic discounts. Note: multi-product discounts are also not supported.
   * @param searchTerm Search the product discount titles (case insensitive).
   * @param productId The product id to match.
   * @returns All the fixed price or percentage automatic discounts that match the search term and are applied to the product (and no other products).
   */
  async findDiscountsToAdvertise(searchTerm: string | undefined | null, productId: string) {
    if (!searchTerm) return [];

    const query = `"${searchTerm}" AND status:ACTIVE AND type:FIXED_AMOUNT OR type:PERCENTAGE`;
    const discounts = await queryAllConnections(cursor =>
      this.admin
        .request(
          `#graphql
        query FindDiscountsToAdvertise($cursor: String, $query: String!) {
          automaticDiscountNodes(after: $cursor, first: 1, query: $query) {
            pageInfo {
              endCursor
              hasNextPage
            }
            nodes {
              automaticDiscount {
                ... on DiscountAutomaticBasic {
                  title
                  combinesWith {
                    orderDiscounts
                    productDiscounts
                  }
                  discountClass
                  minimumRequirement {
                    ... on DiscountMinimumQuantity {
                      greaterThanOrEqualToQuantity
                    }
                  }
                  customerGets {
                    items {
                      ... on DiscountProducts {
                        products(first: 1) {
                          nodes {
                            id
                          }
                          pageInfo {
                            hasNextPage
                          }
                        }
                      }
                    }
                    value {
                      __typename
                      ... on DiscountAmount {
                        amount {
                          amount
                          currencyCode
                        }
                        appliesOnEachItem
                      }
                      ... on DiscountPercentage {
                        percentage
                      }
                    }
                  }
                }
              }
            }
          }
        }`,
          {
            variables: {
              cursor,
              query,
            },
          },
        )
        .then(data => data.automaticDiscountNodes!),
    );

    const search = searchTerm.toLowerCase().trim();
    return discounts
      .map(d => d.automaticDiscount)
      .filter(
        d =>
          'greaterThanOrEqualToQuantity' in d.minimumRequirement &&
          d.title.toLowerCase().includes(search) &&
          d.customerGets?.items?.products?.nodes?.[0]?.id == productId &&
          d.customerGets?.items?.products?.pageInfo?.hasNextPage === false,
      )
      .sort(
        (a, b) =>
          parseInt(a.minimumRequirement.greaterThanOrEqualToQuantity) -
          parseInt(b.minimumRequirement.greaterThanOrEqualToQuantity),
      );
  }
}
