import { AdminClient } from '@/business/clients/admin-client';
import { convertGidToId, queryAllConnections } from '@/lib/utils';
import { singleton } from 'tsyringe';
import { NEW_SYSTEM_KEY_ATTRIBUTE_VALUE, SYSTEM_KEY_ATTRIBUTE_KEY, SYSTEM_NAME_ATTRIBUTE_KEY } from '../constants/cart';
import { ADCCustomerCreated } from '../types/adc';
import { OrderSetupState } from '../types/order';
import { MetafieldRepo } from './metafield';
import { OrderEquipment } from '../adapters/order';

@singleton()
export class OrderRepo {
  constructor(
    private readonly admin: AdminClient,
    private readonly metafieldRepo: MetafieldRepo,
  ) {}

  async getNewADCCustomerCredentials(orderId: string) {
    const [password, loginName] = await Promise.all([
      this.metafieldRepo.getMetafield(orderId, 'credentials', 'password'),
      this.metafieldRepo.getMetafield(orderId, 'credentials', 'login-name'),
    ]);

    return { password, loginName };
  }

  async setNewADCCustomerCredentials(orderId: string, createdCustomer: ADCCustomerCreated) {
    return await this.metafieldRepo.setMetafields([
      {
        ownerId: orderId,
        namespace: 'credentials',
        key: 'password',
        value: createdCustomer.password,
        type: 'single_line_text_field',
      },
      {
        ownerId: orderId,
        namespace: 'credentials',
        key: 'login-name',
        value: createdCustomer.loginName,
        type: 'single_line_text_field',
      },
    ]);
  }

  async getPurchasingEntityInfo(orderId: string) {
    const data = await this.admin.request(
      `#graphql
        query GetOrderADCCreateInputs($orderId: ID!) {
          order(id: $orderId) {
            purchasingEntity {
              __typename
              ... on Customer {
                id
                defaultAddress {
                  address1
                  address2
                  city
                  countryCodeV2
                  country
                  phone
                  provinceCode
                  zip
                  firstName
                  lastName
                }
                email
                firstName
                lastName
                phone
              }
              ... on PurchasingCompany {
                __typename
                company {
                  id
                  name
                },
                contact {
                  customer {
                    id
                    email
                    phone
                  }
                }
                location {
                  id
                  phone
                  shippingAddress {
                    address1
                    address2
                    city
                    phone
                    province
                    zoneCode
                    zip
                  }
                }
              }
            }
          }
        }`,
      { variables: { orderId } },
    );

    return data?.order?.purchasingEntity;
  }

  async getSystemSetupInfo(orderId: string) {
    const attributes = await this.getAttributes(orderId);

    const systemNameAttribute = attributes?.find(att => att.key == SYSTEM_NAME_ATTRIBUTE_KEY)?.value;
    const systemKeyAttribute = attributes?.find(att => att.key == SYSTEM_KEY_ATTRIBUTE_KEY)?.value;

    const newSystem = !systemKeyAttribute || systemKeyAttribute == NEW_SYSTEM_KEY_ATTRIBUTE_VALUE;
    const systemKey = newSystem ? convertGidToId(orderId).toString() : systemKeyAttribute;

    return { newSystem, systemKey, systemNameAttribute };
  }

  async getSetupState(orderId: string): Promise<OrderSetupState | undefined> {
    const setupState = await this.metafieldRepo.getJSONMetafield<OrderSetupState>(orderId, 'setup', 'state');

    return setupState;
  }

  async upsertSetupState(orderId: string, setup: Partial<OrderSetupState>) {
    const setupState = await this.getSetupState(orderId);

    return this.setSetupState(orderId, {
      ...(setupState ?? {}),
      ...(setup as any),
    });
  }

  async setSetupState(orderId: string, setup: OrderSetupState) {
    await this.metafieldRepo.setMetafields({
      ownerId: orderId,
      namespace: 'setup',
      key: 'state',
      value: JSON.stringify(setup),
      type: 'json',
    });

    return setup;
  }

  async getCustomer(orderId: string) {
    const data = await this.admin.request(
      `#graphql
      query GetOrderCustomerId($orderId: ID!) {
        order(id: $orderId) {
          customer {
            id
            email
          }
        }
      }
    `,
      {
        variables: { orderId },
      },
    );

    return data?.order?.customer;
  }

  async getPurchasingEntityId(orderId: string) {
    const data = await this.admin.request(
      `#graphql
      query GetOrderPurchasingEntity($orderId: ID!) {
        order(id: $orderId) {
          purchasingEntity {
            __typename
            ... on Customer {
              id
            }
            ... on PurchasingCompany {
              company {
                id
              }
              location {
                id
              }
            }
          }
        }
      }
    `,
      {
        variables: { orderId },
      },
    );

    const node = data.order?.purchasingEntity;
    if (node?.__typename == 'Customer') return node.id;
    else return node?.company.id!;
  }

  async getAttributes(orderId: string) {
    const data = await this.admin.request(
      `#graphql
      query GetOrderAttributes($orderId: ID!) {
        order(id: $orderId) {
          customAttributes {
            key
            value
          }
        }
      }
    `,
      {
        variables: { orderId },
      },
    );

    return data.order?.customAttributes;
  }

  async getOrderEquipmentLines(orderId: string) {
    const lines = await queryAllConnections(async cursor =>
      this.admin
        .request(
          `#graphql
            query GetOrderLineItems($orderId: ID!, $cursor: String) {
              order(id: $orderId) {
                lineItems(first: 5, after: $cursor) {
                  nodes {
                    quantity
                    product {
                      handle
                      deviceHandle: metafield(namespace: "device", key: "handle") {
                        value
                      }
                    }
                  }
                  pageInfo {
                    hasNextPage
                    endCursor
                  }
                }
              }
            }`,
          { variables: { orderId, cursor } },
        )
        .then(data => data.order?.lineItems!),
    );

    // const quantities = lines
    //   .filter(line => line.product?.deviceHandle?.value)
    //   .reduce<{
    //     [handle in SystemDeviceHandle]?: number;
    //   }>((prev, line) => addQuantityToKey(prev, line.product!.deviceHandle!.value, line.quantity), {});

    return lines as OrderEquipment[];
  }
}
