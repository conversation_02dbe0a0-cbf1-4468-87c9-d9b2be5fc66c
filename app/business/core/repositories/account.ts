import { AdminClient } from '@/business/clients/admin-client';
import { injectable, Lifecycle, scoped } from 'tsyringe';
import { CUSTOMER_ADDRESS_VIEW_FRAGMENT } from '../constants/fragments';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class AccountRepo {
  constructor(private readonly admin: AdminClient) {}

  async getSystemOwnerContactInfo(ownerId: string) {
    const data = await this.admin.request(
      `#graphql
      query GetSystemOwnerContactInfo($ownerId: ID!) {
        node(id: $ownerId) {
          __typename
          ... on Customer {
            firstName,
            lastName,
            phone
            email
            defaultAddress {
              id
            }
            addresses {
              ...CustomerAddressView
            }
          }
          ... on Company {
            name
            mainContact {
              id
              customer {
                id
                email
                displayName
                phone
              }
            }
          }
        }
      }
      ${CUSTOMER_ADDRESS_VIEW_FRAGMENT}
      `,
      {
        variables: {
          ownerId,
        },
      },
    );

    return data?.node;
  }
}
