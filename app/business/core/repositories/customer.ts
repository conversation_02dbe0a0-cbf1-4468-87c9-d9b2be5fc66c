import { AdminClient, validateGQLUserError } from '@/business/clients/admin-client';
import { convertGidToId, queryAllConnections } from '@/lib/utils';
import {
  CompanyLocationViewFragment,
  CustomerAddressViewFragment,
  GetContactLocationAssignmentQuery,
  GetCustomerCompaniesQuery,
} from 'admin.generated';
import { CustomerInput, MailingAddressInput } from 'admin.types';
import { singleton } from 'tsyringe';
import { CustomerAdapter } from '../adapters/customer';
import { COMPANY_LOCATION_VIEW_FRAGMENT, CUSTOMER_ADDRESS_VIEW_FRAGMENT } from '../constants/fragments';
import { SystemRole } from '../types';
import { MetafieldRepo } from './metafield';

export type CustomerCompanyContactFragment = NonNullable<
  GetCustomerCompaniesQuery['customer']
>['companyContactProfiles'][number];
export type CustomerContactLocationAssignmentFragment = NonNullable<
  GetContactLocationAssignmentQuery['companyContact']
>['roleAssignments']['nodes'][number];

@singleton()
export class CustomerRepo {
  constructor(
    private readonly admin: AdminClient,
    private readonly metafieldRepo: MetafieldRepo,
    private readonly adapter: CustomerAdapter,
  ) {}

  async authorizeNonce(customerId: string, nonce: string) {
    const data = await this.metafieldRepo.setMetafields({
      ownerId: customerId,
      type: 'single_line_text_field',
      namespace: '$app:authorize',
      key: 'nonce',
      value: nonce,
    });

    return data?.[0]?.value;
  }

  async consumeNonce(customerId: string) {
    const nonce = await this.metafieldRepo.getMetafield(customerId, '$app:authorize', 'nonce');

    await this.metafieldRepo.deleteMetafields([{ ownerId: customerId, namespace: '$app:authorize', key: 'nonce' }]);

    return nonce;
  }

  async getCustomerDisplayName(customerId: string) {
    const data = await this.admin.request(
      `#graphql
        query GetCustomerDisplayName($customerId: ID!) {
          customer(id: $customerId) {
            displayName
          }
        }`,
      { variables: { customerId } },
    );

    return data?.customer?.displayName;
  }
  async getCustomerEmailFromGid(customerId: string) {
    const data = await this.admin.request(
      `#graphql
        query GetAdminCustomerEmail($customerId: ID!) {
          customer(id: $customerId) {
            email
          }
        }`,
      { variables: { customerId } },
    );

    return data?.customer?.email;
  }

  async getCustomerProfile(customerId: string) {
    const data = await this.admin.request(
      `#graphql
        query GetCustomerProfile($customerId: ID!) {
          customer(id: $customerId) {
            email
            displayName
            firstName
            lastName
            phone
            tags
          }
        }`,
      { variables: { customerId } },
    );

    return data?.customer;
  }

  async createCustomer(input: CustomerInput) {
    const data = await this.admin.request(
      `#graphql
      mutation CreateCustomer($input: CustomerInput!) {
        customerCreate(input: $input) {
          customer {
            id
          }
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          input,
        },
      },
    );

    validateGQLUserError(data?.customerCreate?.userErrors);

    return data?.customerCreate?.customer?.id;
  }

  async getCustomerIdFromEmail(email: string) {
    const data = await this.admin.request(
      `#graphql
      query GetAdminCustomerIdFromEmail($query: String!) {
        customers(first: 1, query: $query) {
          nodes {
            email
            id
          }
        }
      }`,
      {
        variables: {
          query: `email:"${email}"`,
        },
      },
    );

    const node = data?.customers?.nodes?.[0];

    if (!node || node.email != email) return undefined;

    return node.id;
  }

  async getContactLocationsInfo(
    contact: NonNullable<GetCustomerCompaniesQuery['customer']>['companyContactProfiles'][number],
  ) {
    if (!contact) return [];

    const isMainContact = contact.isMainContact;
    const output: {
      access: SystemRole;
      info: CompanyLocationViewFragment;
    }[] = [];

    if (isMainContact) {
      const locations = await queryAllConnections(cursor =>
        this.admin
          .request(
            `#graphql
        query GetAllCompanyLocations($companyId: ID!, $cursor: String) {
          company(id: $companyId) {
            locations(first: 3, after: $cursor) {
              nodes {
                ...CompanyLocationView
              }
              pageInfo {
                endCursor
                hasNextPage
              }
            }
          }
        }
        ${COMPANY_LOCATION_VIEW_FRAGMENT}`,
            {
              variables: {
                companyId: contact.company.id,
                cursor,
              },
            },
          )
          .then(res => res?.company?.locations!),
      );
      output.push(...locations.map(info => ({ access: 'write' as const, info })));
    } else {
      const assignments = await queryAllConnections(cursor =>
        this.admin
          .request(
            `#graphql
        query GetContactLocationsView($contactId: ID!, $cursor: String) {
          companyContact (id: $contactId) {
            roleAssignments(first: 3, after: $cursor) {
              nodes {
                id
                role {
                  name
                },
                companyLocation {
                  ... CompanyLocationView
                }
              }
            }
          }
        }
        ${COMPANY_LOCATION_VIEW_FRAGMENT}`,
            {
              variables: {
                contactId: contact.id,
                cursor,
              },
            },
          )
          .then(data => data.companyContact?.roleAssignments!),
      );

      output.push(
        ...assignments.map(assignment => ({
          access: assignment.role.name == 'Location admin' ? ('write' as const) : ('read' as const),
          info: assignment.companyLocation,
        })),
      );
    }

    return output;
  }

  async getCustomerCompanies(customerId: string) {
    const data = await this.admin.request(
      `#graphql
      query GetCustomerCompanies($customerId: ID!) {
        customer(id: $customerId) {
          companyContactProfiles {
            id
            company {
              name
              id
            }
            isMainContact
          }
        }
      }`,
      {
        variables: {
          customerId,
        },
      },
    );

    return data?.customer?.companyContactProfiles;
  }

  async contactHasLocationAssignment(contactId: string) {
    const data = await this.admin.request(
      `#graphql
      query ContactHasLocationAssignment($contactId: ID!) {
        companyContact(id: $contactId)  {
          roleAssignments(first: 1) {
            nodes {
              id
            }
          }
        }
      }`,
      {
        variables: {
          contactId,
        },
      },
    );

    return !!data?.companyContact?.roleAssignments?.nodes?.length;
  }

  async getContactLocationAssignment(contactId: string, locationId: string) {
    const data = await this.admin.request(
      `#graphql
      query GetContactLocationAssignment($contactId: ID!, $query: String!) {
        companyContact(id: $contactId)  {
          roleAssignments(first: 1, query: $query) {
            nodes {
              id
              companyLocation {
                id
              }
              role {
                id
                name
                note
              }
            }
          }
        }
      }`,
      {
        variables: {
          contactId,
          query: `company_location_id:${convertGidToId(locationId)}`,
        },
      },
    );

    const node = data?.companyContact?.roleAssignments?.nodes?.[0];

    if (!node || node.companyLocation.id != locationId) return undefined;

    return node;
  }

  async getContactRoleAssignments(contactId: string) {
    const data = await queryAllConnections(cursor =>
      this.admin
        .request(
          `#graphql
      query GetContactLocations($contactId: ID!, $cursor: String) {
        companyContact (id: $contactId) {
          roleAssignments(first: 3, after: $cursor, sortKey: LOCATION_NAME) {
            nodes {
              id
              role {
                id
                name
                note
              },
              companyLocation {
                id
                name
              }
            }
          }
        }
      }`,
          {
            variables: {
              contactId,
              cursor,
            },
          },
        )
        .then(data => data.companyContact?.roleAssignments!),
    );

    return data;
  }

  async getCustomerAddresses(customerId: string) {
    const data = await this.admin.request(
      `#graphql
      query GetCustomerAddresses($customerId: ID!) {
        customer(id: $customerId)  {
          addresses{
            ...CustomerAddressView
          }
        }
      }
      ${CUSTOMER_ADDRESS_VIEW_FRAGMENT}`,
      {
        variables: {
          customerId,
        },
      },
    );

    const addresses = data?.customer?.addresses;

    return addresses;
  }

  async updateCustomer(input: CustomerInput) {
    const data = await this.admin.request(
      `#graphql
      mutation UpdateCustomerInfo($input: CustomerInput!) {
        customerUpdate(input: $input) {
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          input,
        },
      },
    );

    return validateGQLUserError(data?.customerUpdate?.userErrors);
  }

  async updateAddress(customerId: string, address: Partial<CustomerAddressViewFragment> & { delete?: boolean }) {
    const addresses = (await this.getCustomerAddresses(customerId)) || [];
    let effectedIndex = -1;

    if (address.id) {
      effectedIndex = addresses.findIndex(other => other.id == address.id);
      if (effectedIndex == -1) throw new Error(`Address ${address.id} not found!`);

      if (address.delete) addresses.splice(effectedIndex, 1);
      else Object.assign(addresses[effectedIndex], this.adapter.sanitizeAddressInput(address));
    }

    const inputs: MailingAddressInput[] = addresses.map(this.adapter.sanitizeAddressInput);

    if (!address.id) {
      effectedIndex = addresses.length;
      inputs.push(this.adapter.sanitizeAddressInput(address));
    }

    const data = await this.admin.request(
      `#graphql
      mutation UpdateCustomerAddresses($customerId: ID!,$addresses: [MailingAddressInput!]!) {
        customerUpdate(input: {
          id: $customerId
          addresses: $addresses
        }) {
          customer {
            addresses {
              id
            }
          }
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          customerId,
          addresses: inputs,
        },
      },
    );

    validateGQLUserError(data?.customerUpdate?.userErrors);

    return (address.id ||
      data?.customerUpdate?.customer?.addresses?.[effectedIndex]?.id ||
      data?.customerUpdate?.customer?.addresses?.at(-1)?.id)!;
  }

  async setDefaultAddress(customerId: string, addressID: string) {
    const data = await this.admin.request(
      `#graphql
      mutation UpdateCustomerDefaultAddress($customerId: ID!, $addressID: ID!) {
        customerUpdateDefaultAddress(customerId: $customerId, addressId: $addressID) {
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          addressID,
          customerId,
        },
      },
    );

    return validateGQLUserError(data?.customerUpdateDefaultAddress?.userErrors);
  }
}
