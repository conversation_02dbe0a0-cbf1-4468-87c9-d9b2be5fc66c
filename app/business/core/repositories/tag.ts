import { AdminClient, validateGQLUserError } from '@/business/clients/admin-client';
import { singleton } from 'tsyringe';

@singleton()
export class TagRepo {
  constructor(private readonly admin: AdminClient) {}
  async getTags(ownerId: string) {
    const data = await this.admin.request(
      `#graphql
            query GetTags($ownerId: ID!) {
                node(id: $ownerId) {
                    __typename
                    ... on Order {
                        tags
                    }
                    ... on Customer {
                        tags
                    }
                    ... on Product {
                        tags
                    }
                }
            }`,
      { variables: { ownerId } },
    );

    if (data.node?.__typename == 'Order' || data.node?.__typename == 'Customer' || data.node?.__typename == 'Product') {
      return data?.node?.tags || [];
    }
    return [];
  }

  async addTags(ownerId: string, tags: string | string[]) {
    const data = await this.admin.request(
      `#graphql
        mutation AddTag($ownerId: ID!, $tags: [String!]!) {
            tagsAdd(id: $ownerId, tags: $tags) {
                userErrors {
                    field
                    message
                }
            }
        }`,
      { variables: { ownerId, tags } },
    );

    validateGQLUserError(data?.tagsAdd?.userErrors);
  }

  async removeTags(ownerId: string, tags: string | string[]) {
    const data = await this.admin.request(
      `#graphql
        mutation RemoveTag($ownerId: ID!, $tags: [String!]!) {
            tagsRemove(id: $ownerId, tags: $tags) {
                userErrors {
                    field
                    message
                }
            }
        }`,
      { variables: { ownerId, tags } },
    );

    validateGQLUserError(data?.tagsRemove?.userErrors);
  }
}
