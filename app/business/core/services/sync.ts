import { AdminClient } from '@/business/clients/admin-client';
import { StripeClient } from '@/business/clients/stripe-client';
import { CacheNone } from '@shopify/hydrogen';
import {
  MetafieldAdminAccessInput,
  MetafieldCustomerAccountAccessInput,
  MetafieldDefinitionInput,
  MetafieldOwnerType,
  MetafieldStorefrontAccessInput,
} from 'admin.types';
import Stripe from 'stripe';
import { inject, singleton } from 'tsyringe';
import { SYSTEM_FEATURES } from '../constants/features';
import { ADCRepo } from '../repositories/adc';
import { MetafieldRepo } from '../repositories/metafield';
import { ServiceRepo } from '../repositories/service';
import { ShopRepo } from '../repositories/shop';

const ENABLED_EVENTS: Stripe.WebhookEndpointCreateParams.EnabledEvent[] = [
  'customer.subscription.created',
  'customer.subscription.deleted',
  'customer.subscription.updated',
  'customer.subscription.paused',
  'customer.subscription.resumed',
  'checkout.session.completed',
];
const DESCRIPTION = 'Send events to SMM';
export const TEMPLATE_ID_METADATA_KEY = 'template-id';
export const PRODUCT_GROUP_METADATA_KEY = 'product-group';

@singleton()
export class SyncService {
  constructor(
    @inject('env') private readonly env: Env,
    private readonly adcRepo: ADCRepo,
    private readonly shopRepo: ShopRepo,
    private readonly metafieldRepo: MetafieldRepo,
    private readonly serviceRepo: ServiceRepo,
    private readonly stripe: StripeClient,
    private readonly admin: AdminClient,
  ) {}

  async synchronizeAllSystems() {
    await this.pushADCTemplatesToStripeFeatures();
    await this.pushServiceProducts();
    await this.registerStripeEventEndpoint();
    await this.pushDeviceHandlesMetafieldDefinition();
  }

  async pushServiceProducts() {
    const serviceProducts = await this.serviceRepo.getServiceProductsFromSource();
    console.log('Registering new service products');
    console.log(JSON.stringify(serviceProducts));
    await this.serviceRepo.setServiceProducts(serviceProducts);
  }

  async pushADCTemplatesToStripeFeatures() {
    const templates = await this.adcRepo.getDealerTemplates(CacheNone());

    const features = await this.stripe.entitlements.features
      .list({ archived: false })
      .autoPagingToArray({ limit: 10000 });

    for await (const template of templates) {
      const handle = template.name.toLowerCase().replace(/ +/g, '-');
      const featureIndex = features.findIndex(
        feature => feature.lookup_key == handle || feature.metadata[TEMPLATE_ID_METADATA_KEY] == template.id.toString(),
      );

      const metadata: Record<string, string> = {
        [TEMPLATE_ID_METADATA_KEY]: template.id.toString(),
      };

      if (featureIndex >= 0) {
        console.log(`Updating template feature: ${template.name}`);

        //Update feature
        await this.stripe.entitlements.features
          .update(features[featureIndex].id, {
            active: true,
            name: template.name,
            metadata,
          })
          .catch(e => console.error(e));

        //Remove the matched index
        features.splice(featureIndex, 1);
      } else {
        console.log(`Registering new template feature: ${template.name}`);

        //Add feature
        await this.stripe.entitlements.features
          .create({
            name: template.name,
            lookup_key: handle,
            metadata,
          })
          .catch(e => console.error(e));
      }
    }

    //Remove feature
    for await (const feature of features) {
      if (!feature.metadata[TEMPLATE_ID_METADATA_KEY] || !feature.active) continue;

      console.log(`Archiving old template feature: ${feature.name}`);

      await this.stripe.entitlements.features.update(feature.id, { active: false });
    }
  }

  async registerStripeEventEndpoint() {
    const host = this.env.HOST;
    const shop = this.admin.config.storeDomain;
    const endpoints = await this.stripe.webhookEndpoints.list().autoPagingToArray({ limit: 100 });

    let endpoint = endpoints.find(endpoint => endpoint.metadata?.['shop'] == shop);
    const URL = `${host}/events/stripe`;
    if (endpoint) {
      console.log(`Deleting endpoint for ${shop}...`);
      await this.stripe.webhookEndpoints.del(endpoint.id);
    }

    console.log(`Creating endpoint for ${shop}...`);
    endpoint = await this.stripe.webhookEndpoints.create({
      description: DESCRIPTION,
      enabled_events: ENABLED_EVENTS,
      metadata: {
        shop,
      },
      url: URL,
    });

    const secret = endpoint?.secret;
    console.log(`Setting endpoint secret for ${shop}: ${secret}...`);
    if (secret) {
      await this.shopRepo.setStripeEventSecret(secret);
    }
  }

  async pushDeviceHandlesMetafieldDefinition() {
    const definition: MetafieldDefinitionInput = {
      ownerType: MetafieldOwnerType.PRODUCT,
      namespace: 'device',
      key: 'handle',
      capabilities: {
        adminFilterable: { enabled: true },
        smartCollectionCondition: { enabled: true },
      },
      access: {
        admin: MetafieldAdminAccessInput.PUBLIC_READ_WRITE,
        storefront: MetafieldStorefrontAccessInput.PUBLIC_READ,
        customerAccount: MetafieldCustomerAccountAccessInput.NONE,
      },
      name: 'Equipment',
      description: 'Used to select which device this shopify product represents.',
      pin: true,
      type: 'single_line_text_field',
      validations: [
        {
          name: 'choices',
          value: JSON.stringify(
            Object.entries(SYSTEM_FEATURES)
              .filter(([_, feature]) => feature.identifier.method == 'equipment' || feature.identifier.method == 'hub')
              .map(([handle]) => handle),
          ),
        },
      ],
    };

    console.log('Upserting device handle metafield definition');
    return await this.metafieldRepo.upsertMetafieldDefinition(definition);
  }
}
