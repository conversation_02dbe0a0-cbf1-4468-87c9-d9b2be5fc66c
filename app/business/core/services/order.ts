import { StripeClient } from '@/business/clients/stripe-client';
import { convertGidToId } from '@/lib/utils';
import Stripe from 'stripe';
import { singleton } from 'tsyringe';
import { OrderAdapter, OrderEquipment } from '../adapters/order';
import { SYSTEM_FEATURES } from '../constants/features';
import {
  DEVICES_FULFILLED_TAG,
  NEW_SYSTEM_TAG,
  SERVICE_PAID_TAG,
  SYSTEM_CREATED_TAG,
  SYSTEM_TAG_PREFIX,
} from '../constants/tags';
import { DeviceRepo } from '../repositories/device';
import { OrderRepo } from '../repositories/order';
import { SystemRepo } from '../repositories/system';
import { TagRepo } from '../repositories/tag';
import { FeatureQuantities } from '../types';
import { parseDeviceAdminFields, type OrderDeviceFields, type OrderDevices, type SystemDevice } from '../types/device';
import { OrderSetupState } from '../types/order';
import { SubscriptionRepo } from '../repositories/subscription';

@singleton()
export class OrderService {
  constructor(
    private readonly repo: OrderRepo,
    private readonly stripeClient: StripeClient,
    private readonly subscriptionRepo: SubscriptionRepo,
    private readonly adapter: OrderAdapter,
    private readonly deviceRepo: DeviceRepo,
    private readonly tagRepo: TagRepo,
    private readonly systemRepo: SystemRepo,
  ) {}

  async resolveSystemSetupKeyWithFallback(orderId: string) {
    const state = await this.repo.getSetupState(orderId);

    if (state?.systemKey) return state?.systemKey;

    const { systemKey } = await this.repo.getSystemSetupInfo(orderId);

    return systemKey;
  }

  async startSystemSetup(orderId: string) {
    const [
      purchasingEntity,
      { newSystem, systemKey, systemNameAttribute },
      {
        featureQuantities,
        orderDevices: { ownerId, orderDevices },
      },
    ] = await Promise.all([
      this.repo.getPurchasingEntityInfo(orderId),
      this.repo.getSystemSetupInfo(orderId),
      this.repo.getOrderEquipmentLines(orderId).then(async orderEquipment => ({
        featureQuantities: this.adapter.convertOrderLineEquipmentToFeatures(orderEquipment),
        orderDevices: await this.getOrderDevices(orderId, { orderEquipment }),
      })),
    ]);

    const adcCustomerInput = this.adapter.convertOrderPurchasingEntityToADCCustomerInput(purchasingEntity);

    if (systemNameAttribute) adcCustomerInput.name = systemNameAttribute;

    const devices = Object.values(orderDevices).flat();
    for (const device of devices) {
      device.state = 'activating';
      device.systemKey = systemKey;
    }

    const orderState: OrderSetupState = {
      customerId:
        purchasingEntity?.__typename == 'PurchasingCompany'
          ? purchasingEntity?.contact?.customer?.id!
          : purchasingEntity?.id!,
      featureQuantities,
      newSystem,
      systemKey,
      ownerId,
    };

    const data = await Promise.all([
      //Set order setup state
      this.repo.upsertSetupState(orderId, orderState),
      this.subscriptionRepo.getOrCreateStripeCustomer(ownerId),
      //Create ADC customer
      this.systemRepo.createADCCustomerForSystem(adcCustomerInput).then(adcCustomer =>
        Promise.all([
          //Set ADC customer credentials
          this.repo.setNewADCCustomerCredentials(orderId, adcCustomer),
          //Set system setup state
          this.systemRepo.setSystem(ownerId, systemKey, {
            locationId: purchasingEntity?.__typename == 'PurchasingCompany' ? purchasingEntity.location.id : undefined,
            adcCustomerId: adcCustomer.customerId,
            firstOrderId: orderId,
          }),
          //Set order devices to be activating
          this.deviceRepo.setDevices(
            ownerId,
            devices?.map(device => ({ ...device, adcCustomerId: adcCustomer.customerId })),
          ),
        ]),
      ),
    ]);

    await this.tagRepo.addTags(
      orderId,
      [SYSTEM_TAG_PREFIX + systemKey, newSystem ? NEW_SYSTEM_TAG : undefined].filter(Boolean),
    );

    return data;
  }

  async trySetSystemIdFromSubscription(subscriptionId: string, orderId: string) {
    const orderState = await this.repo.getSetupState(orderId);

    if (!orderState?.systemKey) throw new Error('Order has not been setup!');
    if (orderState.servicePaid) {
      console.warn('Order already paid for!');
      return;
    }

    const subscription = await this.subscriptionRepo.getSystemSubscription(subscriptionId);
    const { ownerId: subscriptionOwnerId } = this.subscriptionRepo.getSystemSubscriptionMetadata(subscription);
    if (orderState.ownerId != subscriptionOwnerId) throw new Error('Subscription cannot be assigned to this order.');

    const system = await this.systemRepo.getSystem(orderState.ownerId, orderState.systemKey);
    if (!system) throw new Error('System not found!');

    if (system.subscriptionId) {
      console.warn('System already has a subscription assigned!');
      return;
    }

    system.subscriptionId = subscriptionId;
    system.setupOrderId = orderId;
    orderState.servicePaid = true;
    orderState.consentTimestamp = Date.now();

    await Promise.all([
      this.repo.setSetupState(orderId, orderState),
      this.systemRepo.setSystem(orderState.ownerId, orderState.systemKey, system),
      this.tagRepo.addTags(orderId, [SYSTEM_CREATED_TAG, SERVICE_PAID_TAG]),
    ]);
  }

  async trySetSystemIdFromCheckoutSession(session: string | Stripe.Checkout.Session, orderId?: string) {
    const checkout = typeof session == 'string' ? await this.stripeClient.checkout.sessions.retrieve(session) : session;
    const referenceId = checkout.client_reference_id;

    orderId ||= referenceId!;

    if (!referenceId || referenceId != orderId) throw new Error('Client reference does not match order id');

    if (checkout.status != 'complete') return;

    const subscriptionId = (checkout.subscription as Stripe.Subscription)?.['id'] || (checkout.subscription as string);

    if (!subscriptionId) throw new Error('No subscription associated with the checkout session!');

    return await this.trySetSystemIdFromSubscription(subscriptionId, orderId);
  }

  async upgradeAndChargeService(orderId: string, consentTimestamp: number) {
    const orderState = await this.repo.getSetupState(orderId);

    if (!orderState?.systemKey) throw new Error('Order setup state not found!');
    if (orderState.servicePaid) throw new Error('Order already paid for!');

    const system = await this.systemRepo.getSystem(orderState.ownerId, orderState.systemKey);
    if (!system?.subscriptionId) throw new Error('System does not seem to exist...');

    const systemSubscription = await this.subscriptionRepo.getSystemSubscription(system.subscriptionId);
    const { lineItems, scheduledLineItems } =
      await this.subscriptionRepo.calculateFeatureQuantityChangeServiceLineItems({
        systemSubscription,
        featuresDelta: orderState.featureQuantities,
      });

    await Promise.all([
      this.subscriptionRepo.updateSystemLineItems({
        systemSubscription,
        lineItems,
        scheduledLineItems,
      }),
      this.repo.setSetupState(orderId, {
        ...orderState,
        consentTimestamp,
        servicePaid: true,
      }),
      this.tagRepo.addTags(orderId, [SERVICE_PAID_TAG]),
    ]);
  }

  async getOrderDevices(
    orderId: string,
    {
      orderEquipment,
      ownerDevices,
    }: {
      orderEquipment?: OrderEquipment[];
      ownerDevices?: Record<string, SystemDevice>;
    } = {},
  ) {
    const ownerId = (await this.repo.getPurchasingEntityId(orderId))!;

    const orderDevices: OrderDevices = {};

    ownerDevices ||= await this.deviceRepo.getDevicesDictionary(ownerId);
    orderEquipment ||= await this.repo.getOrderEquipmentLines(orderId);

    const DEVICE_HANDLES = Object.entries(SYSTEM_FEATURES)
      .filter(([_, value]) => value.identifier?.method == 'equipment' || value.identifier?.method == 'hub')
      .map(([handle]) => handle);

    let equipmentIndex = 0;
    const orderNumber = convertGidToId(orderId);
    for (const { quantity, product } of orderEquipment) {
      const deviceHandle = product?.deviceHandle?.value;

      if (!quantity || !deviceHandle || !DEVICE_HANDLES.includes(deviceHandle)) continue;

      if (!orderDevices[deviceHandle]?.length) orderDevices[deviceHandle] = [];

      for (let i = 0; i < quantity; i++) {
        const key = `${orderNumber}-${equipmentIndex}`;
        equipmentIndex++;

        const existingDevice = ownerDevices[key] || {};

        const newDevice = {
          ...(ownerDevices[key] || {}),
          state: existingDevice.state || 'inactive',
          handle: deviceHandle as any,
          productHandle: product.handle,
          key,
          orderId,
          ownerId,
        };

        orderDevices[deviceHandle].push(newDevice);
        ownerDevices[key] = newDevice;
      }
    }

    return { ownerId, orderDevices, ownerDevices };
  }

  async tryFulfill(orderId: string, orderDevices: OrderDevices, setupState: OrderSetupState) {
    const fulfillState = this.adapter.validateAdminDeviceFields(orderDevices, setupState);

    if (!setupState.fulfilled && fulfillState.success) {
      setupState.fulfilled = true;
      await this.repo.setSetupState(orderId, setupState);
      await this.tagRepo.addTags(orderId, [DEVICES_FULFILLED_TAG]);
    }

    return fulfillState;
  }

  async upsertAdminFields(orderId: string, upsertFields: OrderDeviceFields) {
    const { ownerId, orderDevices } = await this.getOrderDevices(orderId);

    const setDevices: SystemDevice[] = Object.entries(orderDevices)
      .map(([handle, devices]) =>
        devices.map((device, index) => {
          const adminFields = parseDeviceAdminFields((upsertFields?.[handle]?.[index] || {}) as any, true);

          if (!adminFields?.success) throw new Error('Failed to parse admin fields');

          return { ...device, ...adminFields.data };
        }),
      )
      .flat();

    await this.deviceRepo.setDevices(ownerId, setDevices);
  }
}
