import { AdminClient } from '@/business/clients/admin-client';
import { injectable, Lifecycle, scoped } from 'tsyringe';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class TriggerService {
  constructor(private readonly adminClient: AdminClient) {}

  async triggerFlow(handle: string, payload?: any) {
    const data = await this.adminClient.request(
      `#graphql
      mutation TriggerFlow($handle: String!, $payload: JSON) {
        flowTriggerReceive(handle: $handle, payload: $payload) {
          userErrors {
            field
            message
          }
        }
      }`,
      {
        variables: {
          handle,
          payload,
        },
      },
    );

    return data;
  }
}
