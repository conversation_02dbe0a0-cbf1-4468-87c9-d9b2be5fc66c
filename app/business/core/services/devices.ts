import { AdminClient } from '@/business/clients/admin-client';
import { convertGidToId, verifyHMACSHA512Signature } from '@/lib/utils';
import Strip<PERSON> from 'stripe';
import { inject, injectable, Lifecycle, scoped } from 'tsyringe';
import { ADCRepo } from '../repositories/adc';
import { CompanyRepo } from '../repositories/company';
import { DeviceRepo } from '../repositories/device';
import { ServiceRepo } from '../repositories/service';
import { SubscriptionRepo } from '../repositories/subscription';
import { SystemRepo } from '../repositories/system';
import { FeatureQuantities } from '../types';
import { ADCEquipment } from '../types/adc';
import {
  CUSTOMER_DEVICE_FIELDS,
  parseDeviceAdminFields,
  parseDeviceCustomerFields,
  SystemDevice,
} from '../types/device';
import { TriggerService } from './trigger';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class DeviceService {
  constructor(
    @inject('env') private readonly env: Env,
    @inject('request') private readonly request: Request,
    private readonly deviceRepo: DeviceRepo,
    private readonly companyRepo: CompanyRepo,
    private readonly serviceRepo: ServiceRepo,
    private readonly systemRepo: SystemRepo,
    private readonly subscriptionRepo: SubscriptionRepo,
    private readonly adcRepo: ADCRepo,
    private readonly triggerService: TriggerService,
  ) {}

  async triggerDeviceFlow(
    device: SystemDevice,
    flow:
      | 'device-ready-to-activate'
      | 'device-activated-on-adc'
      | 'vehicle-vin-updated'
      | 'device-sch-for-deactivation'
      | 'device-unsch-for-deactivation'
      | 'device-ready-to-deactivate'
      | 'device-deactivated-on-adc',
  ) {
    const contactCustomerId = convertGidToId(
      device?.ownerId?.includes('Company')
        ? await this.companyRepo.getPrimaryContactCustomerId(device.ownerId)
        : device.ownerId,
    );
    await this.triggerService.triggerFlow(flow, {
      device,
      customer_id: contactCustomerId,
    });
  }

  async tryActivateDevice(
    device: SystemDevice,
    {
      devices,
      ...args
    }: {
      devices: SystemDevice[];
    } & ({ subscribedFeatures: FeatureQuantities } | { systemSubscription: Stripe.Subscription | string | undefined }),
  ): Promise<SystemDevice> {
    const wasScheduledForDeactivation = device.state == 'deactivating';

    device = { ...device, state: 'activating' };

    if (wasScheduledForDeactivation) await this.triggerDeviceFlow(device, 'device-unsch-for-deactivation');

    delete device.activationErrors;

    if (!device.adcCustomerId) {
      device.activationErrors = {
        formErrors: ['Your device has not been assigned to a system!'],
      };
      return device;
    }

    const adminFields = parseDeviceAdminFields(device);
    if (!adminFields?.success) {
      device.activationErrors = {
        formErrors: ['Your device is still being fulfilled!'],
      };
      return device;
    }

    const customerFields = parseDeviceCustomerFields(device);
    if (!customerFields?.success) {
      device.activationErrors = customerFields?.error.flatten();
      return device;
    }

    for (const field of CUSTOMER_DEVICE_FIELDS) {
      const value = (device as any)[field];
      if (!value) continue;

      if (devices.some(other => other.key != device.key && (other as any)[field] === value)) {
        device.activationErrors = {
          fieldErrors: {
            [field]: ['This already exists on another device on your account!'],
          },
        };
        return device;
      }
    }

    const subscribedFeatures =
      'subscribedFeatures' in args
        ? args.subscribedFeatures
        : args.systemSubscription
          ? await this.subscriptionRepo.getFeatureQuantities(args.systemSubscription)
          : {};

    const inUseFeatures = devices.reduce(
      (acc, d) => acc + (d.key != device.key && Boolean(d.deviceId) && d.handle == device.handle ? 1 : 0),
      0,
    );

    const requiresUpgrade = inUseFeatures >= (subscribedFeatures[device.handle] || 0);
    if (requiresUpgrade) {
      device.activationErrors = {
        formErrors: ["To activate your device, you must upgrade your system's subscription."],
      };
      return device;
    }

    device = { ...device, ...customerFields.data, ...adminFields.data };

    if (device.deviceId) {
      device = await this.makeDeviceActive(device, { deviceId: device.deviceId, deviceName: device.nickname! });
    } else {
      await this.triggerDeviceFlow(device, 'device-ready-to-activate');
    }

    return device;
  }

  async activateDevice(device: SystemDevice) {
    const adcCustomerId = device.adcCustomerId!;
    const GENERIC_ERROR = 'Failed to activate device. Please contact support.';

    try {
      //If possible, implement API level device activation, otherwise add tag for steve to activate.
      switch (device.handle) {
        case 'car-tracker':
        case 'fleet-tracker':
          const output = await this.adcRepo.addCarConnector(adcCustomerId, {
            imei: device.IMEI!,
            serialNumber: device.serialNumber!,
            vin: device.VIN!,
            name: device.nickname!,
          });

          if (!output.deviceId) {
            device.activationErrors = {
              formErrors: [output.message || GENERIC_ERROR],
            };
          }

          break;
        default:
          console.warn(`Tried to activate a ${device.handle} device but there is no supported ADC endpoint for that.`);
          device.activationErrors = {
            formErrors: [GENERIC_ERROR],
          };
          break;
      }
    } catch (error) {
      console.error(error);
      //Last resort... maybe the device is already activated and we just need to find it?
      device = await this.tryActivateDirectlyFromEquipment(device).catch(e => {
        console.error(e);
        device.activationErrors = {
          formErrors: [GENERIC_ERROR],
        };
        return device;
      });
    }

    return device;
  }

  private async tryActivateDirectlyFromEquipment(device: SystemDevice) {
    if (device.deviceId) return device;

    const equipment = await this.adcRepo.getCustomerEquipment(device.adcCustomerId!, true);
    const foundEquipment = equipment.find(
      e =>
        ('IMEI' in device && e.imei == device.IMEI) ||
        ('serialNumber' in device && e.serialNumber == device.serialNumber),
    );

    if (!foundEquipment) return device;

    return await this.makeDeviceActive(device, foundEquipment);
  }

  async processADCEvent() {
    const { request } = this;
    const body = await request.text();
    const signature = request.headers.get('Signature')!;
    const SECRET = this.env.ADC_WEBHOOK_SECRET;

    const isValid = await verifyHMACSHA512Signature(body, SECRET, signature);

    if (!isValid) throw new Error('Invalid signature');

    const { ChangeType, CustomerId, DeviceId } = JSON.parse(body) as {
      CreateDateUtc: string;
      CustomerId: number;
      DeviceId: number;
      ChangeType: number;
    };

    switch (ChangeType) {
      case 1: //Device added
        await this.handleADCEquipmentAdded(CustomerId, DeviceId);
        break;
      case 2: //Device removed
        await this.handleADCEquipmentRemoved(CustomerId, DeviceId);
        break;
      default:
        break;
    }
  }

  async handleADCEquipmentRemoved(adcCustomerId: number, deviceId: number) {
    const adcCustomer = await this.adcRepo.getCustomer(adcCustomerId);
    if (!adcCustomer) throw new Error('Missing ADC customer!');

    const ownerId = adcCustomer.dealerCustomerId;

    if (!ownerId) throw new Error('Missing owner ID!');

    const devices = await this.deviceRepo.getDevices(ownerId);
    const systemDevice = devices.find(d => d.adcCustomerId == adcCustomerId && d.deviceId == deviceId);

    if (!systemDevice) {
      console.warn(`No system device found to deactivate for ADC device ID ${deviceId}`);
      return;
    }

    if (systemDevice.state != 'deactivating') {
      console.warn(`Found device for ADC device ID ${deviceId}, but the device was not deactivating...`);
    }

    const inActiveDevice = await this.makeDeviceInactive(systemDevice);

    await this.deviceRepo.setDevices(ownerId, [inActiveDevice]);
  }

  async handleADCEquipmentAdded(adcCustomerId: number, deviceId: number) {
    const equipmentPromise = this.adcRepo.getCustomerEquipment(adcCustomerId, true);
    const adcCustomer = await this.adcRepo.getCustomer(adcCustomerId);
    if (!adcCustomer) throw new Error('Missing ADC customer!');

    const ownerId = adcCustomer.dealerCustomerId;

    if (!ownerId) throw new Error('Missing owner ID!');

    const devices = await this.deviceRepo.getDevices(ownerId);

    const adcDevice = (await equipmentPromise).find(e => e.deviceId == deviceId);

    if (!adcDevice) throw new Error(`No device found for ADC device ID ${deviceId}`);

    const systemDevice = devices.find(
      //Device is owned by the system, is activating, and the handle matches the identified handle
      //TODO: Create a more robust way to identify the device
      sysDevice =>
        ('IMEI' in sysDevice && adcDevice.imei && adcDevice.imei === sysDevice['IMEI']) ||
        ('serialNumber' in sysDevice && adcDevice.serialNumber && adcDevice.serialNumber === sysDevice['serialNumber']),
    );

    if (!systemDevice) throw new Error(`No eligible device found.`);

    if (systemDevice.deviceId == deviceId) {
      console.log(`Device ${systemDevice.nickname} is already active.`);
      return;
    }

    const activatedDevice = await this.makeDeviceActive(systemDevice, adcDevice);

    await this.deviceRepo.setDevices(ownerId, [activatedDevice]);
  }

  async scheduleDeviceForDeactivation(
    device: SystemDevice,
    schedule?: { scheduledFor: Date; consentAt: Date },
  ): Promise<SystemDevice> {
    if (device.deviceId) {
      device = { ...device, state: 'deactivating' } as SystemDevice;
      if (schedule) {
        device.deactivationConsentAt = schedule.consentAt.getTime();
        device.deactivationScheduledFor = schedule.scheduledFor.getTime();
        await this.triggerDeviceFlow(device, 'device-sch-for-deactivation');
      } else {
        await this.triggerDeviceFlow(device, 'device-ready-to-deactivate');
      }

      return device;
    } else {
      return await this.makeDeviceInactive(device);
    }
  }

  async makeDeviceInactive(device: SystemDevice) {
    const inActiveDevice: SystemDevice = {
      ...device,
      state: 'inactive',
      deactivatedAt: Date.now(),
    };
    delete inActiveDevice.deviceId;
    delete inActiveDevice.nickname;
    for (const field of CUSTOMER_DEVICE_FIELDS) {
      delete (inActiveDevice as any)[field];
    }

    console.log(`Deactivated ${device.handle} device (${device.nickname}): ${device.deviceId}`);

    await this.triggerDeviceFlow(device, 'device-deactivated-on-adc');

    return inActiveDevice;
  }

  async makeDeviceActive(device: SystemDevice, equipment: Pick<ADCEquipment, 'deviceId' | 'deviceName'>) {
    const activatedDevice: SystemDevice = { ...device, state: 'active' };

    activatedDevice.deviceId = equipment.deviceId;
    activatedDevice.nickname ||= equipment.deviceName;
    activatedDevice.activatedAt = Date.now();
    delete activatedDevice.activationErrors;

    console.log(
      `Activated ${activatedDevice.handle} device (${activatedDevice.nickname}): ${activatedDevice.deviceId}`,
    );
    await this.triggerDeviceFlow(activatedDevice, 'device-activated-on-adc');
    return activatedDevice;
  }

  async quoteLineItemsForDeviceActivationChange(device: SystemDevice, active: boolean, devices: SystemDevice[]) {
    if (!device.systemKey) throw new Error('System is not setup!');

    const relevantDevices = devices.filter(d => d.systemKey == device.systemKey && d.handle == device.handle);
    const activeQuantity = relevantDevices.reduce(
      (acc, d) => acc + ((d.key == device.key && active) || d.state != 'inactive' ? 1 : 0),
      0,
    );
    const scheduledActiveQuantity = relevantDevices.reduce(
      (acc, d) => acc + ((d.key == device.key ? active : d.state == 'active' || d.state == 'activating') ? 1 : 0),
      0,
    );

    const system = await this.systemRepo.getSystem(device.ownerId, device.systemKey);
    if (!system?.subscriptionId) throw new Error('System is not setup!');

    const systemSubscription = await this.subscriptionRepo.getSystemSubscription(system?.subscriptionId!, ['schedule']);
    const schedule = await this.subscriptionRepo.getSystemSubscriptionSchedule(systemSubscription);

    const lineItems = this.subscriptionRepo.getSubscriptionLineItems(systemSubscription);
    const scheduledLineItems = this.subscriptionRepo.getSubscriptionScheduleLineItems(schedule);

    let hasChange = false;
    const newLineItems = await this.serviceRepo.changeLineItemsByFeatures({
      lineItems,
      change: features => {
        const existingQuantity = features[device.handle] || 0;
        const newQuantity = Math.max(activeQuantity, existingQuantity);

        if (newQuantity != existingQuantity) hasChange = true;

        return { ...features, [device.handle]: newQuantity };
      },
    });

    const newScheduledLineItems = await this.serviceRepo.changeLineItemsByFeatures({
      lineItems: scheduledLineItems || lineItems,
      change: features => {
        const existingQuantity = features[device.handle] || 0;
        const newQuantity = scheduledActiveQuantity;

        if (newQuantity != existingQuantity) hasChange = true;

        return { ...features, [device.handle]: newQuantity };
      },
    });

    return {
      system,
      systemSubscription,
      lineItems: newLineItems,
      scheduledLineItems: newScheduledLineItems,
      hasChange,
    };
  }
}
