import { StripeClient } from '@/business/clients/stripe-client';
import {
  addQuantityToKey,
  convertAddonIdsToAddonQuantities,
  convertAddonQuantitiesToFeatureQuantities,
  convertDevicesToFeatureQuantities,
  partition,
} from '@/lib/utils';
import Stripe from 'stripe';
import { inject, injectable, Lifecycle, scoped } from 'tsyringe';
import { SystemAdapter } from '../adapters/system';
import { ADCRepo } from '../repositories/adc';
import { DeviceRepo } from '../repositories/device';
import { ServiceRepo } from '../repositories/service';
import { ShopRepo } from '../repositories/shop';
import { SubscriptionRepo } from '../repositories/subscription';
import { SystemRepo } from '../repositories/system';
import { FeatureQuantities } from '../types';
import { SystemDevice } from '../types/device';
import { DeviceService } from './devices';
import { OrderService } from './order';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class SystemService {
  constructor(
    @inject('request') private readonly request: Request,
    @inject('waitUntil') private readonly waitUntil: WaitUntil,
    private readonly adapter: SystemAdapter,
    private readonly stripe: StripeClient,
    private readonly shopRepo: ShopRepo,
    private readonly orderService: OrderService,
    private readonly serviceRepo: ServiceRepo,
    private readonly adcRepo: ADCRepo,
    private readonly subscriptionRepo: SubscriptionRepo,
    private readonly systemRepo: SystemRepo,
    private readonly deviceRepo: DeviceRepo,
    private readonly deviceService: DeviceService,
  ) {}

  async processSystemUpdateEvent() {
    const { request, waitUntil, shopRepo, stripe } = this;
    const body = await request.text();
    const signature = request.headers.get('stripe-signature')!;

    const clientSecret = await shopRepo.getStripeEventSecret();

    const event = await stripe.webhooks.constructEventAsync(body, signature, clientSecret);

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.deleted':
      case 'customer.subscription.updated':
      case 'customer.subscription.paused':
      case 'customer.subscription.resumed':
        waitUntil(this.applySystemService(event.data.object));
        break;
      case 'checkout.session.completed':
        //Double check that the session was completed
        waitUntil(this.orderService.trySetSystemIdFromCheckoutSession(event.data.object));
        break;
      default:
        break;
    }
  }

  async applySystemService(subscription: Stripe.Subscription) {
    const metadata = this.subscriptionRepo.getSystemSubscriptionMetadata(subscription);

    if (!metadata?.adcCustomerId) {
      console.log('Updated subscription is not associated with an ADC account.');
      return;
    }

    const { adcCustomerId, ownerId, systemKey } = metadata;

    if (subscription.status == 'canceled') {
      console.log('Subscription canceled. Deleting system and ADC customer...');
      await Promise.allSettled([
        this.adcRepo.deleteCustomer(adcCustomerId),
        this.systemRepo.deleteSystemMetafield(ownerId, systemKey),
      ]);

      return;
    }

    //If the subscription is not active, or has no items, lock the customer.
    if (
      subscription.status != 'active' ||
      !subscription.items.data.reduce((prev, item) => prev + (item.quantity || 0), 0)
    ) {
      await this.adcRepo.setCustomerLockout(adcCustomerId, true, 'System is inactive.');

      return;
    }

    const [_, adcCustomer, subscriptionAddons, devices] = await Promise.all([
      this.adcRepo.setCustomerLockout(adcCustomerId, false, 'Service applied.'),
      this.adcRepo.getCustomer(adcCustomerId),
      this.serviceRepo.getTotalSubscriptionAddons(subscription),
      this.deviceRepo.getDevices(ownerId),
    ]);

    const currentAddons = convertAddonIdsToAddonQuantities(adcCustomer?.servicePlanInfo?.addons || []);
    const targetAddons = this.adapter.applyServicePackageAddonsConstraints(
      subscriptionAddons,
      adcCustomer?.servicePlanInfo?.packageId,
    );

    await this.adcRepo.tryApplyAddonQuantities(adcCustomerId, currentAddons, targetAddons);

    const systemDevices = devices.filter(device => device.systemKey == systemKey);
    const systemFeatures = convertAddonQuantitiesToFeatureQuantities(subscriptionAddons);

    await Promise.allSettled([
      this.flagDevicesForDeactivation(systemDevices, systemFeatures),
      this.flagDevicesForActivation(systemDevices, systemFeatures),
    ]);
  }

  async flagDevicesForActivation(systemDevices: SystemDevice[], subscribedFeatures: FeatureQuantities) {
    let inUseFeatures = convertDevicesToFeatureQuantities(systemDevices.filter(device => device.deviceId));
    const upgradeableDevices = systemDevices.filter(device => {
      if (device.state != 'activating') return false;
      if ((inUseFeatures[device.handle] || 0) >= (subscribedFeatures[device.handle] || 0)) return false;

      inUseFeatures = addQuantityToKey(inUseFeatures, device.handle, 1);

      return true;
    });

    await Promise.allSettled(
      upgradeableDevices?.map(device =>
        this.deviceService
          .tryActivateDevice(device, {
            devices: systemDevices,
            subscribedFeatures,
          })
          .then(device => this.deviceRepo.setDevices(device.ownerId, [device])),
      ),
    );
  }

  async flagDevicesForDeactivation(systemDevices: SystemDevice[], subscribedFeatures: FeatureQuantities) {
    let inUseFeatures = convertDevicesToFeatureQuantities(systemDevices.filter(device => device.deviceId));
    const downgradeableDevices = systemDevices.filter(device => {
      if (device.state != 'deactivating') return false;
      if ((inUseFeatures[device.handle] || 0) <= (subscribedFeatures[device.handle] || 0)) return false;

      inUseFeatures = addQuantityToKey(inUseFeatures, device.handle, -1);
      return true;
    });

    await this.deviceRepo.setDevices(
      downgradeableDevices?.at(0)?.ownerId!,
      await Promise.all(downgradeableDevices.map(device => this.deviceService.scheduleDeviceForDeactivation(device))),
    );
  }
}
