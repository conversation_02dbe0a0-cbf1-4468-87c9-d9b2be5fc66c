/* 
static fillHomeInputWithShopifyCustomer(
    shopifyCustomer: CustomerForADCFragment,
    { loginName, email, firstName, lastName, phoneNumber, address, name }: Omit<CreateHomeInput, 'shopifyCustomerId'>,
  ): FilledCreateHomeInput {
    const shopifyAddress = shopifyCustomer.defaultAddress!;

    const filledAddress: Omit<ADCAddress, 'countryId'> = {
      street1: address?.street1 || shopifyAddress.address1!,
      street2: address?.street2 || shopifyAddress.address2!,
      city: address?.city || shopifyAddress.city!,
      zip: address?.zip || shopifyAddress.zip!,
      state: address?.state || shopifyAddress.provinceCode!,
    };

    return {
      address: filledAddress,
      email: email || shopifyCustomer.email!,
      firstName: firstName || shopifyCustomer.firstName || shopifyCustomer.defaultAddress?.firstName!,
      lastName: lastName || shopifyCustomer.lastName || shopifyCustomer.defaultAddress?.lastName!,
      loginName: loginName || undefined!,
      name: name || undefined!,
      phoneNumber: phoneNumber || shopifyCustomer.phone || shopifyCustomer.defaultAddress?.phone || '************',
    };
  }
*/

/*

  static calculateServiceItemLineUpdates(serviceItems: ServiceItem[], subscription: Stripe.Subscription | undefined) {
    const updates = matchArray<Stripe.SubscriptionUpdateParams.Item, Stripe.SubscriptionItem, ServiceItem>(
      subscription?.items?.data || [],
      serviceItems,
      //Update
      (subscriptionItem, serviceItem) => ({
        id: subscriptionItem.id,
        price: serviceItem.service.price.id,
        quantity: serviceItem.quantity,
      }),
      //Add
      serviceItem => ({
        price: serviceItem.service.price.id,
        quantity: serviceItem.quantity,
      }),
      //Remove
      subscriptionItem => ({
        id: subscriptionItem.id,
        deleted: true,
      }),
      //Skip?
      (subscriptionItem, serviceItem) =>
        serviceItem.service.price.id == subscriptionItem.price.id && serviceItem.quantity == subscriptionItem.quantity,
    );

    return updates;
  }
 */

/*

  static async getFilledCreateHomeInput(
    { admin }: AppAPIContext<'admin'>,
    { shopifyCustomerId: customerGid, ...homeInput }: CreateHomeInput,
  ) {
    const customerQuery = await admin.request(
      `#graphql
        fragment AddressForADC on MailingAddress {
            address1
            address2
            city
            countryCodeV2
            country
            phone
            provinceCode
            province
            zip
            firstName
            lastName
        }
        fragment CustomerForADC on Customer {
            defaultAddress {
                ...AddressForADC
            }
            email
            firstName
            lastName
            id
            phone
        }
        query GetCustomerForADC($customerId: ID!) {
            customer(id: $customerId) {
                ...CustomerForADC
            }
        }
      `,
      {
        variables: {
          customerId: customerGid,
        },
      },
    );

    const shopifyCustomer = customerQuery?.customer;

    if (!shopifyCustomer) throw new Error(`Could not find shopify customer (${customerGid}) to create adc customer from...`);

    const filledHomeInput = HomeAdapter.fillHomeInputWithShopifyCustomer(shopifyCustomer, homeInput);

    return filledHomeInput;
  }
  */
