import { FeatureQuantities, SystemFeature, SystemFeatureHandle } from '../types';
import { ADCEquipment } from '../types/adc';
import { ADCAddon } from '../types/addons';

// export const ZWAVE_ADDON_IDS = [23, 24, 25, 36, 43, 47, 48, 52, 57, 59, 64, 87, 108, 119, 120];

export const SYSTEM_FEATURES: { [handle in SystemFeatureHandle]: SystemFeature } = {
  /**HUB */
  hub: {
    name: 'Smart Hub',
    imageUrl: '',
    identifier: { method: 'hub' },
    limit: 1,
  },
  /**EQUIPMENT */
  'flex-aware': {
    name: 'Flex Aware',
    imageUrl:
      'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/wireless-gate-sensor_fb4cf3d7-7b1a-4e7c-bd1b-01b05eae71df.png?v=1699631810',
    identifier: { method: 'equipment', deviceType: 1, groupId: 208 },
    limit: 10,
  },
  'car-tracker': {
    name: 'Car Tracker',
    imageUrl:
      'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/fleet_tracker_transparetn_background_3x_d6408eed-4344-4630-b61c-20b65e0b19b0.png?v=1747941343',
    identifier: { method: 'equipment', deviceType: 66 }, //Not sure group
    limit: 5,
  },
  'fleet-tracker': {
    name: 'Fleet Tracker',
    identifier: { method: 'equipment', deviceType: 46, groupId: 299 },
    imageUrl:
      'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/fleet_tracker_transparetn_background_3x_d6408eed-4344-4630-b61c-20b65e0b19b0.png?v=1747941343',
    limit: 100,
    isBusinessOnly: true,
  },
  camera: {
    name: 'Camera',
    imageUrl: '',
    identifier: { method: 'equipment', deviceType: 11 },
    limit: 64,
  },
  /**ADDONS */
  'my-circle': {
    name: 'My Circle',
    imageUrl: '',
    identifier: { method: 'addon', addon: ADCAddon.Noonlight },
    limit: 100,
  },
  'safety-button': {
    name: 'Safety Button',
    imageUrl: '',
    identifier: { method: 'addon', addon: ADCAddon.Ambient },
    limit: 1,
  },
};

export const ADDON_SYSTEM_FEATURES: { [addonId in ADCAddon]?: FeatureQuantities } = {
  [ADCAddon.FlexIO]: {
    'flex-aware': 1,
  },
  [ADCAddon.ConnectedCar]: {
    'car-tracker': 1,
  },
  [ADCAddon.ConnectedFleet]: {
    'fleet-tracker': 1,
  },
  [ADCAddon.TwoFiftyMBExtraVideoStorage]: {
    camera: 4,
  },
  [ADCAddon.Noonlight]: {
    'my-circle': 1,
  },
  [ADCAddon.Ambient]: {
    'safety-button': 1,
  },
};

export const identifySystemFeatureFromADCEquipment = (adcDevice: ADCEquipment) => {
  const identifiedFeature = Object.entries(SYSTEM_FEATURES).find(
    ([_, { identifier }]) =>
      identifier.method == 'equipment' &&
      identifier.deviceType == adcDevice.deviceType &&
      (!identifier.groupId || identifier.groupId == adcDevice.groupId),
  );

  if (!identifiedFeature) return undefined;

  return {
    handle: identifiedFeature?.[0],
    feature: identifiedFeature?.[1],
  };
};
