import { ADCServicePackage } from '../types/adc';

export const RESIDENTIAL_SERVICE_PACKAGE: ADCServicePackage = {
  id: 320,
  description: 'All Aware',
  groupDescription: 'No Equipment With AllAware',
  includedFeatures: [],
  freeAddOns: [],
  otherAddOns: [
    {
      feature: 14,
      maxQuantity: 1,
    },
    {
      feature: 16,
      maxQuantity: 16,
    },
    {
      feature: 35,
      maxQuantity: 1,
    },
    {
      feature: 50,
      maxQuantity: 10,
    },
    {
      feature: 52,
      maxQuantity: 1,
    },
    {
      feature: 53,
      maxQuantity: 1,
    },
    {
      feature: 62,
      maxQuantity: 1,
    },
    {
      feature: 66,
      maxQuantity: 5,
    },
    {
      feature: 77,
      maxQuantity: 1,
    },
    {
      feature: 83,
      maxQuantity: 1,
    },
    {
      feature: 98,
      maxQuantity: 10,
    },
    {
      feature: 101,
      maxQuantity: 1,
    },
    {
      feature: 108,
      maxQuantity: 1,
    },
    {
      feature: 128,
      maxQuantity: 1,
    },
    {
      feature: 130,
      maxQuantity: 1,
    },
    {
      feature: 135,
      maxQuantity: 1,
    },
    {
      feature: 137,
      maxQuantity: 1,
    },
    {
      feature: 187,
      maxQuantity: 100,
    },
  ],
};

export const BUSINESS_SERVICE_PACKAGE: ADCServicePackage = {
  id: 319,
  description: 'Connected Fleet',
  groupDescription: 'Video Only',
  includedFeatures: [
    {
      feature: 35,
      maxQuantity: 1,
    },
    {
      feature: 174,
      maxQuantity: 1,
    },
  ],
  freeAddOns: [
    {
      feature: 185,
      maxQuantity: 1,
    },
  ],
  otherAddOns: [
    {
      feature: 12,
      maxQuantity: 26,
    },
    {
      feature: 14,
      maxQuantity: 1,
    },
    {
      feature: 16,
      maxQuantity: 16,
    },
    {
      feature: 21,
      maxQuantity: 1,
    },
    {
      feature: 23,
      maxQuantity: 1,
    },
    {
      feature: 24,
      maxQuantity: 1,
    },
    {
      feature: 25,
      maxQuantity: 1,
    },
    {
      feature: 27,
      maxQuantity: 1,
    },
    {
      feature: 43,
      maxQuantity: 1,
    },
    {
      feature: 48,
      maxQuantity: 1,
    },
    {
      feature: 50,
      maxQuantity: 10,
    },
    {
      feature: 52,
      maxQuantity: 1,
    },
    {
      feature: 53,
      maxQuantity: 1,
    },
    {
      feature: 57,
      maxQuantity: 1,
    },
    {
      feature: 58,
      maxQuantity: 1,
    },
    {
      feature: 62,
      maxQuantity: 1,
    },
    {
      feature: 64,
      maxQuantity: 1,
    },
    {
      feature: 77,
      maxQuantity: 1,
    },
    {
      feature: 89,
      maxQuantity: 1,
    },
    {
      feature: 98,
      maxQuantity: 10,
    },
    {
      feature: 101,
      maxQuantity: 1,
    },
    {
      feature: 108,
      maxQuantity: 1,
    },
    {
      feature: 110,
      maxQuantity: 1,
    },
    {
      feature: 111,
      maxQuantity: 1,
    },
    {
      feature: 112,
      maxQuantity: 8,
    },
    {
      feature: 113,
      maxQuantity: 4,
    },
    {
      feature: 119,
      maxQuantity: 1,
    },
    {
      feature: 120,
      maxQuantity: 1,
    },
    {
      feature: 125,
      maxQuantity: 1,
    },
    {
      feature: 126,
      maxQuantity: 1,
    },
    {
      feature: 127,
      maxQuantity: 1,
    },
    {
      feature: 128,
      maxQuantity: 1,
    },
    {
      feature: 130,
      maxQuantity: 1,
    },
    {
      feature: 135,
      maxQuantity: 1,
    },
    {
      feature: 138,
      maxQuantity: 1,
    },
    {
      feature: 139,
      maxQuantity: 10,
    },
    {
      feature: 162,
      maxQuantity: 1,
    },
    {
      feature: 171,
      maxQuantity: 1,
    },
    {
      feature: 174,
      maxQuantity: 99,
    },
    {
      feature: 186,
      maxQuantity: 1,
    },
    {
      feature: 187,
      maxQuantity: 100,
    },
    {
      feature: 192,
      maxQuantity: 1,
    },
    {
      feature: 193,
      maxQuantity: 1,
    },
    {
      feature: 198,
      maxQuantity: 64,
    },
  ],
};
