export const CUSTOMER_ADDRESS_VIEW_FRAGMENT = `#graphql
  fragment CustomerAddressView on MailingAddress {
    id
    address1
    address2
    firstName
    lastName
    company
    phone
    city
    provinceCode
    province
    zip
    country
  }
` as const;

export const COMPANY_LOCATION_VIEW_FRAGMENT = `#graphql
  fragment CompanyAddressView on CompanyAddress {
    id
    address1
    address2
    companyName
    firstName
    lastName
    phone
    city
    zoneCode
    zip
    recipient
  }
  fragment CompanyLocationView on CompanyLocation {
    id
    billingAddress {
      ...CompanyAddressView
    }
    shippingAddress {
      ...CompanyAddressView
    }
    name
    phone
    ordersCount {
      count
      precision
    }
  }
` as const;
