import { GenerationConfig, SchemaType } from '@google/generative-ai';
import { NHTSADecodeVINResponse, NHTSADecodeVINResponseResult, VINMakeModelYear } from '../types/vin';

export const VIN_IDENTIFIER_MODEL = 'gemini-2.5-flash-preview-05-20';
export const VIN_IDENTIFIER_SYSTEM_INSTRUCTIONS =
  "Identify whatever VINs and license plates (number and state abbreviation code) you can. If you can't identify anything or are EVEN JUST A LITTLE unsure, please output an error_coaching string that might help the user get a better picture. Also, remember to do a final check that all of your VINs are in a valid VIN format, and all your license plates are in a valid US state.";
export const VIN_IDENTIFIER_CONFIG: GenerationConfig = {
  temperature: 1,
  topP: 0.95,
  topK: 40,
  maxOutputTokens: 8192,
  responseMimeType: 'application/json',
  responseSchema: {
    type: SchemaType.OBJECT,
    properties: {
      VINs: {
        type: SchemaType.ARRAY,
        items: {
          type: SchemaType.STRING,
        },
      },
      plates: {
        type: SchemaType.ARRAY,
        items: {
          type: SchemaType.OBJECT,
          properties: {
            plate: {
              type: SchemaType.STRING,
            },
            stateCode: {
              type: SchemaType.STRING,
            },
          },
          required: ['plate', 'stateCode'],
        },
      },
      error_coaching: {
        type: SchemaType.STRING,
      },
    },
  },
};

export const VIN_IDENTIFIER_IMAGE_SIZE = 960;
export const VIN_REGEX = /[A-HJ-NPR-Z\d]{8}[\dX][A-HJ-NPR-Z\d]{8}/gm;

export const NHTSA_ERROR_CODE_ID = 143;
export const NHTSA_MAKE_ID = 26;
export const NHTSA_MODEL_ID = 28;
export const NHTSA_YEAR_ID = 29;

export const matchAllVINGroupsInText = (text: string) => {
  const VINGroups = [...text.matchAll(VIN_REGEX)]
    .filter(group => group)
    .map(group => ({ VIN: group[0], startIndex: group.index }));

  return VINGroups;
};

export const getValueFromNHTSAResults = (results: NHTSADecodeVINResponseResult[], variableId: number) =>
  results.find(result => result.VariableId == variableId)?.Value;

export const getNHTSAResponseErrorCode = (response: NHTSADecodeVINResponse) =>
  getValueFromNHTSAResults(response.Results, NHTSA_ERROR_CODE_ID);

export const getNHTSAResponseToMMY = (response: NHTSADecodeVINResponse): VINMakeModelYear | undefined => {
  const MMY: VINMakeModelYear = {
    make: getValueFromNHTSAResults(response.Results, NHTSA_MAKE_ID)!,
    model: getValueFromNHTSAResults(response.Results, NHTSA_MODEL_ID)!,
    year: getValueFromNHTSAResults(response.Results, NHTSA_YEAR_ID)!,
  };

  if (!MMY.make && !MMY.model && !MMY.year) return undefined;

  return MMY;
};
