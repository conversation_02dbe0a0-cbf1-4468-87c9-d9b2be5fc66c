import { gidSchema } from '@/lib/utils';
import { z } from 'zod';

export const ADMIN_DEVICE_FIELDS = ['serialNumber', 'IMEI', 'MAC'] as const;
export const CUSTOMER_DEVICE_FIELDS = ['nickname', 'VIN'] as const;

export const calculateDeviceFieldsSchema = (device: SystemDevice, fields: readonly string[]) => {
  const MASK = Object.fromEntries(fields.map(key => [key, true] as const));
  const part = systemDeviceFieldsSchema.optionsMap.get(device.handle) || z.object({});
  const schema = systemDeviceMetadataSchema.merge(part).pick(MASK);
  return schema;
};

export const parseDeviceFields = (device: SystemDevice, fields: readonly string[], partial = false) => {
  const schema = calculateDeviceFieldsSchema(device, fields);
  if (partial) return schema?.partial()?.safeParse(device);
  else return schema?.required()?.safeParse(device);
};

export const parseDeviceAdminFields = (device: SystemDevice, partial = false) => {
  return parseDeviceFields(device, ADMIN_DEVICE_FIELDS, partial);
};

export const parseDeviceCustomerFields = (device: SystemDevice, partial = false) => {
  return parseDeviceFields(device, CUSTOMER_DEVICE_FIELDS, partial);
};

export const systemDeviceMetadataSchema = z.object({
  //Inactive -> Device is deactivated and cannot be used
  //Activating -> Device is being activated
  //Active -> Device is setup and ready to use
  //Deactivating -> Device is being deactivated next billing period
  ownerId: z.string(),
  key: z.string().max(30, 'Key must be shorter than 30 characters'),
  state: z.enum(['inactive', 'activating', 'active', 'deactivating']),
  activationErrors: z
    .object({
      formErrors: z.string().array().optional(),
      fieldErrors: z.record(z.string(), z.string().array().optional()).optional(),
    })
    .optional(),
  systemKey: z.string().optional(),
  adcCustomerId: z.number({ coerce: true }).int().optional(),
  orderId: gidSchema('Order').optional(),
  productHandle: z.string().optional(),
  deviceId: z.number({ coerce: true }).int().optional(),
  nickname: z.string().optional(),
  activationConsentAt: z.number().optional(),
  deactivationConsentAt: z.number().optional(),
  deactivationScheduledFor: z.number().optional(),
  activatedAt: z.number().optional(),
  deactivatedAt: z.number().optional(),
  //Temporary variable for storing the legacy price of an SMM flex customer
  legacyPrice: z.number().optional(),
});

export type SystemDeviceMetadata = z.infer<typeof systemDeviceMetadataSchema>;

const serialNumber = z
  .string({ coerce: true })
  .regex(/^\d{10,12}$/gs, 'Serial number must be between 10-12 digits')
  .optional();
const IMEI = z
  .string({ coerce: true })
  .regex(/^\d{15}$/gs, 'IMEI must be 15 digits')
  .optional();
const MAC = z
  .string({ coerce: true })
  .optional()
  .transform((data, ctx) => {
    if (!data) return data;

    const sanitized = data.toUpperCase().replaceAll(/[^A-Z0-9]/gm, '');

    if (/^[0-9A-F]{12}$/gs.test(sanitized) == false) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid MAC format',
      });

      return z.NEVER;
    }

    return sanitized;
  });
export const VIN = z
  .string({ coerce: true })
  .regex(/^[A-HJ-NPR-Z\d]{8}[\dX][A-HJ-NPR-Z\d]{8}$/gs, 'Invalid VIN format')
  .optional();

export const systemDeviceFieldsSchema = z.discriminatedUnion('handle', [
  z.object({ handle: z.literal('flex-aware'), IMEI }),
  z.object({ handle: z.literal('car-tracker'), serialNumber, IMEI, VIN }),
  z.object({ handle: z.literal('fleet-tracker'), serialNumber, IMEI, VIN }),
  z.object({ handle: z.literal('hub'), serialNumber }),
  z.object({ handle: z.literal('camera'), MAC }),
]);
export type SystemDeviceFields = z.infer<typeof systemDeviceFieldsSchema>;

//SystemDevice is stored on the system owner (Shopify Customer or Company) under devices -> {device.key}
export const systemDeviceSchema = systemDeviceMetadataSchema.and(systemDeviceFieldsSchema);
export type SystemDevice = SystemDeviceMetadata & SystemDeviceFields;

export type SystemDeviceHandle = SystemDeviceFields['handle'];

export const upsertDevicesSchema = z.record(z.string(), z.array(systemDeviceFieldsSchema));
export type OrderDeviceFields = { [handle: string]: SystemDeviceFields[] };
export type OrderDevices = { [handle: string]: SystemDevice[] };

export type DeviceTask = { adcCustomerId: number; device: SystemDevice } & (
  | {
      type: 'delete';
      deleteAt: number;
    }
  | {
      type: 'activate';
      activateAt: number;
    }
);
