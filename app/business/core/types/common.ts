import { PROVINCE_CODES } from '@/business/handlers/constants/province-codes';
import { sanitizePhone } from '@/lib/utils';
import { z } from 'zod';

export const zipSchema = z
  .string({ coerce: true })
  .trim()
  .regex(/^[\d]{5}$/, 'Zipcode must be 5 digits.');

export const nameSchema = z
  .string()
  .trim()
  .min(1, 'Name is required.')
  .max(50, 'Name must be less than 50 characters.');
export const companyNameSchema = z
  .string()
  .trim()
  .min(3, 'Company name must be at least 3 characters.')
  .max(50, 'Company name must be less than 50 characters.');

export const address1Schema = z
  .string()
  .trim()
  .min(1, 'Address is required.')
  .max(100, 'Address must be less than 100 characters.');
export const address2Schema = z.string().trim().max(100, 'Street must be less than 100 characters.').optional();

export const citySchema = z
  .string()
  .trim()
  .min(1, 'City is required.')
  .max(35, 'City must be less than 35 characters.');

export const stateSchema = z
  .string()
  .trim()
  .refine(value => PROVINCE_CODES.find(p => p.value === value), {
    message: 'State is invalid.',
  });

export const phoneSchema = z
  .string()
  .transform((phoneNumber, ctx) => {
    if (phoneNumber) {
      const sanitized = sanitizePhone(phoneNumber);

      if (!sanitized) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid phone format.',
        });

        return z.NEVER;
      }

      return sanitized;
    }

    return phoneNumber;
  })
  .optional();
