import { ADCAddon } from './addons';

export interface ADCAuthToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  'as:rep_id': string;
  'as:client_id': string;
  userName: string;
  'as:dealer_id': string;
  '.issued': string;
  '.expires': string;
}

export interface ADCResourceOp {
  value: any;
  path: string;
  op: 'add' | 'replace';
  from?: string;
}

export interface ADCServiceAddon {
  id: number;
  quantityLimit: number;
  deviceType?: number;
}

export interface ADCAddress {
  street1: string;
  street2?: string;
  city: string;
  state?: string;
  zip: string;
  countryId: number;
}

export interface ADCCustomerCreateInput {
  branchId?: number;
  accountAddress: ADCAddress;
  firstName?: string; //Optional if companyName is defined
  lastName?: string; //Optional if companyName is defined
  companyName?: string;
  title?: string;
  email: string;
  phoneNumber: string;
  dealerCustomerId?: string;
  leadId?: number;
  loginName?: string;
  desiredPassword?: string;
  culture: number;
  installWizard?: boolean;
  systemGroupId?: number;
  installationAddress: ADCAddress;
  installationTimeZone: number;
  propertyType: number;
  panelType: number;
  panelVersion: number;
  modemSerialNumber?: string;
  unitDescription?: string;
  panelSettingTemplateId?: number;
  installerCode?: string;
  communicationPathway?: number;
  eventGroupsToForward?: number[];
  phoneLinePresent?: boolean;
  centralStationForwardingOption: number;
  centralStationAccountNumber?: string;
  centralStationReceiverNumber?: string;
  packageId: number;
  addOnFeatures?: number[];
  packageTemplateId?: number;
  ignoreLowCoverageErrors?: boolean;
  customerNotifications?: number[];
  loginNameAtAuthenticationProvider?: string;
  salesRepLoginName?: string;
  installerRepLoginName?: string;
  contractLengthInMonths?: number;
  contractEndDate?: string;
  enrolledInMaintenance?: boolean;
  hasInsuranceFeatures?: boolean;
}

export interface ADCCustomer {
  customerId: number;
  dealerCustomerId?: string;
  firstName?: string;
  lastName?: string;
  dealerId?: number;
  subDealerId?: number;
  email?: string;
  phoneNumber?: string;
  loginName?: string;
  installAddress?: ADCAddress;
  panelVersion?: number;
  culture?: number;
  companyName?: string;
  propertyType?: number;
  unitDescription?: string;
  packageTemplateId?: number;
  readyDateUtc?: string;
  isTerminated?: boolean;
  joinDate?: string;
  termDate?: string;
  detailedPanelVersion?: string;
  modemInfo?: ADCModemInfo;
  centralStationInfo: any;
  servicePlanInfo?: ADCServicePlanInfo;
  zWaveTask?: number;
  panelResponseStatus?: number;
  installType?: number;
  isInteractiveLockoutEnabled?: boolean;
  dualPathMode?: number;
  bestPractices?: any;
  systemGroupId?: number;
  isEnrolledInMaintenance?: boolean;
  dealerCustomerSource?: any;
  contractEndDateUtc?: string;
  contractLengthMonths?: number;
  pendingTerminationDateUtc?: string;
  customerType?: number;
}

export interface ADCModemInfo {
  modemSerial?: string;
  firmwareVersion?: string;
  network?: number;
  twoWayVoiceCapable?: boolean;
  radioNetworkType?: number;
  modemPhoneNumber?: string;
  imei?: string;
}

export interface ADCServicePlanInfo {
  packageId?: number;
  planType?: number;
  addons?: number[];
  totalServicePrice?: number;
  packageDescription?: string;
}

export interface ADCEquipment {
  deviceId: number;
  deviceName: string;
  deviceType: number;
  groupId: number;
  installDate: string;
  maintainDate: string;
  statusDate: string;
  status: number[];
  imei?: string;
  serialNumber?: string;
}

// export interface ADCEquipment {
//   deviceId: number;
//   webSiteDeviceName: string;
//   group: string;
//   installDate: string;
//   maintainDate: string;
//   statusDate: string;
//   partition: number;
//   monitoredForNormalActivity: boolean;
//   status: number[];
//   deviceType: number;
//   nonReportingFlag: boolean;
//   manufacturerSpecificInfo: any;
//   isSecondaryLoop: boolean;
//   isExistingEquipment: boolean;
//   zwaveManufacturer: string;
//   mac?: string;
//   videoDeviceModel?: string;
// }

export enum ADCLoginRole {
  Owner = 1,
  Admin = 212,
  Viewer = 200,
}

export interface ADCLogin {
  loginId: number;
  loginEmail: string;
  loginName: string;
  loginNameAtAuthenticationProvider: string;
  roles: ADCLoginRole[];
  profileFirstName: string;
  profileLastName: string;
}

export interface ADCCustomerCreated {
  customerId: number;
  loginName: string;
  password: string;
}

export interface ADCFeature {
  feature: ADCAddon;
  maxQuantity: number;
}

export interface ADCDealerTemplate {
  id: number;
  name: string;
  description?: string;
  audience?: string;
  showTemplateInAccountCreation?: boolean;
  basePackage: ADCServicePackage;
  addOnFeatures: ADCFeature[];
  monthlyServiceFee: number;
  branchId: number;
}

export interface ADCServicePackage {
  id: number;
  description: string;
  groupDescription: string;
  includedFeatures: ADCFeature[];
  freeAddOns: ADCFeature[];
  otherAddOns: ADCFeature[];
}

export interface ADCCarConnectorInput {
  imei: string;
  serialNumber: string;
  vin: string;
  name: string;
}

export interface ADCCarConnectorOutput {
  deviceId: number;
  customerId: number;
  imei: string;
  serialNumber: string;
  vin: string;
  name: string;
  message?: string;
}
