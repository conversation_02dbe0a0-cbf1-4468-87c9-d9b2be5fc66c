export interface NHTSADecodeVINResponseResult {
  Value: string;
  ValueId: string;
  Variable: string;
  VariableId: number;
}
export interface NHTSADecodeVINResponse {
  Count: number;
  Message: string;
  SearchCriteria: string;
  Results: {
    Value: string;
    ValueId: string;
    Variable: string;
    VariableId: number;
  }[];
}

export interface VINMakeModelYear {
  make: string;
  model: string;
  year: string;
}

export interface LicensePlate {
  plate: string;
  stateCode: string;
}

export interface VehicleIdentifierResponse {
  VINs?: string[];
  plates?: LicensePlate[];
  error_coaching?: string;
}

export interface VINIdentificationState {
  licensePlate?: LicensePlate | undefined | null;
  VIN?: string | undefined | null;
  MMY?: VINMakeModelYear | undefined | null;
}

export interface IdentifyVINsFromImageResponse {
  results: VINIdentificationState[];
  error?: string;
}
