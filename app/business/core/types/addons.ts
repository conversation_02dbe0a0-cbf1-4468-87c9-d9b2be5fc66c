///FOUND at https://bitbucket.corp.adcinternal.com/projects/FALCOR/repos/software/browse/Features/Alarm.ServicePlan/Alarm.ServicePlan.Abstractions/Enums/AddOnFeatureEnum.cs
///AND https://bitbucket.corp.adcinternal.com/projects/FALCOR/repos/software/browse/DotNetShared/DotNetStandard/Alarm.Common.Enums/ServicePlan/ServicePlanFeatureEnum.cs

/// <summary>
/// The add-on features.
/// </summary>
// Note: keep more-or-less in synch with AlarmBusinessObjects.ServicePlanFeatureEnum
// Inserting items in the middle or removing items messes up dealers; only add to the end.
// If you feel like you want to delete, rename it to Obsolete or something.
// The problem is that the constant values we define here don't make it into the wsdl.
// Do not use the Obsolete attribute because that will cause XML serialization to fail.
export enum ADCAddon {
  VoiceNotificationsForAlarms = 1,
  VoiceNotificationsForMonitoring = 2,
  LightAutomation = 3,
  UserCodeControl = 4,
  RemoteArming = 5,
  ThermostatControl = 6,
  ArmingSupervision = 7,
  NormalActivityReports = 8,
  ArmingReports = 9,
  ArmingSchedules = 10,
  Inactivity = 11,
  FiveNormalActivitySensors = 12,
  //skipped DiyCentralStation because it is not a real feature for dealers.
  ProVideo = 14,
  /// <summary>
  /// Note: this is obsolete.  Please use ProVideo plus 1 TwoFiftyMBExtraVideoStorage instead.
  /// </summary>
  ProVideoPlus = 15,
  /// <summary>
  /// Note: the definition of the additional storage has changed.
  /// The addon is now called "Video Expansion Pack" in the UI, and allows an additional 4 cameras and 5000 clips.
  /// </summary>
  TwoFiftyMBExtraVideoStorage = 16,
  TwoWayVoice = 17,
  //skipped TwoWayVoiceOverageRate because it is not a real feature for dealers.
  //skipped PrimaryConnectionSurcharge because it is not a real feature for dealers.
  WeatherToPanel = 20,
  DigitalInputVideos = 21,
  MedicationAlerts = 22,
  ZWaveLights = 23,
  ZWaveThermostats = 24,
  ZWaveLocks = 25,
  EnterpriseNotices = 26,
  ZWaveEnergy = 27,
  Reminders = 28,
  SevereWeatherAlerts = 29,
  ImageSensorAlarms = 30,
  ImageSensorPlus = 31,
  ImageSensorExtraUploads = 32,
  //TODO - uncomment these after launch and add to converter in CustomerManagement, but put them at the end. Inserting items in the middle or removing items messes up dealers.
  //PointCodes = 33,
  //PointCodesPlus = 34,
  EnterpriseSecurityConsole = 35,
  SmartEnergyPlus = 36,
  Securus = 38,
  LutronRemoteAccess = 39,
  Obsolete_LutronLightsAndThermostats = 40, //nexdst time we add an item, feel free to put it in this position, but make sure the numbers match ServicePlanFeatureEnum
  IDProtection = 41,
  GreenButton = 42,
  GarageDoors = 43,
  Wellness = 44,
  AdvancedEnergy = 45,
  AdvancedAutomation = 46,
  LutronIntegration = 47,
  LiftMasterIntegration = 48,
  /// <summary>
  /// Obsolete as of EM-24114.
  /// </summary>
  SchneiderIntegration = 49,
  Video24x7PerSVR = 50,
  TaggIntegration = 51,
  WaterManagement = 52,
  CommercialActivityReports = 53,
  BeCloseCommunityView = 54,
  ULCommericial = 55,
  SolarMonitoring = 56,
  SolarEdgeIntegration = 57,
  EnphaseIntegration = 58,
  NESTIntegration = 59,
  SMSAlarms = 60,
  SMSNotificationsMexico = 61,
  EnterpriseEnergy = 62,
  EnterpriseWellness = 63,
  IrrigationControl = 64,
  RachioIntegration = 65,
  ConnectedCar = 66,
  PropaneMonitoring = 67,
  SMSNotificationsChile = 68,
  SMSNotificationsColombia = 69,
  SMSNotificationsNZ = 70,
  SMSNotificationsAustralia = 71,
  SMSNotificationsBrazil = 72,
  SMSNotificationsPanama = 73,
  /// <summary>
  /// Obsolete as of EM-24985.
  /// </summary>
  CosaIntegration = 74,
  OSnappIntegration = 75,
  /// <summary>
  /// Note: this is obsolete.  Please use ConnectedCar instead.
  /// </summary>
  ConnectedCarPlus = 76,
  DoorbellCameras = 77,
  UnexpectedActivityAlerts = 78,
  AccessControl = 79,
  ZWaveCO = 80,
  HourlySupervision = 81,
  SixHourSupervision = 82,
  BasicDoorbell = 83,
  AlarmVisualVerification = 84,
  PanicButton = 85,
  AudioIntegration = 87,
  /// <summary>
  /// Note: This is obsolete as of EM-30101. The Kona integration never launched.
  /// </summary>
  KonaLabsWaterMetering = 88,
  VideoDeviceAudio = 89,
  CancelVerify = 90,
  SMSNotificationsBelgium = 91,
  AccessControlDoors = 92,
  RouterLimits = 94,
  HomeControllerIntegration = 97,
  FlexIO = 98,
  SMSNotificationsSweden = 99,
  SMSNotificationsNetherlands = 100,
  ProVideoWithAnalytics = 101,
  /// <summary>
  /// Note: this is obsolete.  Please use ConnectedCar instead.
  /// </summary>
  ConnectedCarCalAmp = 102,
  SMSNotificationsNorway = 103,
  SMSNotificationsIreland = 104,
  CarrierCorIntegration = 105,
  BuilderDoorbell = 106,
  AccessPlanUserManagement = 107,
  WaterManagementPlus = 108,
  SmarterBusinessTemperatureMonitoring = 109,
  CommercialVideo8 = 110,
  CommercialVideo16 = 111,
  CommercialVideo8Expansion = 112,
  CommercialVideo16Expansion = 113,
  /// <summary>
  /// Note: This is obsolete.
  /// A combination of of ZWaveThermostats + Dealer Wizard preference
  /// is now the driver for gating adding Lennox equipment to a customer account.
  /// </summary>
  LennoxIComfortIntegration = 114,
  AlarmLink = 115,
  ImageSensorLimited = 116,
  SolarIntegration = 119,
  ZWaveShades = 120,
  UnattendedShowing = 121,
  AccessControl16 = 122,
  AccessControl32 = 123,
  AccessControl64 = 124,
  SmartViewForOnboardRecording = 125,
  OpenEyeCloudConnect = 126,
  BusinessActivityAnalytics = 127,
  PremiumVideo = 128,
  ExtraSmarterBusinessTemperatureMonitoringSensors = 129,
  SmartViewForOnboardRecordingCV = 130,
  VideoAnalyticsRuleCreated = 131,
  ProVideoWithAnalytics1000 = 132,
  AzureActiveDirectoryIntegration = 133,
  VideoAnalyticsRuleCreated2000 = 134,
  Ambient = 135,
  UnattendedShowingSolution = 136,
  SingleDoorbellWithAnalytics = 137,
  CommercialVideo4 = 138,
  CommercialVideo4Expansion = 139,
  VideoBasic = 140,
  VideoBasicWithAnalytics = 141,
  VideoAwareness = 142,
  VideoComplete = 143,
  VideoOneCameraAddOn = 144,
  BaseCallTime = 145,
  ImageUploadCount = 146,
  IOLights = 147,
  IOLocks = 148,
  IOGarageDoorsAndGates = 149,
  NestVideoIntegration = 150,
  EssenceMPERS = 151,
  Builder770BasicDoorbell = 152,
  Builder770PremiumDoorbell = 153,
  ThirtyMinuteSupervision = 154,
  NestAwareResale = 155,
  SmarterAccessControlPlus = 156,
  AccessControlPlus16 = 157,
  AccessControlPlus32 = 158,
  AccessControlPlus64 = 159,
  AccessControlPlusDoors = 160,
  MobileCredentials = 161,
  LiftMasterSurcharge = 162,
  FreeMobileCredentials = 163,
  // The name of this feature has changed to In-App Property Panic
  // You will still see VideoPropertyPanic in the code to preserve backwards compatability
  VideoPropertyPanic = 164,
  SmartArming = 165,
  CommercialVideo2Basic = 166,
  CommercialVideo2BasicExpansion = 167,
  WaterCloudDevices = 168,
  ElevatedEventsMonitoring = 169,
  ZWaveNoiseSensors = 170,
  ThirdPartyCameras = 171,
  CellRouter = 173,
  ConnectedFleet = 174,
  AmbientVoice = 175,
  WifiAwareness = 176,
  MobileCredentials2500 = 177,
  MobileCredentials5000 = 178,
  MobileCredentials7500 = 179,
  LocalMalfunctionSignalingAndSecondaryReaderSupport = 180,
  PropertyActions = 181,
  GunshotDetectionSDS = 183,
  GunshotDetectionPlusSDS = 184,
  MultiLevelEnterpriseSecurityConsole = 185,
  CameraTwoWayAudioforMonitoringResponse = 186,
  Noonlight = 187,
  ProactiveVideoEscalatedEventMonitoring = 188,
  AccessControlEscalatedEventMonitoring = 189,
  // skip PhoneReceiverSurcharge = 190 since it is not a feature that can be added through webservices

  ContinuousAudioRecordingSvr = 192,
  ContinuousAudioRecordingOnboard = 193,
  HazeGuard = 194,
  HazeGuardAdditionalUnits = 195,
  SixMinuteSupervision = 196,
  AIDeterrence = 198,
  RetentionEngine = 199,
  SunFlowerLabsDrone = 200,
  VideoIntercomDirectoryUser = 201,
  VideoIntercomManagement = 202,
  FallDetection = 203,
  ExternalVideoMonitoringSoftwareSupport = 204,

  //Note: if you add a value here, you need to increment the value in WebServices\CustomerManagement.asmx GetLatestCallerVersion
  //and then return that new value in MinCallerVersionForWebServices for your new enum value
}
