import { MoneyV2 } from '@shopify/hydrogen/storefront-api-types';

import { AdvertisableDiscount } from '@/routes/($theme).$handle.product/loader';
import { CartApiQueryFragment } from 'storefront.generated';
import { CartSystemView } from '@/business/handlers/types/cart';

export type CartLineError = {
  message: string;
  description: string;
  errorCode: string;
  link: string;
};

export type AugmentedCart = Omit<CartApiQueryFragment, 'cost' | 'lines'> & {
  cost: CartApiQueryFragment['cost'] & {
    subTotal: MoneyV2;
    totalBeforeSavings: MoneyV2;
    totalSavings: MoneyV2;
  };
  lines: {
    nodes: (CartApiQueryFragment['lines']['nodes'][number] & {
      subscription: MoneyV2;
      error?: CartLineError;
      discounts: AdvertisableDiscount[];
    })[];
  };
};

export interface CartPayload {
  cart: AugmentedCart;
  systems: CartSystemView[];
  fallbackLocationId: string | null;
  orderingCustomerId: string | null;
}
