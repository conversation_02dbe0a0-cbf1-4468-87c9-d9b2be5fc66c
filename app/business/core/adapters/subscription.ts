import { GetSystemOwnerContactInfoQuery } from 'admin.generated';
import <PERSON><PERSON> from 'stripe';
import { OWNER_ID_METADATA_KEY } from '../constants/subscription';

export class SubscriptionAdapter {
  constructor() {}

  convertOwnerContactInfoToStripeCustomerInput(
    ownerId: string,
    node: NonNullable<GetSystemOwnerContactInfoQuery['node']>,
  ): Stripe.CustomerCreateParams {
    let params: Stripe.CustomerCreateParams;

    if (node.__typename == 'Customer') {
      const defaultAddressId = node.defaultAddress?.id;
      const defaultAddress = node.addresses.find(address => address.id == defaultAddressId);

      params = {
        name: [node.firstName, node.lastName].join(' '),
        email: node.email || undefined,
        phone: node.phone || undefined,
        address: defaultAddress?.address1
          ? {
              line1: defaultAddress.address1 || undefined,
              line2: defaultAddress.address2 || undefined,
              city: defaultAddress.city || undefined,
              country: defaultAddress.country || undefined,
              state: defaultAddress.province || undefined,
              postal_code: defaultAddress.zip || undefined,
            }
          : undefined,
        metadata: {
          [OWNER_ID_METADATA_KEY]: ownerId,
        },
      };
    } else if (node.__typename == 'Company') {
      params = {
        name: node.name,
        email: node.mainContact?.customer?.email || undefined,
        phone: node.mainContact?.customer?.phone || undefined,
        metadata: {
          [OWNER_ID_METADATA_KEY]: ownerId,
        },
      };
    } else {
      throw new Error(`Shopify node ${ownerId} is not a Customer or Company.`);
    }

    return params;
  }
}
