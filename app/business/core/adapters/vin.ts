import { singleton } from 'tsyringe';

@singleton()
export class VINAdapter {
  arrayBufferToBase64(buffer: A<PERSON>yBuffer) {
    // Convert ArrayBuffer to Uint8Array
    const bytes = new Uint8Array(buffer);

    // Convert bytes to string
    let binaryString = '';
    for (let i = 0; i < bytes.length; i++) {
      binaryString += String.fromCharCode(bytes[i]);
    }

    // Convert binary string to base64
    return btoa(binaryString);
  }
}
