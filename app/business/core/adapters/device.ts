import { injectable } from 'tsyringe';
import { SystemDevice } from '../types/device';
import { addQuantityToKey } from '@/lib/utils';
import { FeatureQuantities } from '../types';

@injectable()
export class DeviceAdapter {
  constructor() {}

  sumActiveOrDeactivatingDevices(devices: SystemDevice[]) {
    return devices.reduce<FeatureQuantities>(
      (prev, device) =>
        device.state == 'active' || device.state == 'deactivating' ? addQuantityToKey(prev, device.handle, 1) : prev,
      {},
    );
  }

  sumActiveDevices(devices: SystemDevice[]) {
    return devices.reduce<FeatureQuantities>(
      (prev, device) => (device.state == 'active' ? addQuantityToKey(prev, device.handle, 1) : prev),
      {},
    );
  }
}
