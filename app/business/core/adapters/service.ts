import {
  addQuantities,
  addQuantityToKey,
  ceilDivideQuantities,
  convertAddonQuantitiesToFeatureQuantities,
  minQuantities,
  multiplyQuantities,
} from '@/lib/utils';
import Stripe from 'stripe';
import { singleton } from 'tsyringe';
import { ADDON_SYSTEM_FEATURES, SYSTEM_FEATURES } from '../constants/features';
import { AddonQuantities, FeatureQuantities, ServiceProduct, SubscriptionLineItem } from '../types';
import { ADCDealerTemplate } from '../types/adc';
import { ADCAddon } from '../types/addons';

export const TEMPLATE_ID_METADATA_KEY = 'template-id';

@singleton()
export class ServiceAdapter {
  constructor() {}

  serviceProductsByPriceId(serviceProducts: ServiceProduct[]) {
    return Object.fromEntries(serviceProducts.map(product => [product.priceId, product]));
  }

  serviceProductsByGroup(serviceProducts: ServiceProduct[]) {
    const serviceGroups: { [group: string]: ServiceProduct[] } = {};

    for (const service of serviceProducts) {
      if (serviceGroups[service.group]) serviceGroups[service.group].push(service);
      else serviceGroups[service.group] = [service];
    }

    return serviceGroups;
  }

  calculateMinimumQuantityToSupportFeatures(features: FeatureQuantities, service: ServiceProduct) {
    return ceilDivideQuantities(features, convertAddonQuantitiesToFeatureQuantities(service.addons));
  }

  lineItemsToFeatureQuantities(lines: SubscriptionLineItem[], products: ServiceProduct[]) {
    return convertAddonQuantitiesToFeatureQuantities(this.subscriptionLinesToAddonQuantities(lines, products));
  }

  subscriptionLinesToAddonQuantities(lines: SubscriptionLineItem[], products: ServiceProduct[]) {
    let addons: AddonQuantities = {};

    for (const line of lines) {
      const product = products.find(product => product.priceId == line.price);

      if (!product) {
        console.log('Subscription line product is missing from products list.');
        continue;
      }

      addons = addQuantities(addons, multiplyQuantities(product.addons, line.quantity || 0));
    }

    return addons;
  }

  stripeFeaturesToAddonQuantities(stripeFeatures: Stripe.ProductFeature[], adcTemplates: ADCDealerTemplate[]) {
    let addons: AddonQuantities = {};
    for (const feature of stripeFeatures) {
      const template = adcTemplates.find(
        template => template.id.toString() == feature?.entitlement_feature?.metadata?.[TEMPLATE_ID_METADATA_KEY],
      );

      if (!template) continue;

      addons = template.addOnFeatures.reduce(
        (prev, addon) => addQuantityToKey(prev, addon.feature, addon.maxQuantity),
        addons,
      );
    }

    return addons;
  }

  applyFeatureLimits(features: FeatureQuantities) {
    const SYSTEM_FEATURE_LIMITS: FeatureQuantities = Object.fromEntries(
      Object.entries(SYSTEM_FEATURES).map(([handle, { limit }]) => [handle, limit]),
    );

    return minQuantities(features, SYSTEM_FEATURE_LIMITS);
  }
}
