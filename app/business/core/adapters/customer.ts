import { CountryCode, MailingAddressInput } from 'admin.types';
import { injectable, singleton } from 'tsyringe';

@singleton()
export class CustomerAdapter {
  sanitizeAddressInput(address: MailingAddressInput) {
    return {
      address1: address.address1,
      address2: address.address2,
      city: address.city,
      company: address.company,
      countryCode: CountryCode.US,
      firstName: address.firstName,
      lastName: address.lastName,
      phone: address.phone,
      provinceCode: address.provinceCode,
      zip: address.zip,
    };
  }
}
