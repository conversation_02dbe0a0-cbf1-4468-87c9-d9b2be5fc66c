import { convertADCFeaturesToAddonQuantities, maxQuantities, minQuantities, subtractQuantities } from '@/lib/utils';
import { singleton } from 'tsyringe';
import { BUSINESS_SERVICE_PACKAGE, RESIDENTIAL_SERVICE_PACKAGE } from '../constants/service-packages';
import { AddonQuantities, CreateSystemADCCustomerInput } from '../types';
import { ADCCustomerCreateInput, ADCServicePackage } from '../types/adc';

@singleton()
export class SystemAdapter {
  constructor() {}

  createSystemADCCustomerInputToADCCustomerCreateInput({
    ownerId,
    loginName,
    email,
    phoneNumber,
    address,
    name,
    ...conditional
  }: CreateSystemADCCustomerInput) {
    const adcInput: ADCCustomerCreateInput = {
      dealerCustomerId: ownerId,
      accountAddress: { ...address, countryId: 1 },
      installationAddress: { ...address, countryId: 1 },
      culture: 1, //US,
      unitDescription: name,
      loginName,
      email,
      phoneNumber,
      centralStationForwardingOption: 0, //UNSET
      installationTimeZone: 0, //UNSET
      panelType: 99, //NO PANEL
      panelVersion: -1, //NO VERSION

      //Residential settings
      propertyType: 1, //HOME
      packageId: RESIDENTIAL_SERVICE_PACKAGE.id,
    };

    if (conditional.type == 'business') {
      const { company } = conditional;

      adcInput.packageId = BUSINESS_SERVICE_PACKAGE.id;
      adcInput.companyName = company;
      adcInput.propertyType = 4; //Business
    } else if (conditional.type == 'personal') {
      const { firstName, lastName } = conditional;

      adcInput.packageId = RESIDENTIAL_SERVICE_PACKAGE.id;
      adcInput.firstName = firstName;
      adcInput.lastName = lastName;
      adcInput.propertyType = 1; //Single family home
    }

    return adcInput;
  }

  applyServicePackageAddonsConstraints(addons: AddonQuantities, packageId: number | undefined) {
    let servicePackage: ADCServicePackage;

    if (packageId == RESIDENTIAL_SERVICE_PACKAGE.id) servicePackage = RESIDENTIAL_SERVICE_PACKAGE;
    else if (packageId == BUSINESS_SERVICE_PACKAGE.id) servicePackage = BUSINESS_SERVICE_PACKAGE;
    else throw new Error('Unrecognized service package on customer.');

    const includedAddons = convertADCFeaturesToAddonQuantities(servicePackage.includedFeatures);
    const addonsWithoutIncluded = subtractQuantities(addons, includedAddons);

    const minAddons = {};
    const maxAddons = convertADCFeaturesToAddonQuantities(servicePackage.otherAddOns);
    const addonsAfterClamp = minQuantities(maxAddons, maxQuantities(minAddons, addonsWithoutIncluded));

    return addonsAfterClamp;
  }
}
