// Enable tSyringe
import '@abraham/reflection';
import * as shopifyHydrogen from '@shopify/hydrogen';
import { action } from '../../home.cart';
import type { Session, SessionStorage } from 'react-router';
import { SessionClient } from '@/business/clients/session-client';
import { AppLoadContext } from '@shopify/remix-oxygen';

// jest.mock('../../../../server', () => {
//   return {
//     AppSession: jest.fn().mockImplementation(() => {
//       return {
//         has: jest.fn(),
//         get: jest.fn(),
//         flash: jest.fn(),
//         unset: jest.fn(),
//         set: jest.fn(),
//         destroy: jest.fn(),
//         commit: jest.fn(),
//       };
//     }),
//   };
// });

// const mockEnv: Env = {
//   SESSION_SECRET: 'mock_key',
//   PUBLIC_STOREFRONT_API_TOKEN: 'mock_key',
//   PRIVATE_STOREFRONT_API_TOKEN: 'mock_key',
//   PUBLIC_STORE_DOMAIN: 'mock_key',
//   PUBLIC_STOREFRONT_ID: 'mock_key',
//   PUBLIC_CUSTOMER_ACCOUNT_API_CLIENT_ID: 'mock_key',
//   PUBLIC_CUSTOMER_ACCOUNT_API_URL: 'mock_key',
//   PUBLIC_BUILDER_KEY: 'mock_key',
//   PUBLIC_CHECKOUT_DOMAIN: 'mock_key',
//   SHOP_ID: 'mock_key',
//   STRIPE_PUBLISHABLE_KEY: 'mock_key',
//   STRIPE_SECRET_KEY: 'mock_key',
//   ADC_AUTH_2FA_ID: 'mock_key',
//   ADC_AUTH_CLIENT_ID: 'mock_key',
//   ADC_AUTH_PASSWORD: 'mock_key',
//   ADC_AUTH_USERNAME: 'mock_key',
//   ADC_DEALER_ID: 'mock_key',
//   ADC_SSO_PASSWORD: 'mock_key',
//   ADC_SSO_USERNAME: 'mock_key',
//   HOST: 'mock_key',
//   MULTIPASS_SECRET: 'mock_key',
//   SCOPES: 'mock_key',
//   SENDGRID_MARKETING_API_KEY: 'mock_key',
//   SENDGRID_OTP_TEMPLATE_ID: 'mock_key',
//   SENDGRID_TRANSACTIONAL_API_KEY: 'mock_key',
//   SHOPIFY_API_KEY: 'mock_key',
//   SHOPIFY_API_SECRET: 'mock_key',
//   SHOPIFY_OFFLINE_ADMIN_ACCESS_TOKEN: 'mock_key',
//   TWILIO_ACCOUNT_SID: 'mock_key',
//   TWILIO_AUTH_TOKEN: 'mock_key',
//   TWILIO_SENDER: 'mock_key',
// };

// const mockCart = {
//   get: jest.fn(),
//   getCartId: jest.fn(),
//   setCartId: jest.fn(),
//   create: jest.fn(),
//   addLines: jest.fn(),
//   updateLines: jest.fn(),
//   removeLines: jest.fn(),
//   updateDiscountCodes: jest.fn(),
//   updateBuyerIdentity: jest.fn(),
//   updateNote: jest.fn(),
//   updateSelectedDeliveryOption: jest.fn(),
//   updateAttributes: jest.fn(),
//   setMetafields: jest.fn(),
//   deleteMetafield: jest.fn(),
//   updateGiftCardCodes: jest.fn(),
// };

// const mockStorefront: shopifyHydrogen.Storefront = {
//   query: jest.fn(),
//   mutate: jest.fn(),
//   cache: undefined,
//   CacheNone: jest.fn(),
//   CacheLong: jest.fn(),
//   CacheShort: jest.fn(),
//   CacheCustom: jest.fn(),
//   generateCacheControlHeader: jest.fn(),
//   getPublicTokenHeaders: jest.fn(),
//   getPrivateTokenHeaders: jest.fn(),
//   getShopifyDomain: jest.fn(),
//   getApiUrl: jest.fn(),
//   i18n: {
//     language: 'EN',
//     country: 'US',
//   },
// };

// class MockExecutionContext implements ExecutionContext {
//   waitUntil(promise: Promise<any>): void {
//     // Mock implementation
//   }

//   passThroughOnException(): never {
//     // Mock implementation
//     throw new Error('Mock implementation');
//   }
// }

// const mockContext: AppLoadContext = {
//   env: mockEnv,
//   cart: mockCart,
//   storefront: mockStorefront,
//   session: new SessionClient({} as SessionStorage, {} as Session),
//   waitUntil: new MockExecutionContext().waitUntil,
//   adc: {} as any,
//   admin: {} as any,
//   shopify: {} as any,
//   stripe: {} as any,
// };

describe('cart - action', () => {
  it('should throw an error if no action is provided', async () => {
    await expect(async () => {
      throw new Error('No action provided');
    }).rejects.toThrow('No action provided');
  });

  // it("should handle LinesAdd action", async () => {
  //   const formData = new FormData();
  //   const formInput = {
  //     action: "LinesUpdate",
  //     inputs: {
  //       lines: [
  //         {
  //           id: "gid://shopify/CartLine/12345?cart=6789",
  //           quantity: 2,
  //         },
  //       ],
  //     },
  //   };
  //   formData.append("cartFormInput", JSON.stringify(formInput));
  //   formData.append("decrease-quantity", "2");

  //   jest.spyOn(shopifyHydrogen.CartForm, "getFormInput").mockReturnValue({
  //     action: "LinesUpdate",
  //     inputs: {
  //       lines: [
  //         {
  //           id: "gid://shopify/CartLine/1234?cart=56789",
  //           quantity: 2,
  //         },
  //       ],
  //       "decrease-quantity": "2",
  //     },
  //   });

  //   const performAction = async () => {
  //     return action({
  //       request: new Request("http://app.com/path", {
  //         method: "POST",
  //         body: formData,
  //       }),
  //       params: {},
  //       context: mockContext,
  //     });
  //   };

  //   await expect(performAction()).rejects.toThrow("No action provided");
  // });
});
