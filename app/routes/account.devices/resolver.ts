import { VIN } from '@/business/core/types/device';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

export const activateSchema = z.object({
  nickname: z.string().optional(),
  consent: z.number({ coerce: true }).optional(),
  VIN,
});

export const schema = z
  .object({
    deviceKey: z.string(),
  })
  .and(
    z.discriminatedUnion('type', [
      z.object({
        type: z.literal('quoteActivation'),
      }),
      z.object({
        type: z.literal('quoteDeactivation'),
      }),
      activateSchema.extend({
        type: z.literal('activate'),
      }),
      z.object({
        type: z.literal('deactivate'),
        consent: z.number({ coerce: true }),
      }),
    ]),
  );

export type SchemaType = z.infer<typeof schema>;

export type ActivateDevicePart = Partial<Omit<SchemaType & { type: 'activate' }, 'type' | 'deviceKey'>>;

export const resolver = zodResolver(schema);
