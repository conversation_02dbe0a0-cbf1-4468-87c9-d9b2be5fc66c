import { SystemDeviceHandle } from '@/business/core/types/device';
import { useAccountLoader } from '@/routes/account/hooks';
import useDevices from './use-devices';

export default function useDeviceService(handle: SystemDeviceHandle) {
  const { features } = useAccountLoader();
  const devices = useDevices();

  const activeDeviceCount = devices[handle].reduce((acc, curr) => acc + (curr.state == 'active' ? 1 : 0), 0);
  const serviceFeatureCount = features[handle] || 0;

  return {
    activeDeviceCount,
    serviceFeatureCount,
    wouldRequireUpgrade: activeDeviceCount >= serviceFeatureCount, //In theory the active should never be greater than the service feature count, but this is a sanity check
  };
}
