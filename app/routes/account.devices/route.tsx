import { Suspense } from 'react';
import { useAccountLoader } from '../account/hooks';
import { Await } from 'react-router';
import { Skeleton } from '@/components/ui/skeleton';
import { DevicesCard } from './components/devices-card';

import { action } from './action';

export { action };

export default function Page() {
  const { devices } = useAccountLoader();

  return (
    <>
      <h1 className="text-2xl font-bold">Manage your devices here.</h1>
      <Suspense fallback={<Skeleton className="h-[500px] w-full" />}>
        <Await resolve={devices}>{devices => <DevicesCard devices={devices} />}</Await>
      </Suspense>
    </>
  );
}
