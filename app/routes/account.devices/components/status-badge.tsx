import { Badge, BadgeProps } from '@/components/ui/badge';
import { SystemDevice } from '@/business/core/types/device';
import { TooltipContentProps } from '@radix-ui/react-tooltip';
import { DeviceField } from './compute-fields';

export function StatusBadge({
  device,
  className,
  side,
}: {
  device: SystemDevice;
  className?: string;
  side?: TooltipContentProps['side'];
}) {
  let variant: BadgeProps['variant'] = 'outline';
  let label = 'Pending activation';

  switch (device.state) {
    case 'activating':
      variant = 'success';
      label = 'Pending activation';
      break;
    case 'active':
      variant = 'default';
      label = 'Active';
      break;
    case 'inactive':
      variant = 'secondary';
      label = 'Inactive';
      break;
    case 'deactivating':
      variant = 'destructive';
      label = 'Pending deactivation';
      break;
    default:
      break;
  }

  return (
    <Badge variant={variant} className={className}>
      <DeviceField device={device} field="status" side={side}>
        {label}
      </DeviceField>
    </Badge>
  );
}
