import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { SystemDevice } from '@/business/core/types/device';
import { TooltipContentProps } from '@radix-ui/react-tooltip';
import { <PERSON>ertCircle, CircleAlert } from 'lucide-react';
import { ReactNode } from 'react';
import { VINCell } from './fields/VIN';
import { useAccountLoader } from '@/routes/account/hooks';

export function DeviceField({
  device,
  field,
  children,
  fallback,
  side,
}: {
  device: SystemDevice;
  field: string;
  children?: ReactNode;
  fallback?: ReactNode;
  side?: TooltipContentProps['side'];
}) {
  const { systemRole } = useAccountLoader();
  const value = (device as any)[field];
  children = children || value || '-';

  if (fallback && (device.state == 'active' || device.state == 'deactivating' || systemRole != 'write')) {
    children = typeof fallback == 'boolean' ? value : fallback;
  }

  const activationErrors = device.activationErrors;
  const errors: string[] =
    field == 'status' ? activationErrors?.formErrors : (activationErrors as any)?.fieldErrors?.[field];

  if (!errors?.length || device.state != 'activating') return children;

  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger className="relative" asChild>
          <div>
            {children}
            <CircleAlert className="absolute top-0 right-0 size-6 translate-x-1/2 -translate-y-2/3 rounded-full bg-yellow-500 p-0.5 text-white" />
          </div>
        </TooltipTrigger>
        <TooltipContent side={side}>
          {errors.map((error, index) => (
            <p key={index}>{error}</p>
          ))}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function computeFields(index: number, device: SystemDevice) {
  const fields: { [label: string]: ReactNode } = { '#': index + 1, Name: device.nickname };

  switch (device.handle) {
    case 'flex-aware':
      fields['SN/IMEI'] = <DeviceField device={device} field="IMEI" />;
      break;
    case 'car-tracker':
    case 'fleet-tracker':
      fields['IMEI'] = <DeviceField device={device} field="IMEI" />;
      fields['Serial Number'] = <DeviceField device={device} field="serialNumber" />;
      fields['VIN'] = <DeviceField device={device} field="VIN" />;
      break;
    case 'camera':
      fields['MAC'] = <DeviceField device={device} field="MAC" />;
      break;
    case 'hub':
      fields['Serial Number'] = <DeviceField device={device} field="serialNumber" />;
      break;
    default:
      break;
  }

  return fields;
}
