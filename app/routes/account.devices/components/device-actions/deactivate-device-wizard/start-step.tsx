import { Button } from '@/components/ui/button';
import { DialogClose, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DeactivateStep, useDeactivateForm, useDeactivateSteps } from './context';

const STEP_NAME = 'start';

export const useStartStep = () => STEP_NAME;

export function StartStep() {
  return (
    <DeactivateStep name={STEP_NAME}>
      <StartStepImpl />
    </DeactivateStep>
  );
}

function StartStepImpl() {
  const {
    formData: { device },
  } = useDeactivateForm();
  const { nextStep } = useDeactivateSteps();

  return (
    <>
      <DialogHeader>
        <DialogTitle>Deactivate {device.nickname || device.handle}</DialogTitle>
        <DialogDescription>
          Are you sure you want to deactivate this device? Deactivating will remove its service and any associated data.
          Please review the details in the next step before confirming.
        </DialogDescription>
      </DialogHeader>
      <DialogFooter>
        <DialogClose asChild>
          <Button variant="outline">Cancel</Button>
        </DialogClose>
        <Button variant="destructive" onClick={nextStep}>
          Continue
        </Button>
      </DialogFooter>
    </>
  );
}
