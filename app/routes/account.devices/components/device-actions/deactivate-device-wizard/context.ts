import type { SystemDevice } from '@/business/core/types/device';
import { createWizardFormContext, createWizardStepsContext } from '@/components/Wizard/ctx-factory';
import type Stripe from 'stripe';

const { WizardFormProvider: DeactivateFormProvider, useWizardForm: useDeactivateForm } = createWizardFormContext<{
  device: SystemDevice;
  consent?: number;
  invoice?: Stripe.Invoice;
}>();

const {
  WizardStepsProvider: DeactivateStepsProvider,
  useWizardSteps: useDeactivateSteps,
  WizardStep: DeactivateStep,
} = createWizardStepsContext();

export { DeactivateFormProvider, DeactivateStep, DeactivateStepsProvider, useDeactivateForm, useDeactivateSteps };
