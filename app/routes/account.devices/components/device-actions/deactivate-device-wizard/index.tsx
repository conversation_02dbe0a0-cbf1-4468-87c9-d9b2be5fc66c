import { SystemDevice } from '@/business/core/types/device';
import Wizard from '@/components/Wizard';
import { action } from '@/routes/account.devices/action';
import { SchemaType } from '@/routes/account.devices/resolver';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { useFetcher } from 'react-router';
import { useCallback, useState } from 'react';
import ProgressIndicator from '../progress-indicator';
import { CompleteStep, useCompleteStep } from './complete-step';
import { DowngradeStep, useDowngradeStep } from './downgrade-step';
import { StartStep, useStartStep } from './start-step';
import { DeactivateFormProvider, DeactivateStepsProvider, useDeactivateForm, useDeactivateSteps } from './context';

interface DeactivateDeviceWizardProps {
  device: SystemDevice;
}

export default function DeactivateDeviceWizard({ device }: DeactivateDeviceWizardProps) {
  const deactivationFetcher = useFetcher<typeof action>({ key: 'deactivate' + device.key });

  const submit = useCallback(
    (finalState: { device: SystemDevice; consent?: number }) => {
      deactivationFetcher.submit(
        {
          type: 'deactivate',
          deviceKey: device.key,
          consent: finalState.consent,
        } as SchemaType,
        {
          method: 'POST',
        },
      );
    },
    [deactivationFetcher, device.key],
  );

  return (
    <DeactivateFormProvider initialData={{ device }} onSubmit={submit}>
      <DeactivateWizardSteps />
    </DeactivateFormProvider>
  );
}

function DeactivateWizardSteps() {
  const { submit } = useDeactivateForm();
  const [animateRef] = useAutoAnimate<HTMLDivElement>();
  return (
    <DeactivateStepsProvider steps={[useStartStep(), useDowngradeStep(), useCompleteStep()]} onComplete={submit}>
      <DeactivateProgress />
      <div ref={animateRef} className="w-full space-y-4">
        <StartStep />
        <DowngradeStep />
        <CompleteStep />
      </div>
    </DeactivateStepsProvider>
  );
}

function DeactivateProgress() {
  const { stepIndex, totalSteps } = useDeactivateSteps();

  return <ProgressIndicator stepIndex={stepIndex} totalSteps={totalSteps} />;
}
