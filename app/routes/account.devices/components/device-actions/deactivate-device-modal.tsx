import { SystemDevice } from '@/business/core/types/device';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { useAccountLoader } from '@/routes/account/hooks';
import { useState } from 'react';
import DeactivateDeviceWizard from './deactivate-device-wizard';

export function DeactivateDeviceAction({
  device,
  title,
  disable,
}: {
  device: SystemDevice;
  title: string;
  disable?: boolean;
}) {
  const { systemRole } = useAccountLoader();
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!disable && (
        <DialogTrigger
          onClick={event => {
            event.preventDefault();
            setOpen(true);
          }}
          asChild
          disabled={systemRole != 'write'}
        >
          <DropdownMenuItem>{title}</DropdownMenuItem>
        </DialogTrigger>
      )}
      <DialogContent>
        <DeactivateDeviceWizard device={device} />
      </DialogContent>
    </Dialog>
  );
}
