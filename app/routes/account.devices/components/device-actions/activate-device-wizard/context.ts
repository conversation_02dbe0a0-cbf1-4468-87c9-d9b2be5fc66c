import type { SystemDevice } from '@/business/core/types/device';
import { createWizardFormContext, createWizardStepsContext } from '@/components/Wizard/ctx-factory';
import type { ActivateDevicePart } from '@/routes/account.devices/resolver';
import type Stripe from 'stripe';

const { WizardFormProvider: ActivateFormProvider, useWizardForm: useActivateForm } = createWizardFormContext<
  {
    device: SystemDevice;
    invoice?: Stripe.Invoice;
  } & ActivateDevicePart
>();

const {
  WizardStepsProvider: ActivateStepsProvider,
  useWizardSteps: useActivateSteps,
  WizardStep: ActivateStep,
} = createWizardStepsContext();

export { ActivateFormProvider, useActivateForm, ActivateStepsProvider, useActivateSteps, ActivateStep };
