import { SYSTEM_FEATURES } from '@/business/core/constants/features';
import { Button } from '@/components/ui/button';
import { DialogClose, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { BaseStepState } from '@/components/Wizard/ctx-factory';
import { ActivateStep, useActivateForm, useActivateSteps } from './context';

const STEP_NAME = 'start';

export const useStartStep = (): BaseStepState => ({
  name: STEP_NAME,
});

export function StartStep() {
  return (
    <ActivateStep name={STEP_NAME}>
      <StartStepImpl />
    </ActivateStep>
  );
}

function StartStepImpl() {
  const { nextStep } = useActivateSteps();

  const {
    formData: { device },
  } = useActivateForm();
  const systemFeatures = SYSTEM_FEATURES[device.handle];
  const deviceName = systemFeatures.name;
  const deviceImage = systemFeatures.imageUrl;

  return (
    <ActivateStep name={STEP_NAME}>
      <DialogHeader>
        <DialogTitle>Activate your {deviceName} device</DialogTitle>
        <DialogDescription>Please follow the steps to activate your {deviceName}.</DialogDescription>
      </DialogHeader>
      <img src={deviceImage} alt={deviceName} className="h-auto w-full" />
      <DialogFooter className="justify-between!">
        <DialogClose asChild>
          <Button variant="outline">Cancel</Button>
        </DialogClose>
        <Button variant="primary" onClick={nextStep}>
          Next
        </Button>
      </DialogFooter>
    </ActivateStep>
  );
}
