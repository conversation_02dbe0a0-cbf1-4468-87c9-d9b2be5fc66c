import { SYSTEM_FEATURES } from '@/business/core/constants/features';
import { Button } from '@/components/ui/button';
import { DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { BaseStepState } from '@/components/Wizard/ctx-factory';
import { useCallback, useState } from 'react';
import { ActivateStep, useActivateForm, useActivateSteps } from './context';
import { useRemixForm } from 'remix-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { activateSchema, schema } from '@/routes/account.devices/resolver';
import TextField from '@/components/TextField';
import { Form } from '@/components/ui/form';

const STEP_NAME = 'nickname';

export const useNicknameStep = (): BaseStepState => ({
  name: STEP_NAME,
});

export const NicknameStep = () => (
  <ActivateStep name={STEP_NAME}>
    <NicknameStepImpl />
  </ActivateStep>
);

function NicknameStepImpl() {
  const { nextStep, prevStep } = useActivateSteps();
  const {
    commit,
    formData: { device, nickname: savedNickname },
  } = useActivateForm();
  const deviceName = SYSTEM_FEATURES[device.handle].name;

  const form = useRemixForm({
    resolver: zodResolver(activateSchema.pick({ nickname: true }).required()),
    defaultValues: {
      nickname: savedNickname || '',
    },
    submitHandlers: {
      onValid: data => {
        commit(data);
        nextStep();
      },
    },
  });

  return (
    <Form form={form} className="w-full space-y-4">
      <DialogHeader>
        <DialogTitle>Please provide a name for your {deviceName}.</DialogTitle>
        <DialogDescription>
          The name will be used to identify your {deviceName} in your app and notifications.
        </DialogDescription>
      </DialogHeader>
      <TextField name="nickname" label="Nickname" autoComplete="off" />
      <DialogFooter>
        <Button variant="outline" onClick={prevStep}>
          Back
        </Button>
        <Button variant="primary" type="submit">
          Set Device Name
        </Button>
      </DialogFooter>
    </Form>
  );
}
