import { SystemDevice } from '@/business/core/types/device';
import { action } from '@/routes/account.devices/action';
import { ActivateDevicePart, SchemaType } from '@/routes/account.devices/resolver';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { useCallback } from 'react';
import { useFetcher } from 'react-router';
import ProgressIndicator from '../progress-indicator';
import { CompleteStep, useCompleteStep } from './complete-step';
import { ActivateFormProvider, ActivateStepsProvider, useActivateForm, useActivateSteps } from './context';
import { NicknameStep, useNicknameStep } from './nickname-step';
import { StartStep, useStartStep } from './start-step';
import { UpgradeStep, useUpgradeStep } from './upgrade-step';
import { useVINStep, VINStep } from './vin-step';

export default function ActivateDeviceWizard({ device }: { device: SystemDevice }) {
  const activationFetcher = useFetcher<typeof action>({ key: 'activation' + device.key });
  const submit = useCallback(
    (state: ActivateDevicePart) => {
      activationFetcher.submit(
        {
          type: 'activate',
          deviceKey: device.key,
          consent: state.consent || 0,
          VIN: state.VIN,
          nickname: state.nickname,
        } as SchemaType,
        {
          method: 'POST',
        },
      );
    },
    [activationFetcher, device.key],
  );

  return (
    <ActivateFormProvider initialData={{ device }} onSubmit={submit}>
      <ActivateWizardSteps />
    </ActivateFormProvider>
  );
}

function ActivateWizardSteps() {
  const [animateRef] = useAutoAnimate<HTMLDivElement>();
  const { submit } = useActivateForm();

  return (
    <ActivateStepsProvider
      steps={[useStartStep(), useVINStep(), useNicknameStep(), useUpgradeStep(), useCompleteStep()]}
      onComplete={submit}
    >
      <ActivateProgress />
      <div ref={animateRef} className="w-full space-y-4">
        <StartStep />
        <VINStep />
        <NicknameStep />
        <UpgradeStep />
        <CompleteStep />
      </div>
    </ActivateStepsProvider>
  );
}

function ActivateProgress() {
  const { stepIndex, totalSteps } = useActivateSteps();

  return <ProgressIndicator stepIndex={stepIndex} totalSteps={totalSteps} />;
}
