import InvoiceConfirmationField from '@/components/InvoiceConfirmationField';
import { Button } from '@/components/ui/button';
import { DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { BaseStepState } from '@/components/Wizard/ctx-factory';
import { action } from '@/routes/account.devices/action';
import useDeviceService from '@/routes/account.devices/hooks/use-device-service';
import { SchemaType } from '@/routes/account.devices/resolver';
import { useFetcher } from 'react-router';
import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { ActivateStep, useActivateForm, useActivateSteps } from './context';

const STEP_NAME = 'upgrade';

export const useUpgradeStep = (): BaseStepState => {
  const {
    formData: { device },
  } = useActivateForm();
  const { wouldRequireUpgrade } = useDeviceService(device.handle);
  return {
    name: STEP_NAME,
    skip: !wouldRequireUpgrade,
    complete: false,
  };
};

export const UpgradeStep = () => (
  <ActivateStep name={STEP_NAME}>
    <UpgradeStepImpl />
  </ActivateStep>
);

export function UpgradeStepImpl() {
  const {
    commit,
    formData: { device, nickname },
  } = useActivateForm();
  const { nextStep, prevStep } = useActivateSteps();

  const [consent, setConsent] = useState(0);
  const quoteFetcher = useFetcher<typeof action>({ key: 'quote' + device.key });

  useEffect(() => {
    if (quoteFetcher.state == 'submitting') return;

    setConsent(0);
    quoteFetcher.submit(
      {
        deviceKey: device.key,
        type: 'quoteActivation',
      } as SchemaType,
      {
        method: 'POST',
      },
    );
  }, []);

  const submit = useCallback(() => {
    commit({ consent, invoice: quoteFetcher.data?.invoice });
    nextStep();
  }, [commit, nextStep, consent, quoteFetcher]);

  if (quoteFetcher.state != 'submitting') return <Loader2 className="mx-auto my-8 size-16 animate-spin" />;
  return (
    <>
      <DialogHeader>
        <DialogTitle>Confirm service details for "{nickname}"</DialogTitle>
        <DialogDescription>
          The changes to your service are detailed below. Please review and confirm to proceed.
        </DialogDescription>
      </DialogHeader>
      {quoteFetcher.data?.invoice && (
        <ScrollArea className="w-full" viewportClassName="max-h-[50vh]">
          <div className="px-4">
            <InvoiceConfirmationField invoice={quoteFetcher.data.invoice} consent={consent} setConsent={setConsent} />
          </div>
        </ScrollArea>
      )}
      <DialogFooter>
        <Button variant="outline" onClick={prevStep}>
          Back
        </Button>
        <Button variant="primary" disabled={!consent} onClick={submit}>
          Confirm & activate
        </Button>
      </DialogFooter>
    </>
  );
}
