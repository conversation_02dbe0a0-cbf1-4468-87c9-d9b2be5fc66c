import { SystemDevice } from '@/business/core/types/device';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ReactNode } from 'react';
import useDeviceService from '../../hooks/use-device-service';
import { ActivateDeviceAction } from './activate-device-modal';
import { DeactivateDeviceAction } from './deactivate-device-modal';

export function DeviceActions({
  device,
  children,
  className,
}: {
  device: SystemDevice;
  children?: ReactNode;
  className?: string;
}) {
  const { wouldRequireUpgrade } = useDeviceService(device.handle);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className={className} asChild>
        <Button variant="ghost" size="icon-sm">
          {children}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <ActivateDeviceAction
          device={device}
          disable={device.state == 'active'}
          title={
            device.state == 'deactivating'
              ? 'Cancel device deactivation'
              : wouldRequireUpgrade
                ? 'Upgrade & activate device'
                : 'Activate device'
          }
        />
        <DeactivateDeviceAction
          device={device}
          disable={device.state != 'activating' && device.state != 'active'}
          title={device.state == 'activating' ? 'Cancel device activation' : 'Deactivate device'}
        />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
