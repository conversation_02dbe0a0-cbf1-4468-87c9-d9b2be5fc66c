import { DeviceRepo } from '@/business/core/repositories/device';
import { SubscriptionRepo } from '@/business/core/repositories/subscription';
import { DeviceService } from '@/business/core/services/devices';
import { parseDeviceCustomerFields, SystemDevice } from '@/business/core/types/device';
import { AccountHandler } from '@/business/handlers/account.handler';
import { FieldErrors } from 'react-hook-form';
import { ActionFunctionArgs } from 'react-router';
import { getValidatedFormData } from 'remix-hook-form';
import Stripe from 'stripe';
import { resolver, SchemaType } from './resolver';
import { stripeDate } from '@/lib/utils';

export async function action({ request, context }: ActionFunctionArgs): Promise<{
  device?: SystemDevice;
  invoice?: Stripe.Invoice;
  paymentMethod?: Stripe.PaymentMethod;
  errors?: FieldErrors<SchemaType>;
}> {
  const accountHandler = context.resolve(AccountHandler);
  const { system, role: access } = await accountHandler.validateSystemAccess();

  if (access != 'write') throw new Response('Unauthorized', { status: 403 });

  const { data, errors } = await getValidatedFormData<SchemaType>(request, resolver);

  if (errors) return { errors };

  const subscriptionRepo = context.resolve(SubscriptionRepo);
  const deviceService = context.resolve(DeviceService);
  const deviceRepo = context.resolve(DeviceRepo);
  const devices = await deviceRepo.getDevices(system.ownerId);
  let device = devices.find(d => d.key == data.deviceKey);

  if (device?.systemKey != system.key) {
    throw new Response('Device not found', { status: 404 });
  }

  if (data.type == 'quoteActivation' || data.type == 'quoteDeactivation') {
    const { systemSubscription, lineItems, scheduledLineItems } =
      await deviceService.quoteLineItemsForDeviceActivationChange(device, data.type == 'quoteActivation', devices);

    const invoice = await subscriptionRepo.previewUpdateSystemLineItems({
      systemSubscription,
      lineItems,
      scheduledLineItems,
    });

    return { invoice };
  } else if (data.type == 'activate') {
    const result = parseDeviceCustomerFields({ ...device, ...data }, true);

    if (!result?.success) return { errors: result?.error as FieldErrors<SchemaType> };

    const activationConsent = data.consent;

    if (activationConsent && activationConsent > Date.now() - 15 * 60 * 1000) {
      device.activationConsentAt = activationConsent;

      const { hasChange, systemSubscription, lineItems, scheduledLineItems } =
        await deviceService.quoteLineItemsForDeviceActivationChange(device, true, devices);

      if (hasChange) {
        await subscriptionRepo.updateSystemLineItems({
          systemSubscription,
          lineItems,
          scheduledLineItems,
        });
      }
    }

    const newDevice = await deviceService.tryActivateDevice({ ...device, ...result.data } as SystemDevice, {
      devices,
      systemSubscription: system.subscriptionId!,
    });

    if ('VIN' in newDevice && newDevice.VIN && newDevice.VIN != (device as any)?.VIN) {
      await deviceService.triggerDeviceFlow(device, 'vehicle-vin-updated');
    }

    await deviceRepo.setDevices(system.ownerId, [newDevice]);

    return { device: newDevice };
  } else if (data.type == 'deactivate') {
    if (data.consent <= Date.now() - 15 * 60 * 1000) throw new Error('Deactivation consent timed out');

    const { systemSubscription, lineItems, scheduledLineItems, hasChange } =
      await deviceService.quoteLineItemsForDeviceActivationChange(device, false, devices);

    if (hasChange) {
      await subscriptionRepo.updateSystemLineItems({
        systemSubscription,
        lineItems,
        scheduledLineItems,
      });
    }

    device = await deviceService.scheduleDeviceForDeactivation(device!, {
      consentAt: new Date(data.consent),
      scheduledFor: stripeDate(systemSubscription.current_period_end),
    });
    await deviceRepo.setDevices(system.ownerId, [device]);

    return { device };
  }

  return {};
}
