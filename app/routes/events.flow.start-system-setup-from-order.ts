import { ShopifyClient } from '@/business/clients/shopify-client';
import { OrderService } from '@/business/core/services/order';
import { type ActionFunctionArgs } from 'react-router';

export async function action({ request, context }: ActionFunctionArgs) {
  const { payload } = await context.resolve(ShopifyClient).authenticate.flow(request);

  if (payload.handle != 'start-system-setup-from-order')
    throw new Response('Incorrect flow action hit.', { status: 403 });

  const orderId: string = payload?.properties?.order_id;
  if (typeof orderId !== 'string') throw new Response('No order id found.', { status: 400 });

  const result = await context.resolve(OrderService).startSystemSetup(orderId);

  return Response.json(result);
}
