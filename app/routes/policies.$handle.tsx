import { StorefrontClient } from '@/business/clients/storefront-client';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '@/business/handlers/builder.handler';
import HTMLText from '@/components/HTML';
import { generatePageTitle } from '@/lib/utils';
import { useLoaderData, type MetaFunction } from 'react-router';
import { type Shop } from '@shopify/hydrogen/storefront-api-types';
import { type LoaderFunctionArgs } from 'react-router';
import { LanguageCode } from 'storefront.types';
import Fallback from './home.$';

type SelectedPolicies = keyof Pick<Shop, 'privacyPolicy' | 'shippingPolicy' | 'termsOfService' | 'refundPolicy'>;

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [{ title: generatePageTitle(data?.title) }];
};

export async function loader(args: LoaderFunctionArgs) {
  const { params, context } = args;
  if (!params.handle) {
    throw new Response('No handle was passed in', { status: 404 });
  }

  const policyName = params.handle.replace(/-([a-z])/g, (_: unknown, m1: string) =>
    m1.toUpperCase(),
  ) as SelectedPolicies;

  const storefront = context.resolve(StorefrontClient);
  const data = await storefront.query(
    `#graphql
    fragment Policy on ShopPolicy {
      body
      handle
      id
      title
      url
    }
    query Policy(
      $country: CountryCode
      $language: LanguageCode
      $privacyPolicy: Boolean!
      $refundPolicy: Boolean!
      $shippingPolicy: Boolean!
      $termsOfService: Boolean!
    ) @inContext(language: $language, country: $country) {
      shop {
        privacyPolicy @include(if: $privacyPolicy) {
          ...Policy
        }
        shippingPolicy @include(if: $shippingPolicy) {
          ...Policy
        }
        termsOfService @include(if: $termsOfService) {
          ...Policy
        }
        refundPolicy @include(if: $refundPolicy) {
          ...Policy
        }
      }
    }
  `,
    {
      variables: {
        privacyPolicy: false,
        shippingPolicy: false,
        termsOfService: false,
        refundPolicy: false,
        [policyName]: true,
        language: storefront.i18n?.language as LanguageCode,
      },
    },
  );

  const policy = data.shop?.[policyName] || undefined;
  const content = !policy ? await context.resolve(BuilderHandler).handlePageFetch({ model: 'page' }) : undefined;
  const title = policy?.title || content?.name;

  return { title, policy, content };
}

export default function Policy() {
  const data = useLoaderData<typeof loader>();

  return (
    <div className="flex w-full flex-col items-center justify-center px-[48px]">
      <div className="mt-[64px] mb-[128px] w-full max-w-[1024px]">
        <p className="text-gray-dark mb-[96px] w-full text-center">{data.title}</p>
        {data.policy ? <HTMLText HTML={data.policy.body} className="w-full" /> : <Fallback />}
      </div>
    </div>
  );
}
