import { ShopifyClient } from '@/business/clients/shopify-client';
import { useFetcher } from 'react-router';
import { LoaderFunctionArgs } from 'react-router';
import { BlockStack, Button, Card, Image, InlineStack, Link, Page, Text } from '@shopify/polaris';

export const loader = async ({ request, context }: LoaderFunctionArgs) => {
  await context.resolve(ShopifyClient).authenticate.admin(request);

  return Response.json({
    success: true,
  });
};

export default function Admin() {
  return (
    <Page title="Admin Tools" primaryAction={<Link url="/admin/reveal-token">Reveal access token</Link>}>
      <BlockStack gap="200">
        <StripeIntegrationCard />
      </BlockStack>
    </Page>
  );
}

function StripeIntegrationCard() {
  const fetcher = useFetcher({ key: 'sync-stripe' });

  return (
    <Card>
      <InlineStack align="space-between" wrap={false} gap={'200'} blockAlign="start">
        <BlockStack inlineAlign="start" gap={'200'}>
          <Text as="h2" variant="headingSm">
            Stripe Integration
          </Text>
          <p>
            Currently updates to ADC Templates need to be synchronized with Stripe Features and updates to Stripe
            pricing needs to synchronized to the Shop. Click the button below to synchronize.
          </p>
          <Button
            onClick={() => fetcher.submit({}, { action: '/admin/api/sync-service', method: 'POST' })}
            loading={fetcher.state != 'idle'}
          >
            Sync shop services
          </Button>
        </BlockStack>
        <Image
          source="https://cdn.shopify.com/s/files/1/0549/2486/9694/files/stripe-logo.jpg?v=1729359873"
          alt="Stripe Logo"
          width={100}
          height={100}
        />
      </InlineStack>
    </Card>
  );
}
