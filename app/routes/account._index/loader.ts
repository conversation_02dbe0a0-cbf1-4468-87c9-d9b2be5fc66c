import { CustomerAccountClient } from '@/business/clients/accounts-client';
import { ADCRepo } from '@/business/core/repositories/adc';
import { OrderRepo } from '@/business/core/repositories/order';
import { SystemRepo } from '@/business/core/repositories/system';
import { GET_ORDER_TRACKING_INFO } from '@/business/customer-account/order';
import { AccountHandler } from '@/business/handlers/account.handler';
import { LoaderFunctionArgs } from 'react-router';

const getAccountSetupInfo = async (context: LoaderFunctionArgs['context']) => {
  const { ownerId, systemKey } = context.resolve(AccountHandler).getAccountSession() || {};

  if (!ownerId || !systemKey) return;

  const system = await context.resolve(SystemRepo).getSystem(ownerId, systemKey);

  if (!system?.firstOrderId) return;

  const [orderData, credentials, equipment] = await Promise.all([
    context.resolve(CustomerAccountClient).query(GET_ORDER_TRACKING_INFO, {
      variables: {
        orderId: system.firstOrderId,
      },
    }),
    context.resolve(OrderRepo).getNewADCCustomerCredentials(system.firstOrderId),
    context.resolve(ADCRepo).getCustomerEquipment(system.adcCustomerId, true),
  ]);

  const order = orderData?.data?.order;
  return { order, initialCredentials: credentials, equipment };
};

export async function loader({ context }: LoaderFunctionArgs) {
  const accountSetupInfo = await getAccountSetupInfo(context);

  return {
    accountSetupInfo,
  };
}
