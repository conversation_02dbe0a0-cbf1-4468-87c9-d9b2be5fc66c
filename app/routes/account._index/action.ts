import { TagRepo } from '@/business/core/repositories/tag';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { ActionFunctionArgs } from 'react-router';
import { validateFormData } from 'remix-hook-form';
import { resolver, SchemaType } from './resolver';

export async function action({ context, request }: ActionFunctionArgs) {
  const customerId = await context.resolve(AuthHandler).getLoggedInCustomerId({ redirectToLogin: true });
  const { data, errors } = await validateFormData<SchemaType, unknown>(await request.json(), resolver);

  if (errors) return { success: false, errors };

  const tagRepo = context.resolve(TagRepo);

  switch (data.action) {
    case 'tag':
      await tagRepo.addTags(customerId, data.tags);
      break;
    case 'removeTags':
      await tagRepo.removeTags(customerId, data.tags);
      break;
  }

  return { success: true };
}
