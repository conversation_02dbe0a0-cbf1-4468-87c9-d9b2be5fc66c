import { cn } from '@/lib/utils';
import { Check, LockKeyhole } from 'lucide-react';
import { Link } from 'react-router';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useAutoAnimate } from '@formkit/auto-animate/react';

export type StepState = 'locked' | 'not-started' | 'inprogress' | 'complete';

function StepStateIcon({ state, onClick, to }: { state: StepState; onClick?: () => void; to?: string }) {
  switch (state) {
    case 'locked':
      return <LockKeyhole className="size-8 shrink-0 p-1 text-muted-foreground" />;
    case 'not-started':
      return (
        <Button variant="primary" size="p-md" onClick={onClick} asChild={!!to}>
          {to ? <Link to={to}>Begin</Link> : 'Begin'}
        </Button>
      );
    case 'inprogress':
      return (
        <Button variant="primary" size="p-md" onClick={onClick} asChild={!!to}>
          {to ? <Link to={to}>Resume</Link> : 'Resume'}
        </Button>
      );
    case 'complete':
      return (
        <Check className="size-8 rounded-full bg-linear-to-r from-gradient-start to-gradient-end p-2 text-primary-foreground" />
      );
    default:
      return null;
  }
}

export type SetupCardProps = {
  state: Exclude<StepState, 'not-started'>;
  title: string;
  statusText?: string;
  required?: boolean;
  stepNumber: number;
  imageUrl?: string;
  completedStepCount?: number;
  totalStepCount?: number;
  children?: React.ReactNode;
};

export function SetupCard({
  state,
  title,
  stepNumber,
  imageUrl,
  statusText,
  required,
  completedStepCount,
  totalStepCount,
  children,
}: SetupCardProps) {
  const [autoAnimate] = useAutoAnimate();
  return (
    <Card
      ref={autoAnimate}
      className={
        state == 'inprogress'
          ? 'my-4 grid grid-cols-1 sm:my-6 sm:grid-cols-2'
          : 'my-4 flex flex-row items-center justify-between p-4 sm:my-6 sm:p-6'
      }
    >
      {state == 'inprogress' ? (
        <>
          <div className="flex flex-col gap-4 p-4 sm:border-r sm:border-muted sm:p-6">
            <div className="grow">
              {Boolean(imageUrl) && <img src={imageUrl} alt={title} className="float-right ml-2 max-h-36 max-w-24" />}
              <div className="flex size-8 shrink-0 items-center justify-center rounded-full border border-primary text-primary">
                {stepNumber}
              </div>
              <h2 className="mt-2 text-xl font-bold">{title}</h2>
              {required && (
                <p className="mt-1 text-sm text-primary">
                  <span className="-mt-2 mb-2">*</span>Required
                </p>
              )}
              {statusText && <p className={cn('text-sm text-muted-foreground sm:hidden')}>{statusText}</p>}
            </div>
            {totalStepCount && (
              <div className="grow-0 space-y-2">
                <p className="text-xs text-muted-foreground">
                  {completedStepCount} of {totalStepCount} completed
                </p>
                <Progress value={((completedStepCount || 0) * 100) / totalStepCount} />
              </div>
            )}
          </div>
          <div>{children}</div>
        </>
      ) : (
        <>
          <div className="flex items-center gap-4">
            <div className="flex size-8 shrink-0 items-center justify-center rounded-full border border-primary text-primary">
              {stepNumber}
            </div>
            <div className="space-y-1">
              <h2 className="font-bold">{title}</h2>
              {statusText && (
                <p className={cn('text-sm sm:hidden', state == 'complete' ? 'text-primary' : 'text-muted-foreground')}>
                  {statusText}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center gap-4">
            {statusText && (
              <p
                className={cn(
                  'hidden text-right text-sm sm:block',
                  state == 'complete' ? 'text-primary' : 'text-muted-foreground',
                )}
              >
                {statusText}
              </p>
            )}
            <StepStateIcon state={state} />
          </div>
        </>
      )}
    </Card>
  );
}

export type SetupCardStepProps = {
  state: StepState;
  number: number;
  title: string;
  onClick?: () => void;
  to?: string;
};

export function SetupCardStep({ number, title, state, onClick, to }: SetupCardStepProps) {
  return (
    <div className="flex items-center justify-between border-b border-muted px-4 py-6 last:border-b-0 sm:border-r sm:border-muted sm:px-6 sm:py-8">
      <h2 className="text-sm font-semibold text-muted-foreground md:text-lg">
        {number}. {title}
      </h2>
      <StepStateIcon state={state} onClick={onClick} to={to} />
    </div>
  );
}
