import { SETUP_COMPLETE_DISMISSED } from '@/routes/account._index/constants';
import { useAccountLoader } from '@/routes/account/hooks';
import { SetupStepState } from '../types';
import { useAddContactSubStep } from './add-contact';
import { useAppLoginSubStep } from './app-login';
import { useSubscriptionSubStep } from './subscription';

export function useAccountSetupStep(): SetupStepState {
  const { profile } = useAccountLoader();
  const completedOnce = Boolean(profile.tags?.includes(SETUP_COMPLETE_DISMISSED));

  const subSteps = [useSubscriptionSubStep(), useAppLoginSubStep(), useAddContactSubStep()];
  const complete = subSteps.every(s => s.complete);

  return {
    name: 'setup-account',
    title: 'Account Setup',
    imageUrl: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/account-setup-image.png?v=**********',
    required: false,
    complete,
    skip: completedOnce && complete,
    subSteps,
  };
}
