import { SETUP_COMPLETE_DISMISSED } from '@/routes/account._index/constants';
import { useSetupForm } from '@/routes/account._index/resolver';
import { useAccountLoader } from '@/routes/account/hooks';
import { SetupStepState, SetupSubStepState } from '../types';
import { PreferencesDialog } from './preferences-dialog';
import { useSetUpNotificationsStep } from './set-up-a-notification';
import { useSetUpAdjustDriverBehaviorStep } from './set-up-adjust-driver-behavior';
import { useSetUpGeofencesStep } from './set-up-geofences-step';
import { useSetUpReportsStep } from './set-up-reports-step';
import { useSetUpUsersStep } from './set-up-users';
import { FLEET_PREFERENCES_DIALOG } from './context';

export function useSetupFleetPreferencesStep(): SetupStepState {
  const {
    formData: { currentDialog },
  } = useSetupForm();
  const { profile } = useAccountLoader();

  const subSteps: SetupSubStepState[] = [
    useSetUpReportsStep(),
    useSetUpUsersStep(),
    useSetUpGeofencesStep(),
    useSetUpNotificationsStep(),
    useSetUpAdjustDriverBehaviorStep(),
  ];

  const completedOnce = Boolean(profile.tags?.includes(SETUP_COMPLETE_DISMISSED));
  const complete = subSteps.every(step => step.complete) && currentDialog != FLEET_PREFERENCES_DIALOG;

  return {
    name: 'setup-fleet-aware-preferences',
    title: 'Set Up Fleet Aware Preferences',
    imageUrl:
      'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/Fleet_Tracker_Product_Card_image_scaled.png?v=**********',
    skip: completedOnce && complete,
    complete,
    prerequisites: ['setup-account'],
    required: true,
    subSteps,
    Dialog: PreferencesDialog,
  };
}
