import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useCallback } from 'react';
import { FLEET_PREFERENCES_DIALOG, PreferencesStepsProvider } from './context';
import { SetupNotifications, useSetUpNotificationsStep } from './set-up-a-notification';
import { SetupBehavior, useSetUpAdjustDriverBehaviorStep } from './set-up-adjust-driver-behavior';
import { AppLogin, useAppLoginStep } from './set-up-app-login-step';
import { SetupBonus, useSetUpBonusStep } from './set-up-bonus-step';
import { SetupGeofences, useSetUpGeofencesStep } from './set-up-geofences-step';
import { SetupReports, useSetUpReportsStep } from './set-up-reports-step';
import { useSetupForm } from '../../resolver';
import { SetupUsers, useSetUpUsersStep } from './set-up-users';
import { SetupCongrats, useSetUpCongratsStep } from './set-up-congrats-step';

export function PreferencesDialog() {
  const {
    formData: { currentDialog },
    commit,
  } = useSetupForm();

  const setOpen = useCallback(
    (toOpen: boolean) => commit({ currentDialog: toOpen ? FLEET_PREFERENCES_DIALOG : null }),
    [commit],
  );
  const close = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <Dialog open={currentDialog == FLEET_PREFERENCES_DIALOG} onOpenChange={setOpen}>
      <DialogContent className="flex h-screen flex-col gap-4 overflow-y-auto md:h-[90%] md:w-[800px] md:max-w-[90vw]">
        <PreferencesStepsProvider
          steps={[
            useAppLoginStep(),
            useSetUpReportsStep(),
            useSetUpUsersStep(),
            useSetUpGeofencesStep(),
            useSetUpNotificationsStep(),
            useSetUpAdjustDriverBehaviorStep(),
            useSetUpBonusStep(),
            useSetUpCongratsStep(),
          ]}
          onComplete={close}
        >
          <AppLogin />
          <SetupReports />
          <SetupUsers />
          <SetupGeofences />
          <SetupNotifications />
          <SetupBehavior />
          <SetupBonus />
          <SetupCongrats />
        </PreferencesStepsProvider>
      </DialogContent>
    </Dialog>
  );
}
