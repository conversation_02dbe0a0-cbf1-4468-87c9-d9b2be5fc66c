import { createWizardStepsContext } from '@/components/Wizard/ctx-factory';
import { useSetupForm } from '@/routes/account._index/resolver';
import { useCallback } from 'react';

export const {
  WizardStep: PreferencesStep,
  useWizardSteps: usePreferencesSteps,
  WizardStepsProvider: PreferencesStepsProvider,
} = createWizardStepsContext();

export const FLEET_PREFERENCES_DIALOG = 'fleet-preferences';

export const useOpenPreferencesDialog = () => {
  const { commit } = useSetupForm();
  const openDialog = useCallback(() => commit({ currentDialog: FLEET_PREFERENCES_DIALOG }), [commit]);
  return openDialog;
};
