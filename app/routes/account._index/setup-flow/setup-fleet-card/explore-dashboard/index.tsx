import { Dialog, DialogContent } from '@/components/ui/dialog';
import { WizardStepsProvider } from '@/components/Wizard/ctx-factory';
import { SETUP_FLEET_DASHBOARD_EXPLORED_COMPLETE } from '@/routes/account._index/constants';
import { useSetupForm } from '@/routes/account._index/resolver';
import { useAccountLoader } from '@/routes/account/hooks';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { useSearchParams } from 'react-router';
import { useCallback, useEffect } from 'react';
import { SetupSubStepState } from '../../types';
import { LandedDesktop, useLandedDesktopStep } from './landed-desktop';
import { LinkSent, useLinkSentStep } from './link-sent';
import MobileFunnelStep, { useMobileFunnelStep } from './mobile-funnel-step';
import { TourStep, useTourStep } from './tour-step';
import useDevices from '@/routes/account.devices/hooks/use-devices';

const STEP_NAME = 'explore-dashboard';

export function useExploreDashboard(): SetupSubStepState {
  const { profile } = useAccountLoader();
  const devices = useDevices();

  const { commit } = useSetupForm();
  const openDialog = useCallback(() => {
    commit({ currentDialog: STEP_NAME });
  }, []);

  const complete = Boolean(profile.tags?.includes(SETUP_FLEET_DASHBOARD_EXPLORED_COMPLETE));

  return {
    name: STEP_NAME,
    title: 'Explore the Dashboard',
    complete,
    Dialog: ExploreDashboardDialog,
    onClick: openDialog,
    locked: !devices?.['fleet-tracker']?.some(device => 'VIN' in device),
    prerequisites: false,
  };
}

function ExploreDashboardDialog() {
  const [animateRef] = useAutoAnimate();
  const [searchParams, setSearchParams] = useSearchParams();
  const {
    formData: { currentDialog },
    commit,
    submit,
  } = useSetupForm();

  const setOpen = useCallback((open: boolean) => {
    commit({ currentDialog: open ? STEP_NAME : null });
  }, []);

  useEffect(() => {
    if (searchParams.has('explore-dashboard')) {
      setOpen(true);
      searchParams.delete('explore-dashboard');
      setSearchParams({ ...searchParams });
    }
  }, []);

  return (
    <Dialog open={currentDialog == STEP_NAME} onOpenChange={setOpen}>
      <DialogContent className="flex h-screen flex-col gap-4 overflow-y-auto md:h-[90%] md:w-[800px] md:max-w-[90vw]">
        <div ref={animateRef} className="flex flex-col gap-4">
          <ExploreDashboardSteps
            onComplete={() => submit({ action: 'tag', tags: [SETUP_FLEET_DASHBOARD_EXPLORED_COMPLETE] }, true)}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}

function ExploreDashboardSteps({ onComplete }: { onComplete: () => void }) {
  return (
    <WizardStepsProvider
      steps={[useMobileFunnelStep(), useLinkSentStep(), useLandedDesktopStep(), useTourStep()]}
      onComplete={onComplete}
    >
      <MobileFunnelStep />
      <LinkSent />
      <LandedDesktop />
      <TourStep />
    </WizardStepsProvider>
  );
}
