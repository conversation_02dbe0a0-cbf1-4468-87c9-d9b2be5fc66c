import { SETUP_COMPLETE_DISMISSED } from '@/routes/account._index/constants';
import useDevices from '@/routes/account.devices/hooks/use-devices';
import { useAccountLoader } from '@/routes/account/hooks';
import { SetupStepState, SetupSubStepState } from '../types';
import { useAddVehiclesSubStep } from './add-vehicles';
import { useExploreDashboard } from './explore-dashboard';
import { usePlugInTrackersSubStep } from './plug-in-trackers';

export function useSetupFleetStep(): SetupStepState {
  const { profile } = useAccountLoader();
  const completedOnce = Boolean(profile.tags?.includes(SETUP_COMPLETE_DISMISSED));

  const devices = useDevices();

  const subSteps: SetupSubStepState[] = [useAddVehiclesSubStep(), usePlugInTrackersSubStep(), useExploreDashboard()];
  const complete = subSteps.every(step => step.complete);

  return {
    name: 'setup-fleet',
    title: 'Set Up Fleet Aware',
    imageUrl:
      'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/Fleet_Tracker_Product_Card_image_scaled.png?v=**********',
    skip: devices['fleet-tracker']?.length === 0 || (completedOnce && complete),
    complete,
    prerequisites: ['setup-account'],
    required: true,
    subSteps,
  };
}
