import { SYSTEM_FEATURES } from '@/business/core/constants/features';
import { OWNER_ID_METADATA_KEY } from '@/business/core/constants/subscription';
import { SystemFeatureIdentifier } from '@/business/core/types';
import { convertGidToId } from '@/lib/utils';
import { useAccountLoader } from '@/routes/account/hooks';
import { useLoaderData } from 'react-router';
import { loader } from '../../loader';
import { SetupSubStepState } from '../types';
import useDevices from '@/routes/account.devices/hooks/use-devices';

export function usePlugInTrackersSubStep(): SetupSubStepState {
  const { subscription } = useAccountLoader();
  const devices = useDevices();
  const { accountSetupInfo } = useLoaderData<typeof loader>();

  const identifier = SYSTEM_FEATURES['fleet-tracker']?.identifier as SystemFeatureIdentifier & { method: 'equipment' };
  const fleetTrackers = accountSetupInfo?.equipment.filter(
    e => e.deviceType == identifier?.deviceType && parseInt(e.groupId.toString()) == identifier?.groupId,
  );

  const totalTrackers = devices['fleet-tracker']?.length || 0;
  const unaddedTrackers = totalTrackers - (fleetTrackers?.length || 0);
  const unpluggedTrackers =
    unaddedTrackers + (fleetTrackers?.reduce((total, ft) => total + (ft.status?.every(s => s == 0) ? 1 : 0), 0) || 0);

  const ownerId = subscription?.metadata[OWNER_ID_METADATA_KEY]!;
  return {
    name: 'plug-in-trackers',
    title: `Plug In Trackers`,
    complete: unpluggedTrackers == 0,
    started: unpluggedTrackers < totalTrackers,
    to: `/plug-in-guide/fleet-tracker/${convertGidToId(ownerId)}`,
    locked: !devices?.['fleet-tracker']?.some(device => 'VIN' in device),
    prerequisites: false,
  };
}
