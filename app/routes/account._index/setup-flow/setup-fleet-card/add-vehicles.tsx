import { convertGidToId } from '@/lib/utils';
import useDevices from '@/routes/account.devices/hooks/use-devices';
import { SetupSubStepState } from '../types';

export function useAddVehiclesSubStep(): SetupSubStepState {
  const devices = useDevices();

  const orderId = devices?.['fleet-tracker']?.find(
    device => device.state == 'activating' && !('VIN' in device),
  )?.orderId!;

  return {
    name: 'add-vehicles',
    title: 'Add Your Vehicles',
    complete: Boolean(!orderId),
    started: Boolean(devices?.['fleet-tracker']?.some(device => 'VIN' in device)),
    to: `/vin-tool/${convertGidToId(orderId)}`,
    prerequisites: true,
  };
}
