import { SETUP_FLEX_AWARE_CONNECTED_CABLE_COMPLETE } from '@/routes/account._index/constants';
import useDevices from '@/routes/account.devices/hooks/use-devices';
import { useAccountLoader } from '@/routes/account/hooks';
import { SetupStepState, SetupSubStepState } from '../types';
import { useNotificationTestSubStep } from './notification-test';
import { useInstallLocationSubStep } from './install-location';
import { useRefineNotificationsSubStep } from './refine-notifications';
import { FlexAwareSetupDialog } from './flex-aware-setup-dialog';
import { FLEX_AWARE_CONNECTED_CABLE_SETUP_DIALOG } from './context';
import { useSetupForm } from '../../resolver';

export function useSetupFlexAwareConnectedCableStep(): SetupStepState {
  const {
    formData: { currentDialog },
  } = useSetupForm();
  const { profile } = useAccountLoader();
  const completedOnce = Boolean(profile.tags?.includes(SETUP_FLEX_AWARE_CONNECTED_CABLE_COMPLETE));

  const devices = useDevices();

  const subSteps: SetupSubStepState[] = [
    useNotificationTestSubStep(),
    useInstallLocationSubStep(),
    useRefineNotificationsSubStep(),
  ];
  const complete = subSteps.every(step => step.complete) && currentDialog != FLEX_AWARE_CONNECTED_CABLE_SETUP_DIALOG;

  return {
    name: 'setup-flex-aware-connected-cable',
    title: 'Set Up Flex Aware Connected Cable',
    imageUrl:
      'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/FlexAwareConnectedCablerightfacewithcable.jpg?v=**********',
    skip:
      !devices['flex-aware']?.length ||
      devices['flex-aware'].every(d => !d.productHandle?.includes('connected-cable')) ||
      (completedOnce && complete),
    complete,
    prerequisites: ['setup-account'],
    required: true,
    subSteps,
    Dialog: FlexAwareSetupDialog,
  };
}
