import {
  SETUP_FLEX_AWARE_CONNECTED_CABLE_REFINE_NOTIFICATIONS_COMPLETE,
  SETUP_FLEX_AWARE_CONNECTED_CABLE_COMPLETE,
  SETUP_FLEX_AWARE_CONNECTED_CABLE_INSTALL_LOCATION_COMPLETE,
  SETUP_FLEX_AWARE_CONNECTED_CABLE_NOTIFICATION_COMPLETE,
} from '../../constants';
import { FlexAwareSetupStep } from './context';
import { useFlexAwareSetupSteps } from './context';
import { useSetupForm } from '../../resolver';
import { DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { SeparatorWithText } from '@/components/SeparatorWithText';
import { STEP_NAME as NOTIFICATION_STEP_NAME } from './notification-test';
import { useAccountLoader } from '@/routes/account/hooks';
import { BaseStepState } from '@/components/Wizard/ctx-factory';

const STEP_NAME = 'progress-step';

export function useProgressStepSubStep(): BaseStepState {
  const { profile } = useAccountLoader();

  return {
    name: STEP_NAME,
    complete: !!profile.tags?.includes(SETUP_FLEX_AWARE_CONNECTED_CABLE_COMPLETE),
  };
}

export function ProgressStep() {
  const { nextStep, goToStep } = useFlexAwareSetupSteps();
  const { submit } = useSetupForm();

  function handleContinue() {
    submit({
      action: 'removeTags',
      tags: [
        SETUP_FLEX_AWARE_CONNECTED_CABLE_NOTIFICATION_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_INSTALL_LOCATION_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_REFINE_NOTIFICATIONS_COMPLETE,
      ],
    });
    goToStep(NOTIFICATION_STEP_NAME);
  }

  return (
    <FlexAwareSetupStep name={STEP_NAME}>
      <h3 className="text-sm font-semibold text-primary">Set Up Flex Aware Connected Cable: 3 of 3</h3>
      <DialogTitle className="text-4xl font-bold">Great Progress!</DialogTitle>
      <DialogDescription>
        <p className="mb-2">
          If you have <strong>more</strong> Flex Aware Connected Cables to set up, repeat this process again.
        </p>

        <Button className="mt-4 w-full md:max-w-xs" onClick={handleContinue}>
          Continue Setting Up Next Flex Aware Connected Cable
        </Button>
        <SeparatorWithText text="OR" className="mt-8 mb-6" />
        <p className="mb-2">
          If you have <strong>no more</strong> Flex Aware’s to set up, mark all as done.
        </p>
        <Button
          className="mt-4 w-full md:max-w-xs"
          onClick={() => {
            submit({ action: 'tag', tags: [SETUP_FLEX_AWARE_CONNECTED_CABLE_COMPLETE] });
            nextStep();
          }}
        >
          I am all done!
        </Button>
      </DialogDescription>
    </FlexAwareSetupStep>
  );
}
