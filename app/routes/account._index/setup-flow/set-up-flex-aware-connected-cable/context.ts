import { createWizardStepsContext } from '@/components/Wizard/ctx-factory';
import { useSetupForm } from '@/routes/account._index/resolver';
import { useCallback } from 'react';

export const {
  WizardStep: FlexAwareSetupStep,
  useWizardSteps: useFlexAwareSetupSteps,
  WizardStepsProvider: FlexAwareSetupStepsProvider,
} = createWizardStepsContext();

export const FLEX_AWARE_CONNECTED_CABLE_SETUP_DIALOG = 'flex-aware-connected-cable-setup';

export const useOpenFlexAwareSetupDialog = () => {
  const { commit } = useSetupForm();
  const openDialog = useCallback(() => commit({ currentDialog: FLEX_AWARE_CONNECTED_CABLE_SETUP_DIALOG }), [commit]);
  return openDialog;
};
