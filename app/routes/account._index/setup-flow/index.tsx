import { Skeleton } from '@/components/ui/skeleton';
import { useAccountLoader } from '@/routes/account/hooks';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { Await, useFetcher } from 'react-router';
import { Fragment, Suspense, useCallback } from 'react';
import GreetingMessage from '../components/greeting-message';
import { SetupCard, SetupCardProps, SetupCardStep, SetupCardStepProps, StepState } from '../components/setup-card';
import { SETUP_COMPLETE_DISMISSED } from '../constants';
import { SchemaType, SetupFormProvider } from '../resolver';
import { useAccountSetupStep } from './setup-account-card';
import SetupCompleteCard from './setup-complete-card';
import { useSetupFleetStep } from './setup-fleet-card';
import { useSetupFleetPreferencesStep } from './setup-fleet-preferences-card';
import StillInTransitCard from './still-in-transit-card';
import { SetupStepState, SetupSubStepState } from './types';
import { useSetupFlexAwareStep } from './set-up-flex-aware';
import { useSetupFlexAwareConnectedCableStep } from './set-up-flex-aware-connected-cable';

const useSetupFlowSteps = () => [
  useAccountSetupStep(), //Add steps here
  useSetupFleetStep(),
  useSetupFleetPreferencesStep(),
  useSetupFlexAwareStep(),
  useSetupFlexAwareConnectedCableStep(),
];

export default function SetupFlow() {
  const [animateRef] = useAutoAnimate();
  const fetcher = useFetcher({ key: 'setup' });

  const submitForm = useCallback(
    (data: SchemaType) => {
      fetcher.submit(data, { method: 'POST', encType: 'application/json' });
    },
    [fetcher],
  );

  const { devices } = useAccountLoader();

  return (
    <SetupFormProvider onSubmit={submitForm}>
      <Suspense fallback={<Skeleton className="h-[500px] w-full" />}>
        <Await resolve={devices}>
          <div ref={animateRef} className="w-full">
            <SetupFlowImpl />
          </div>
        </Await>
      </Suspense>
    </SetupFormProvider>
  );
}

function SetupFlowImpl() {
  const { subscription, profile } = useAccountLoader();
  const steps = useSetupFlowSteps();

  if (!subscription?.id) {
    return <StillInTransitCard />;
  }

  if (!steps.every(step => step.complete)) {
    return <SetupCards steps={steps} />;
  }

  if (!profile.tags?.includes(SETUP_COMPLETE_DISMISSED)) {
    return <SetupCompleteCard />;
  }

  return <GreetingMessage message={`Welcome back`} />;
}

function SetupCards({ steps }: { steps: SetupStepState[] }) {
  let stepNumber = 0;
  let firstIncompleteStepIndex = -1;

  return (
    <>
      <GreetingMessage message={`Let’s get started`} />
      {steps.map((step, index) => {
        if (step.skip) return null;

        stepNumber++;

        const totalStepCount = step.subSteps.reduce((acc, subStep) => acc + (subStep.skip ? 0 : 1), 0);
        const completedStepCount = step.subSteps.reduce(
          (acc, subStep) => acc + (subStep.complete && !subStep.skip ? 1 : 0),
          0,
        );

        let statusText: string | undefined = undefined;
        const missedPrereqs: string[] = [];

        if (typeof step.prerequisites == 'boolean' && step.prerequisites) {
          for (let i = 0; i < index; i++) {
            if (!steps[i].complete && !steps[i].skip) missedPrereqs.push(steps[i].title);
          }
        } else if (Array.isArray(step.prerequisites)) {
          for (const prereq of step.prerequisites) {
            const other = steps.find(s => s.name === prereq);
            if (other && !other.complete && !other.skip) missedPrereqs.push(other.title);
          }
        }

        if (!step.complete && firstIncompleteStepIndex == -1) firstIncompleteStepIndex = index;

        const state: Exclude<StepState, 'not-started'> = step.complete
          ? 'complete'
          : !missedPrereqs.length && !step.locked
            ? 'inprogress'
            : 'locked';

        if (missedPrereqs.length) {
          statusText = `*Complete ${missedPrereqs
            .map((p, i) => (missedPrereqs.length >= 2 && i == missedPrereqs.length - 1 ? `and “${p}”` : `“${p}”`))
            .join(', ')} to Unlock`;
        }

        const Component = step.Component || SetupCard;
        const { imageUrl, title, required } = step;
        const props: SetupCardProps = {
          state,
          imageUrl,
          stepNumber,
          statusText,
          title,
          required,
          totalStepCount,
          completedStepCount,
        };

        return (
          <Fragment key={step.name}>
            <Component {...props}>
              <SetupCardSubSteps steps={step.subSteps} />
            </Component>
            {step.Dialog && <step.Dialog {...props} />}
          </Fragment>
        );
      })}
    </>
  );
}

function SetupCardSubSteps({ steps }: { steps: SetupSubStepState[] }) {
  let stepNumber = 0;

  return (
    <>
      {steps.map((step, index) => {
        if (step.skip) return null;
        stepNumber++;

        let locked = step.locked || false;

        if (typeof step.prerequisites == 'boolean' && step.prerequisites) {
          for (let i = 0; i < index; i++) {
            if (!steps[i].complete) {
              locked = true;
              break;
            }
          }
        } else if (Array.isArray(step.prerequisites)) {
          for (const prereq of step.prerequisites) {
            const other = steps.find(s => s.name === prereq);
            if (other && !other.complete && !other.skip) {
              locked = true;
              break;
            }
          }
        }

        const state: StepState = step.complete
          ? 'complete'
          : locked
            ? 'locked'
            : step.started
              ? 'inprogress'
              : 'not-started';

        const Component = step.Component || SetupCardStep;
        const { title, to, onClick } = step;
        const props: SetupCardStepProps = {
          number: stepNumber,
          title,
          state,
          to,
          onClick,
        };

        return (
          <Fragment key={step.name}>
            <Component {...props} />
            {step.Dialog && <step.Dialog {...props} />}
          </Fragment>
        );
      })}
    </>
  );
}
