import { FC } from 'react';
import { SetupCardProps, SetupCardStepProps } from '../components/setup-card';

export type SetupSubStepState = {
  name: string;
  complete: boolean;
  title: string;
  started?: boolean;
  skip?: boolean;
  prerequisites?: string[] | boolean;
  locked?: boolean;
  to?: string;
  onClick?: () => void;
  Component?: FC<SetupCardStepProps>;
  Dialog?: FC<Partial<SetupCardStepProps>>;
};
export type SetupStepState = {
  name: string;
  title: string;
  skip?: boolean;
  complete?: boolean;
  imageUrl?: string;
  required?: boolean;
  prerequisites?: string[] | boolean;
  locked?: boolean;
  subSteps: SetupSubStepState[];
  Component?: FC<SetupCardProps>;
  Dialog?: FC<Partial<SetupCardProps>>;
};
