import { SETUP_FLEX_AWARE_COMPLETE } from '@/routes/account._index/constants';
import useDevices from '@/routes/account.devices/hooks/use-devices';
import { useAccountLoader } from '@/routes/account/hooks';
import { SetupStepState, SetupSubStepState } from '../types';
import { useNotificationTestSubStep } from './notification-test';
import { useInstallLocationSubStep } from './install-location';
import { useRefineNotificationsSubStep } from './refine-notifications';
import { FlexAwareSetupDialog } from './flex-aware-setup-dialog';
import { FLEX_AWARE_SETUP_DIALOG } from './context';
import { useSetupForm } from '../../resolver';

export function useSetupFlexAwareStep(): SetupStepState {
  const {
    formData: { currentDialog },
  } = useSetupForm();
  const { profile } = useAccountLoader();
  const completedOnce = Boolean(profile.tags?.includes(SETUP_FLEX_AWARE_COMPLETE));

  const devices = useDevices();

  const subSteps: SetupSubStepState[] = [
    useNotificationTestSubStep(),
    useInstallLocationSubStep(),
    useRefineNotificationsSubStep(),
  ];
  const complete = subSteps.every(step => step.complete) && currentDialog != FLEX_AWARE_SETUP_DIALOG;

  return {
    name: 'setup-flex-aware',
    title: 'Set Up Flex Aware',
    imageUrl:
      'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/flex-aware_fa-product-image_resi_desktop_374x374_2x_v1_comp.png?v=**********',
    skip:
      !devices['flex-aware']?.length ||
      devices['flex-aware'].every(d => d.productHandle?.includes('connected-cable')) ||
      (completedOnce && complete),
    complete,
    prerequisites: ['setup-account'],
    required: true,
    subSteps,
    Dialog: FlexAwareSetupDialog,
  };
}
