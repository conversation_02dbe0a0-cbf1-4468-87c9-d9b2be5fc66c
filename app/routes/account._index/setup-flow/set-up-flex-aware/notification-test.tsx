import { Button } from '@/components/ui/button';
import { DialogDescription, DialogTitle } from '@/components/ui/dialog';
import { SETUP_FLEX_AWARE_NOTIFICATION_COMPLETE } from '@/routes/account._index/constants';
import { useAccountLoader } from '@/routes/account/hooks';
import { Check, ExternalLink, TriangleAlert } from 'lucide-react';
import { useSetupForm } from '../../resolver';
import { SetupSubStepState } from '../types';
import { FlexAwareSetupStep, useFlexAwareSetupSteps, useOpenFlexAwareSetupDialog } from './context';

export const STEP_NAME = 'notification-test';

export function useNotificationTestSubStep(): SetupSubStepState {
  const { profile } = useAccountLoader();

  return {
    name: STEP_NAME,
    title: 'Perform a Notification Test',
    complete: !!profile.tags?.includes(SETUP_FLEX_AWARE_NOTIFICATION_COMPLETE),
    prerequisites: true,
    onClick: useOpenFlexAwareSetupDialog(),
  };
}

export function NotificationTest() {
  const { nextStep } = useFlexAwareSetupSteps();
  const { submit } = useSetupForm();
  const { systemType } = useAccountLoader();
  const href =
    systemType === 'business'
      ? 'https://support-business.allaware.com/hc/en-us/articles/**************-How-to-Set-Up-Your-Flex-Aware-Part-1'
      : 'https://support.allaware.com/hc/en-us/articles/**************-How-to-Set-Up-Your-Flex-Aware-Part-1';

  return (
    <FlexAwareSetupStep name={STEP_NAME}>
      <h3 className="text-sm font-semibold text-primary">Set Up Flex Aware: 1 of 3</h3>
      <DialogTitle className="text-4xl font-bold">Perform a Notification Test</DialogTitle>
      <DialogDescription>
        <p className="mb-2">
          We recommend doing this at home <strong>before</strong> going out to install the device! Open the article
          below to perform the test.
        </p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Insert batteries
        </p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Add a notification
        </p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Perform the test
        </p>
      </DialogDescription>
      <div className="rounded-sm border border-destructive/50 p-2 text-sm text-destructive">
        <TriangleAlert className="inline size-4" /> Return here when the test is completed.
      </div>

      <Button className="mt-4 w-full md:max-w-xs" asChild>
        <a href={href} target="_blank" rel="noreferrer">
          Perform a Notification Test <ExternalLink className="inline size-4" />
        </a>
      </Button>
      <Button
        className="mt-4 w-full max-w-xs"
        variant="outline"
        onClick={() => {
          submit({ action: 'tag', tags: [SETUP_FLEX_AWARE_NOTIFICATION_COMPLETE] });
          nextStep();
        }}
      >
        <Check className="inline size-4" /> Mark as Complete
      </Button>
    </FlexAwareSetupStep>
  );
}
