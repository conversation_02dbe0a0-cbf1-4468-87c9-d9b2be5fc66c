import { Button } from '@/components/ui/button';
import { DialogDescription, DialogTitle } from '@/components/ui/dialog';
import { useAccountLoader } from '@/routes/account/hooks';
import { Check, ExternalLink } from 'lucide-react';
import { SETUP_FLEX_AWARE_INSTALL_LOCATION_COMPLETE } from '../../constants';
import { useSetupForm } from '../../resolver';
import { SetupSubStepState } from '../types';
import { FlexAwareSetupStep, useFlexAwareSetupSteps, useOpenFlexAwareSetupDialog } from './context';
const STEP_NAME = 'install-location';

export function useInstallLocationSubStep(): SetupSubStepState {
  const { profile } = useAccountLoader();

  return {
    name: STEP_NAME,
    title: 'Mount at Install Location',
    complete: !!profile.tags?.includes(SETUP_FLEX_AWARE_INSTALL_LOCATION_COMPLETE),
    prerequisites: true,
    onClick: useOpenFlexAwareSetupDialog(),
  };
}

export function InstallLocation() {
  const { nextStep } = useFlexAwareSetupSteps();
  const { submit } = useSetupForm();
  const { systemType } = useAccountLoader();
  const href =
    systemType === 'business'
      ? 'https://support-business.allaware.com/hc/en-us/articles/**************-How-to-Set-Up-Your-Flex-Aware-Part-2'
      : 'https://support.allaware.com/hc/en-us/articles/**************-How-to-Set-Up-Your-Flex-Aware-Part-2';

  return (
    <FlexAwareSetupStep name={STEP_NAME}>
      <h3 className="text-sm font-semibold text-primary">Set Up Flex Aware: 2 of 3</h3>
      <DialogTitle className="text-4xl font-bold">Install Flex Aware</DialogTitle>
      <DialogDescription>
        <p className="mb-2">Now that we have tested the Flex Aware, we’re ready to install it on site.</p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Gather Recommended Tools
        </p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Test Cellular Reception on Site
        </p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Mount Flex Aware
        </p>
        <p className="mt-2">
          <strong>Pro Tip:</strong> Skim through the Installation Video before going on site to understand how to
          install it.
        </p>
      </DialogDescription>

      <Button className="mt-4 w-full md:max-w-xs" asChild>
        <a href={href} target="_blank" rel="noreferrer">
          Open Install Guide & Video <ExternalLink className="inline size-4" />
        </a>
      </Button>
      <Button
        className="mt-4 w-full max-w-xs"
        variant="outline"
        onClick={() => {
          submit({ action: 'tag', tags: [SETUP_FLEX_AWARE_INSTALL_LOCATION_COMPLETE] });
          nextStep();
        }}
      >
        <Check className="inline size-4" /> Mark as Complete
      </Button>
    </FlexAwareSetupStep>
  );
}
