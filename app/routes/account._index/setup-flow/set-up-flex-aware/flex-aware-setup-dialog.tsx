import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useCallback } from 'react';
import { useSetupForm } from '../../resolver';
import { NotificationTest, useNotificationTestSubStep } from './notification-test';
import { InstallLocation, useInstallLocationSubStep } from './install-location';
import { RefineNotifications, useRefineNotificationsSubStep } from './refine-notifications';
import { FlexAwareSetupStepsProvider, FLEX_AWARE_SETUP_DIALOG } from './context';
import { ProgressStep, useProgressStepSubStep } from './progress-step';

export function FlexAwareSetupDialog() {
  const {
    formData: { currentDialog },
    commit,
  } = useSetupForm();

  const setOpen = useCallback(
    (toOpen: boolean) => commit({ currentDialog: toOpen ? FLEX_AWARE_SETUP_DIALOG : null }),
    [commit],
  );
  const close = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <Dialog open={currentDialog == FLEX_AWARE_SETUP_DIALOG} onOpenChange={setOpen}>
      <DialogContent className="flex h-screen flex-col gap-4 overflow-y-auto md:h-[90%] md:w-[800px] md:max-w-[90vw]">
        <FlexAwareSetupStepsProvider
          steps={[
            useNotificationTestSubStep(),
            useInstallLocationSubStep(),
            useRefineNotificationsSubStep(),
            useProgressStepSubStep(),
          ]}
          onComplete={close}
        >
          <NotificationTest />
          <InstallLocation />
          <RefineNotifications />
          <ProgressStep />
        </FlexAwareSetupStepsProvider>
      </DialogContent>
    </Dialog>
  );
}
