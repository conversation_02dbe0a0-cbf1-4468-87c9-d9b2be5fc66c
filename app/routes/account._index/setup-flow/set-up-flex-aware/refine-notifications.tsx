import { SetupSubStepState } from '../types';
import { SETUP_FLEX_AWARE_COMPLETE, SETUP_FLEX_AWARE_REFINE_NOTIFICATIONS_COMPLETE } from '../../constants';
import { FlexAwareSetupStep, useOpenFlexAwareSetupDialog } from './context';
import { useFlexAwareSetupSteps } from './context';
import { useAccountLoader } from '@/routes/account/hooks';
import { useSetupForm } from '../../resolver';
import { DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Check, ExternalLink } from 'lucide-react';
import { Link } from 'react-router';

const STEP_NAME = 'refine-notifications';

export function useRefineNotificationsSubStep(): SetupSubStepState {
  const { profile } = useAccountLoader();

  return {
    name: STEP_NAME,
    title: 'Refine Notifications',
    complete: !!profile.tags?.includes(SETUP_FLEX_AWARE_REFINE_NOTIFICATIONS_COMPLETE),
    prerequisites: true,
    onClick: useOpenFlexAwareSetupDialog(),
  };
}

export function RefineNotifications() {
  const { nextStep } = useFlexAwareSetupSteps();
  const { submit } = useSetupForm();
  const { systemType } = useAccountLoader();
  const href =
    systemType === 'business'
      ? 'https://support-business.allaware.com/hc/en-us/articles/**************-How-to-Set-up-Your-Flex-Aware-Part-3'
      : 'https://support.allaware.com/hc/en-us/articles/**************-How-to-Set-Up-Your-Flex-Aware-Part-3';

  return (
    <FlexAwareSetupStep name={STEP_NAME}>
      <h3 className="text-sm font-semibold text-primary">Set Up Flex Aware: 3 of 3</h3>
      <DialogTitle className="text-4xl font-bold">Refine Notifications</DialogTitle>
      <DialogDescription>
        <p className="mb-2">Flex Aware offers robust notification options to tailor alerts for your needs.</p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Notify others
        </p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Only notify me in the evenings
        </p>
        <p className="font-bold text-body-highlight">
          <Check className="inline size-4 text-emphasis" /> Tell me when it’s closed
        </p>
        <p className="mt-2">
          Follow this final guide to optimize your alerts and notifications. This step is optional.
        </p>
      </DialogDescription>

      <Button className="mt-4 w-full md:max-w-xs" asChild>
        <a href={href} target="_blank" rel="noreferrer">
          Refine Notifications <ExternalLink className="inline size-4" />
        </a>
      </Button>
      <Button
        className="mt-4 w-full max-w-xs"
        variant="outline"
        onClick={() => {
          submit({ action: 'tag', tags: [SETUP_FLEX_AWARE_REFINE_NOTIFICATIONS_COMPLETE] });
          nextStep();
        }}
      >
        <Check className="inline size-4" /> Mark as Complete
      </Button>
    </FlexAwareSetupStep>
  );
}
