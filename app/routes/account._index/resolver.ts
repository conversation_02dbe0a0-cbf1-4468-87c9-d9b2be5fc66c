import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  SETUP_ADD_USERS_COMPLETE,
  SETUP_NOTIFICATION_COMPLETE,
  SETUP_GEOFENCES_COMPLETE,
  SETUP_REPORTS_COMPLETE,
  SETUP_BONUS_COMPLETE,
  SETUP_ADJUST_DRIVER_BEHAVIOR_COMPLETE,
  SETUP_LOGIN_TO_DESKTOP_APP_COMPLETE,
  SETUP_COMPLETE_DISMISSED,
  SETUP_FLEET_DASHBOARD_EXPLORED_COMPLETE,
  SETUP_CONTACT_ADDED_COMPLETE,
  SETUP_APP_LOGIN_COMPLETE,
  SETUP_FLEX_AWARE_NOTIFICATION_COMPLETE,
  SETUP_FLEX_AWARE_INSTALL_LOCATION_COMPLETE,
  SETUP_FLEX_AWARE_REFINE_NOTIFICATIONS_COMPLETE,
  SETUP_FLEX_AWARE_COMPLETE,
  SETUP_FLEX_AWARE_CONNECTED_CABLE_INSTALL_LOCATION_COMPLETE,
  SETUP_FLEX_AWARE_CONNECTED_CABLE_REFINE_NOTIFICATIONS_COMPLETE,
  SETUP_FLEX_AWARE_CONNECTED_CABLE_COMPLETE,
  SETUP_FLEX_AWARE_CONNECTED_CABLE_NOTIFICATION_COMPLETE,
} from './constants';
import { createWizardFormContext } from '@/components/Wizard/ctx-factory';

const schema = z.discriminatedUnion('action', [
  z.object({
    action: z.literal('tag'),
    tags: z.array(
      z.enum([
        SETUP_ADD_USERS_COMPLETE,
        SETUP_NOTIFICATION_COMPLETE,
        SETUP_GEOFENCES_COMPLETE,
        SETUP_REPORTS_COMPLETE,
        SETUP_BONUS_COMPLETE,
        SETUP_ADJUST_DRIVER_BEHAVIOR_COMPLETE,
        SETUP_LOGIN_TO_DESKTOP_APP_COMPLETE,
        SETUP_FLEET_DASHBOARD_EXPLORED_COMPLETE,
        SETUP_CONTACT_ADDED_COMPLETE,
        SETUP_APP_LOGIN_COMPLETE,
        SETUP_COMPLETE_DISMISSED,
        SETUP_FLEX_AWARE_NOTIFICATION_COMPLETE,
        SETUP_FLEX_AWARE_INSTALL_LOCATION_COMPLETE,
        SETUP_FLEX_AWARE_REFINE_NOTIFICATIONS_COMPLETE,
        SETUP_FLEX_AWARE_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_NOTIFICATION_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_INSTALL_LOCATION_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_REFINE_NOTIFICATIONS_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_COMPLETE,
      ]),
    ),
  }),
  z.object({
    action: z.literal('removeTags'),
    tags: z.array(
      z.enum([
        SETUP_FLEX_AWARE_NOTIFICATION_COMPLETE,
        SETUP_FLEX_AWARE_INSTALL_LOCATION_COMPLETE,
        SETUP_FLEX_AWARE_REFINE_NOTIFICATIONS_COMPLETE,
        SETUP_FLEX_AWARE_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_NOTIFICATION_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_INSTALL_LOCATION_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_REFINE_NOTIFICATIONS_COMPLETE,
        SETUP_FLEX_AWARE_CONNECTED_CABLE_COMPLETE,
      ]),
    ),
  }),
]);
export type SchemaType = z.infer<typeof schema>;
export const resolver = zodResolver(schema);

export const { useWizardForm: useSetupForm, WizardFormProvider: SetupFormProvider } = createWizardFormContext<
  SchemaType & { currentDialog?: string | null }
>();
