import { ShopifyClient } from '@/business/clients/shopify-client';
import { VINRepo } from '@/business/core/repositories/vin';
import { type ActionFunctionArgs } from 'react-router';

export async function action({ request, context }: ActionFunctionArgs) {
  const { payload } = await context.resolve(ShopifyClient).authenticate.flow(request);

  if (payload.handle != 'decode-vehicle-vin') throw new Response('Incorrect flow action hit.', { status: 403 });

  const VIN: string = payload?.properties?.VIN;
  if (typeof VIN !== 'string') throw new Response('No VIN found.', { status: 400 });

  const result = await context.resolve(VINRepo).decodeVIN(VIN);

  return Response.json({
    return_value: result || {},
  });
}
