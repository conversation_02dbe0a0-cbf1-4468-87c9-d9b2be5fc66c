import { ShopifyClient } from '@/business/clients/shopify-client';
import { OrderRepo } from '@/business/core/repositories/order';
import { OrderService } from '@/business/core/services/order';
import { FeatureQuantities } from '@/business/core/types';
import { addQuantityToKey, convertIdToGid } from '@/lib/utils';
import { LoaderFunctionArgs } from 'react-router';

export async function loader({ context, params, request }: LoaderFunctionArgs) {
  await context.resolve(ShopifyClient).authenticate.admin(request);

  const { orderNumber } = params;
  const orderId = convertIdToGid('Order', orderNumber);

  if (!orderId) throw new Error('Missing order id!');

  const orderService = context.resolve(OrderService);
  const { orderDevices, ownerDevices } = await orderService.getOrderDevices(orderId);
  const setupState = await context.resolve(OrderRepo).getSetupState(orderId);

  if (!setupState) throw new Error('Setup state not found!');

  //Write the global number to each device
  Object.values(ownerDevices)
    .sort((a, b) => a.key.localeCompare(b.key))
    .reduce((qtys, device) => {
      qtys = addQuantityToKey(qtys, device.handle, 1);
      (device as any).number = qtys[device.handle] || 0;
      return qtys;
    }, {} as FeatureQuantities);

  const fulfillState = await orderService.tryFulfill(orderId, orderDevices, setupState);

  return { orderDevices, setupState, fulfillState };
}
