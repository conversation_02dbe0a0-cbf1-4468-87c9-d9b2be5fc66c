import { DictionaryTable } from '@/components/polaris/DictionaryTable';
import { Form } from '@/components/ui/form';
import { SYSTEM_FEATURES } from '@/business/core/constants/features';
import { DEVICE_FULFILL_ROW_STRATEGY_MAP } from './device-strategies';
import type { OrderDeviceFields, SystemDeviceHandle } from '@/business/core/types/device';
import { useLoaderData, useParams } from 'react-router';
import { useAppBridge } from '@shopify/app-bridge-react';
import { Banner, BlockStack, Card, Page, Text } from '@shopify/polaris';
import { OrderFulfilledIcon } from '@shopify/polaris-icons';
import { useEffect } from 'react';
import { useRemixForm } from 'remix-hook-form';
import { action } from './action';
import { loader } from './loader';
import { resolver } from './resolver';

export { loader, action };

export default function FulfillOrder() {
  const { orderNumber } = useParams();
  const { orderDevices, setupState, fulfillState } = useLoaderData<typeof loader>();
  const shopify = useAppBridge();

  const form = useRemixForm<OrderDeviceFields>({
    mode: 'onChange',
    resolver,
    values: orderDevices,
  });

  const loading = form.formState.isSubmitting || form.formState.isLoading;
  useEffect(() => shopify.loading(loading), [loading]);

  const categoryEntries = Object.entries(orderDevices);

  return (
    <Form form={form} data-save-bar data-discard-confirmation>
      <Page title="Fulfill order" fullWidth>
        <BlockStack gap="300">
          {setupState.fulfilled && (
            <Banner tone="success" title="Fulfilled!" icon={OrderFulfilledIcon}>
              Order has been fulfilled.
            </Banner>
          )}
          {!fulfillState.success && !!fulfillState.error.issues?.length && (
            <Banner tone="warning" title={`Next steps (${fulfillState.error.issues.length})`}>
              <Text as="p" variant="headingLg" fontWeight="bold">
                {fulfillState.error.issues[0].path.join('.') || 'root'}: {fulfillState.error.issues[0].message}
              </Text>
            </Banner>
          )}

          {categoryEntries.length ? (
            categoryEntries.map(([handle, devices]) => (
              <Card key={handle}>
                <Text as="h3" variant="headingMd">
                  {SYSTEM_FEATURES[handle as unknown as SystemDeviceHandle].name}
                </Text>
                <DictionaryTable
                  dictionaryRows={devices.map((device, index) => {
                    const row = {
                      '#': <span>{(device as any).number}</span>,
                      Nickname: <span>{device.nickname || '-'}</span>,
                      ...DEVICE_FULFILL_ROW_STRATEGY_MAP[handle as SystemDeviceHandle]({
                        path: `${handle}.${index}`,
                      }),
                    };
                    return row;
                  })}
                />
                {devices.map((_, index) => (
                  <input key={index} type="hidden" {...form.register(`${handle}.${index}.handle`)} value={handle} />
                ))}
              </Card>
            ))
          ) : (
            <Text as="p">No equipment to fulfill!</Text>
          )}
        </BlockStack>
      </Page>
    </Form>
  );
}
