import { ShopifyClient } from '@/business/clients/shopify-client';
import { OrderService } from '@/business/core/services/order';
import { OrderDeviceFields } from '@/business/core/types/device';
import { convertIdToGid } from '@/lib/utils';
import { ActionFunctionArgs } from 'react-router';
import { getValidatedFormData } from 'remix-hook-form';
import { resolver } from './resolver';

export async function action({ context, params, request }: ActionFunctionArgs) {
  await context.resolve(ShopifyClient).authenticate.admin(request);

  const { orderNumber } = params;
  const orderId = convertIdToGid('Order', orderNumber)!;

  const { data, errors } = await getValidatedFormData<OrderDeviceFields>(request, resolver);

  if (errors) return { errors };

  await context.resolve(OrderService).upsertAdminFields(orderId, data);

  return { errors: null };
}
