import { SystemDeviceHandle } from '@/business/core/types/device';
import { FormTextField, FormTextFieldProps } from '@/components/polaris/FormTextField';
import { KeyboardEventHandler, ReactNode, useCallback } from 'react';
import { useRemixFormContext } from 'remix-hook-form';

const ScannableTextField = ({ ...props }: FormTextFieldProps) => {
  const { setValue } = useRemixFormContext();
  // const scanQRCode = useCallback(
  //   (text: string) => {
  //     const SN = /SN=(\d+)/gm.exec(text)?.[1];
  //     const IMEI = /IMEI=(\d+)/gm.exec(text)?.[1];
  //     const MAC = /MAC=(\d+)/gm.exec(text)?.[1];

  //     if (!SN && !IMEI && !MAC) {
  //       setValue(`${props.path}.${props.name}`, text);
  //       return;
  //     }

  //     setValue(`${props.path}.serialNumber`, SN || '', { shouldDirty: true, shouldTouch: true });
  //     setValue(`${props.path}.IMEI`, IMEI || '', { shouldDirty: true, shouldTouch: true });
  //     setValue(`${props.path}.MAC`, MAC || '', { shouldDirty: true, shouldTouch: true });
  //     const pathParts = props.path?.split('.') || [];
  //     const nextPath = [...pathParts.slice(0, -1), parseInt(pathParts?.at(-1) || '0') + 1, props.name].join('.');
  //     document?.querySelector<HTMLInputElement>(`input[name="${nextPath}"]`)?.focus();
  //   },
  //   [props.path, props.name, setValue],
  // );

  const checkForQRCode = useCallback<KeyboardEventHandler<HTMLDivElement>>(
    e => {
      if (e.key !== 'Enter') return;

      const text = (e.target as HTMLInputElement)?.value || '';
      const SN = /SN=(\d+)/gm.exec(text)?.[1];
      const IMEI = /IMEI=(\d+)/gm.exec(text)?.[1];
      const MAC = /MAC=(\d+)/gm.exec(text)?.[1];

      if (!SN && !IMEI && !MAC) {
        // setValue(`${props.path}.${props.name}`, text);
        return;
      }

      setValue(`${props.path}.serialNumber`, SN || '', { shouldDirty: true, shouldTouch: true });
      setValue(`${props.path}.IMEI`, IMEI || '', { shouldDirty: true, shouldTouch: true });
      setValue(`${props.path}.MAC`, MAC || '', { shouldDirty: true, shouldTouch: true });
      const pathParts = props.path?.split('.') || [];
      const nextPath = [...pathParts.slice(0, -1), parseInt(pathParts?.at(-1) || '0') + 1, props.name].join('.');
      document?.querySelector<HTMLInputElement>(`input[name="${nextPath}"]`)?.focus();
    },
    [props.path, props.name, setValue],
  );
  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions
    <div onKeyDown={checkForQRCode}>
      <FormTextField {...props} selectTextOnFocus />
    </div>
  );
};

export const DEVICE_FULFILL_ROW_STRATEGY_MAP: {
  [T in SystemDeviceHandle]: (props: { path: string }) => { [header: string]: ReactNode };
} = {
  'car-tracker': props => ({
    'Serial number': <ScannableTextField name="serialNumber" {...props} />,
    IMEI: <ScannableTextField name="IMEI" {...props} />,
  }),
  'fleet-tracker': props => ({
    'Serial number': <ScannableTextField name="serialNumber" {...props} />,
    IMEI: <ScannableTextField name="IMEI" {...props} />,
  }),
  'flex-aware': props => ({
    'IMEI/SN': <ScannableTextField name="IMEI" {...props} />,
  }),
  hub: props => ({
    'Serial number': <ScannableTextField name="serialNumber" {...props} />,
  }),
  camera: props => ({
    MAC: <ScannableTextField name="MAC" {...props} />,
  }),
};
