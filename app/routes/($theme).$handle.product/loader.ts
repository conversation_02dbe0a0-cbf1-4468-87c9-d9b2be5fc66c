import { PERSONAL_MODEL, BUSINESS_MODEL } from '@/business/core/constants/config';
import DiscountRepo from '@/business/core/repositories/discount';
import { BuilderHandler } from '@/business/handlers/builder.handler';
import { CartHandler } from '@/business/handlers/cart.handler';
import { ProductHandler } from '@/business/handlers/product.handler';
import { type LoaderFunctionArgs } from 'react-router';

export async function loader({ params, context, request }: LoaderFunctionArgs) {
  const { theme, handle } = params;

  if (theme && theme != 'business' && theme != 'home') {
    throw new Response(null, { status: 404 });
  }

  const page = theme
    ? context
        .resolve(BuilderHandler)
        .handlePageFetch({
          request,
          model: theme == 'business' ? BUSINESS_MODEL : PERSONAL_MODEL,
          pathnamePrefix: '/' + theme,
        })
        .catch(error => {
          console.warn(error);
          return null;
        })
    : null;

  const product = await context.resolve(ProductHandler).getProduct(handle!);
  const discounts = context
    .resolve(DiscountRepo)
    .findDiscountsToAdvertise(product.advertisedDiscountSearchTerm?.value, product.id);
  const subscription = context.resolve(CartHandler).getSubscription(product.deviceHandle?.value, 1);

  return { product, discounts, subscription, page: page ? await page : null };
}

export type ProductLoaderData = Awaited<ReturnType<typeof loader>>;
export type ProductData = ProductLoaderData['product'];
export type SubscriptionPromise = ProductLoaderData['subscription'];
export type DiscountsPromise = ProductLoaderData['discounts'];
export type AdvertisableDiscount = Awaited<DiscountsPromise>[number];
