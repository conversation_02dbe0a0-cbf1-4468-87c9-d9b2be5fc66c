import { BUSINESS_MODEL, PERSONAL_MODEL } from '@/business/core/constants/config';
import Builder from '@/components/Builder';
import PageLayout from '@/components/PageLayout';
import ProductDataProvider from '@/components/ProductDataProvider';
import useTheme from '@/hooks/use-theme';
import { useLoaderData } from 'react-router';
import { loader } from './loader';
import { meta } from './meta';
import { HeadersFunction } from 'react-router';

export { loader, meta };

// export const headers: HeadersFunction = () =>
//   new Headers({
//     'Oxygen-Cache-Control': 'public, max-age=3600, s-maxage=7200, stale-while-revalidate=600',
//     Vary: 'Accept-Encoding, Sec-Ch-Ua-Mobile',
//   });

export default function Product() {
  const theme = useTheme();
  const { page } = useLoaderData<typeof loader>();

  return (
    <PageLayout>
      <ProductDataProvider>
        <Builder model={theme == 'business' ? BUSINESS_MODEL : PERSONAL_MODEL} content={page as any} />
      </ProductDataProvider>
    </PageLayout>
  );
}
