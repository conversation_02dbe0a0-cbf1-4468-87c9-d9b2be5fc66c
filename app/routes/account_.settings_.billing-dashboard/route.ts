import { getRedirectUrlFromRequest } from '@/lib/utils';
import { AccountHandler } from '@/business/handlers/account.handler';
import { LoaderFunctionArgs, redirect } from 'react-router';
import { SubscriptionRepo } from '@/business/core/repositories/subscription';
import { StripeClient } from '@/business/clients/stripe-client';

export async function loader({ context, request }: LoaderFunctionArgs) {
  const { ownerId, ownerRole } = await context.resolve(AccountHandler).validateOwnerAccess();

  if (ownerRole != 'write') throw new Response('Unauthorized', { status: 401 });

  const { searchParams } = new URL(request.url);
  const returnPathname = getRedirectUrlFromRequest(request, '/account/settings');

  const stripe = context.resolve(StripeClient);
  const subscriptionRepo = context.resolve(SubscriptionRepo);
  const env = context.resolve<Env>('env');

  const customer = await subscriptionRepo.getOrCreateStripeCustomer(ownerId);

  const session = await stripe.billingPortal.sessions.create({
    customer: customer.id,
    return_url: `${env.HOST}${returnPathname || ''}`,
  });

  const pathname = searchParams.get('target');
  return redirect(session.url + (pathname?.startsWith('/') ? pathname : ''));
}
