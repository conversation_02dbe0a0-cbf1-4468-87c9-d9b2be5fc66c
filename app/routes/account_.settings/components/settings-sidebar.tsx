import { SidebarContentBuilder, SidebarContentMenu } from '@/components/SidebarContentBuilder';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { useFetcher, useLoaderData, useNavigation, useSubmit } from 'react-router';
import {
  Building2,
  ChevronsUpDown,
  CircleDollarSign,
  Contact2,
  DollarSign,
  Loader2,
  LogOut,
  Package,
  User,
  UserCircle,
} from 'lucide-react';
import { useCallback } from 'react';
import { action } from '../action';
import { loader } from '../loader';

function LogoutMenuItem() {
  const submit = useSubmit();
  const { state } = useNavigation();

  const logout = useCallback(() => {
    submit(null, { action: '/account/logout', method: 'POST' });
  }, [submit]);

  return (
    <SidebarMenuButton onClick={logout} disabled={state == 'submitting'}>
      <LogOut />
      <span className="text-muted-foreground">Log out</span>
    </SidebarMenuButton>
  );
}

export const SETTINGS_MENU: SidebarContentMenu = {
  root: [
    {
      icon: Contact2,
      label: 'Contact',
      pathname: '/account/settings',
    },
    {
      icon: Package,
      label: 'Orders',
      external: true,
      pathname: '/account/settings/orders',
    },
    {
      icon: DollarSign,
      label: 'Billing',
      external: true,
      pathname: '/account/settings/billing-dashboard',
    },
    LogoutMenuItem,
  ],
};

function SystemSelector({ ownerId, systemOwners }: { ownerId: string; systemOwners: { id: string; name: string }[] }) {
  const { isMobile } = useSidebar();
  const fetcher = useFetcher<typeof action>();
  const switching = fetcher.state != 'idle';
  const selectedOwner = systemOwners.find(owner => owner.id == ownerId);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuButton size="lg" disabled={switching} className="mt-6 shadow-lg">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            {ownerId.startsWith('gid://shopify/Customer') ? (
              <User className="size-4" />
            ) : (
              <Building2 className="size-4" />
            )}
          </div>
          {selectedOwner?.name || '—'}
          {switching ? <Loader2 className="ml-auto animate-spin" /> : <ChevronsUpDown className="ml-auto" />}
        </SidebarMenuButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-(--radix-dropdown-menu-trigger-width) min-w-56"
        align="start"
        side={isMobile ? 'bottom' : 'right'}
        sideOffset={4}
      >
        {systemOwners.map(({ id, name }) => (
          <DropdownMenuItem key={id} className={cn('w-full', { 'bg-muted': id == ownerId })} asChild>
            <button onClick={() => fetcher.submit({ ownerId: id }, { method: 'POST' })}>
              {id.startsWith('gid://shopify/Customer') ? <User className="size-4" /> : <Building2 className="size-4" />}
              {name}
            </button>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function SettingsSidebar() {
  const { contacts, customer, ownerId } = useLoaderData<typeof loader>();

  return (
    <Sidebar className="bg-transparent" width="22rem">
      <SidebarHeader className="p-12">
        <h1 className="font-bold">Profile Settings</h1>
        <SidebarMenu>
          <SidebarMenuItem>
            <SystemSelector ownerId={ownerId} systemOwners={[customer, ...contacts.map(contact => contact.company)]} />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="px-12">
        <SidebarContentBuilder content={SETTINGS_MENU} />
      </SidebarContent>
    </Sidebar>
  );
}
