import PageInfiniteLoader from '@/components/PageInfiniteLoader';
import { Button } from '@/components/ui/button';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { Link, Outlet } from 'react-router';
import { X } from 'lucide-react';
import { action } from './action';
import { SettingsSidebar } from './components/settings-sidebar';
import { loader } from './loader';

export { action, loader };

export default function Settings() {
  const isMobile = useIsMobileBreakpoint();

  return (
    <>
      <PageInfiniteLoader />
      <SidebarProvider>
        <SettingsSidebar />
        <main className="w-full">
          {isMobile && <SidebarTrigger />}
          <section className={cn('relative mx-auto w-full max-w-(--breakpoint-sm) space-y-4 px-4 pb-12 md:py-12')}>
            <Outlet />
          </section>
          <Button variant="link" size="icon" className="fixed top-0 right-0" asChild>
            <Link to="/account" prefetch="intent">
              <X className="size-6!" />
            </Link>
          </Button>
        </main>
      </SidebarProvider>
    </>
  );
}
