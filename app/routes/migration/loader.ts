import { SessionClient } from '@/business/clients/session-client';
import { StripeClient } from '@/business/clients/stripe-client';
import { DeviceRepo } from '@/business/core/repositories/device';
import { SubscriptionRepo } from '@/business/core/repositories/subscription';
import { SystemRepo } from '@/business/core/repositories/system';
import { TagRepo } from '@/business/core/repositories/tag';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { convertGidToId } from '@/lib/utils';
import { LoaderFunctionArgs } from 'react-router';
import Stripe from 'stripe';

export async function loader({ context }: LoaderFunctionArgs) {
  const customerId = await context.resolve(AuthHandler).getLoggedInCustomerId({ redirectToLogin: true });

  const systems = await context.resolve(SystemRepo).getAllSystems(customerId);

  const env = context.resolve<Env>('env');

  const system = systems.find(system => system.requiresMigration);
  const devices = system
    ? (await context.resolve(DeviceRepo).getDevices(customerId))?.filter(device => device.systemKey == system.key)
    : [];

  let session: Stripe.Checkout.Session | undefined = undefined;
  let stripeCheckoutId: string | undefined = undefined;
  if (system?.requiresMigration && devices?.length) {
    const sessionClient = context.resolve(SessionClient);

    stripeCheckoutId = sessionClient.get('stripeCheckoutId');
    if (stripeCheckoutId) {
      session = await context
        .resolve(StripeClient)
        .checkout.sessions.retrieve(stripeCheckoutId)
        .catch(error => {
          console.log('Failed to get session', error);
          return undefined;
        });

      if (session?.status == 'complete') {
        console.log(`Migrating ${customerId}!`);
        system.requiresMigration = false;
        system.subscriptionId = session.subscription as string;
        console.log(`Saving system: ${JSON.stringify(system)}`);
        await context.resolve(SystemRepo).setSystem(system.ownerId, system.key, system);
        console.log('Shutting of old SMM subscription and contract!');
        await fetch(`https://smm.allaware.com/shop/migrate/${convertGidToId(customerId)}`, {
          method: 'POST',
          headers: {
            authorization: env.MULTIPASS_SECRET,
          },
        })
          .then(res => res.json())
          .catch(error => {
            console.error(
              `Error while migrating customer (${customerId})! Check SMM logs for more info. Stripe subscription was setup successfully: ${session?.subscription}`,
              error,
            );
            return undefined;
          });

        console.log(`Tagging customer as migrated!`);
        await context.resolve(TagRepo).addTags(customerId, ['MIGRATED_FROM_SMM']);
        console.log(`Migration complete!`);
      }

      if (session?.status != 'open') {
        stripeCheckoutId = undefined;
        sessionClient.unset('stripeCheckoutId');
      }
    }

    if (!stripeCheckoutId) {
      session = await context.resolve(SubscriptionRepo).createMigrationCheckoutSession({
        customerId,
        system,
        devices,
        returnUrl: `${env.HOST}/migration`,
      });

      stripeCheckoutId = session.id;
      context.resolve(SessionClient).set('stripeCheckoutId', session.id);
    }
  } else {
    context.resolve(SessionClient).unset('stripeCheckoutId');
  }

  return { system, devices, stripeKey: env.STRIPE_PUBLISHABLE_KEY, clientSecret: session?.client_secret };
}
