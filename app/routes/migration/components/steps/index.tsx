import { MigrationStepsProvider } from '../context';
import CompleteStep from './complete';
import SubscribeStep, { useSubscribeStep } from './subscribe';
import WelcomeStep, { useWelcomeStep } from './welcome';

export function MigrationFlowSteps() {
  return (
    <MigrationStepsProvider steps={[useWelcomeStep(), useSubscribeStep()]}>
      <WelcomeStep />
      <SubscribeStep />
      <CompleteStep />
    </MigrationStepsProvider>
  );
}
