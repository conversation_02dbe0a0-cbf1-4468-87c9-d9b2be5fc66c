import { useLoaderData } from 'react-router';
import { loader } from '../../loader';
import { MigrationStep } from '../context';
import { getStripe } from '@/hooks/stripe-client';
import { EmbeddedCheckout, EmbeddedCheckoutProvider } from '@stripe/react-stripe-js';

const STEP_NAME = 'subscribe';

export const useSubscribeStep = () => {
  const { system } = useLoaderData<typeof loader>();

  return {
    name: STEP_NAME,
    skip: !system?.requiresMigration,
  };
};

export default function SubscribeStep() {
  const { stripeKey, clientSecret } = useLoaderData<typeof loader>();
  const stripe = getStripe(stripeKey!);

  return (
    <MigrationStep name={STEP_NAME}>
      <h3 className="text-center text-sm font-semibold text-primary">Account Migration: 2 of 3</h3>
      <h1 className="my-2 text-center text-3xl font-bold">Add Your Payment Method</h1>
      <p className="mb-6 text-center text-lg text-muted-foreground">
        You will not be billed today. The payment method you add today will be used for your All Aware subscription
        going forward.
      </p>
      <EmbeddedCheckoutProvider options={{ clientSecret: clientSecret }} stripe={stripe}>
        <EmbeddedCheckout />
      </EmbeddedCheckoutProvider>
    </MigrationStep>
  );
}
