import { But<PERSON> } from '@/components/ui/button';
import { <PERSON> } from 'react-router';
import { MigrationStep } from '../context';

const STEP_NAME = 'complete';

export default function CompleteStep() {
  return (
    <MigrationStep name={STEP_NAME}>
      <h3 className="text-center text-sm font-semibold text-primary">Account Migration: Complete</h3>
      <h1 className="my-2 text-center text-3xl font-bold">Success!</h1>
      <p className="text-center text-lg text-muted-foreground">
        You are now on the new system and your service will continue seamlessly. Thank you for choosing All Aware to
        monitor your remote property and assets.
      </p>
      <Button variant="primary" size="full" className="mt-6" asChild>
        <Link to="/account" prefetch="intent">
          Go to New Dashboard
        </Link>
      </Button>
    </MigrationStep>
  );
}
