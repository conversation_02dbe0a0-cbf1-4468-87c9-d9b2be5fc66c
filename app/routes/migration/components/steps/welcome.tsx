import { useLoaderData } from 'react-router';
import { loader } from '../../loader';
import { MigrationStep, useMigrationSteps } from '../context';
import { generateImageSrcSet } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Check } from 'lucide-react';

const STEP_NAME = 'welcome';

export const useWelcomeStep = () => {
  const { system } = useLoaderData<typeof loader>();

  return {
    name: STEP_NAME,
    skip: !system?.requiresMigration,
  };
};

export default function WelcomeStep() {
  const { nextStep } = useMigrationSteps();
  const dashboardImage = 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/account-portal.png?v=**********';
  const srcSet = generateImageSrcSet(dashboardImage, [100, 250, 500]);

  return (
    <MigrationStep name={STEP_NAME}>
      <h3 className="text-center text-sm font-semibold text-primary">Account Migration: 1 of 3</h3>
      <h1 className="my-2 text-center text-3xl font-bold">Introducing Our New System!</h1>
      <p className="mb-6 text-center text-lg text-muted-foreground">
        We are migrating billing systems. It only takes 60 seconds to complete your account on the new system.
      </p>
      <img
        src={dashboardImage}
        srcSet={srcSet}
        sizes="(max-width: 500px) 100vw, 500px"
        alt="Account Portal"
        className="w-full rounded-md"
      />
      <Button variant="primary" size="full" className="my-6" onClick={nextStep}>
        Get Started
      </Button>
      <Separator className="my-6" />
      <h2 className="mb-2 text-xl font-bold">Things to Note:</h2>
      <ul className="space-y-1 text-lg text-muted-foreground">
        <li>
          <Check className="mr-1 inline-block size-6 text-primary" /> You will <b>not</b> be billed today. Your
          subscription amount and date will remain as before.
        </li>
        <li>
          <Check className="mr-1 inline-block text-primary" /> This only affects your normal subscription processing.
          Your All Aware app login name and password <b>will remain the same.</b>
        </li>
        <li>
          <Check className="mr-1 inline-block text-primary" /> This step is required for all existing customers.
        </li>
      </ul>
      <p className="text-md mt-6 text-muted-foreground">
        Have questions? Contact support at{' '}
        <a href="mailto:<EMAIL>" className="underline">
          <EMAIL>
        </a>
      </p>
    </MigrationStep>
  );
}
