import { VINService } from '@/business/core/services/vin';
import { LicensePlate } from '@/business/core/types/vin';
import { ActionFunctionArgs } from 'react-router';
import { type FileUpload, parseFormData } from '@mjackson/form-data-parser';

export async function action({ context, request }: ActionFunctionArgs) {
  const data = await parseFormData(request, {
    maxFileSize: 1000000,
  });

  const vinService = context.resolve(VINService);

  const file = data.get('image') as File;

  if (file) {
    return await vinService.identifyVINsFromImage(file);
  }

  const plateStr = data.get('plate') as string;
  if (!plateStr) {
    throw new Response('Missing body', { status: 400 });
  }

  const plate = JSON.parse(plateStr) as LicensePlate;
  const result = await vinService.identifyVINFromPlate(plate);

  return { results: [result] };
}
