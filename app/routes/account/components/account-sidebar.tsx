import { CustomerSystemInfo } from '@/business/handlers/types/system';
import { SidebarContentBuilder } from '@/components/SidebarContentBuilder';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { SETTINGS_MENU } from '@/routes/account_.settings/components/settings-sidebar';
import { Await, useFetcher, useLoaderData } from 'react-router';
import { Building2, ChevronsUpDown, CreditCard, Home, LayoutDashboard, Loader2, UserCircle, Zap } from 'lucide-react';
import { Fragment, Suspense, useMemo } from 'react';
import { action } from '../action';
import { loader } from '../loader';
import AppIcon from '/images/app-launch-icon.png?url';

function SystemSelector({ systemKey, systems }: { systemKey: string | undefined; systems: CustomerSystemInfo[] }) {
  const isLoading = systemKey == 'loading';

  const { isMobile } = useSidebar();
  const fetcher = useFetcher<typeof action>({ key: 'system-selector' });
  const switching = fetcher.state != 'idle';
  const selectedSystem = systems.find(system => system.key == systemKey);

  const labeledSystems = useMemo(() => {
    const labeled: { [label: string]: CustomerSystemInfo[] } = {};

    for (const system of systems) {
      let label = system.ownerName;
      if (system.locationName && system.locationName != label) {
        label = label + ' — ' + system.locationName;
      }

      if (labeled[label]?.length) labeled[label].push(system);
      else labeled[label] = [system];
    }
    return Object.entries(labeled);
  }, [isLoading, systems]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger disabled={!systemKey} asChild>
        <SidebarMenuButton size="lg" disabled={switching || isLoading} className="mt-6 shadow-lg">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            {selectedSystem?.systemType == 'business' ? <Building2 className="size-4" /> : <Home className="size-4" />}
          </div>
          {selectedSystem?.systemName || '—'}
          {switching || isLoading ? (
            <Loader2 className="ml-auto animate-spin" />
          ) : (
            <ChevronsUpDown className="ml-auto" />
          )}
        </SidebarMenuButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" side={isMobile ? 'bottom' : 'right'} sideOffset={4}>
        <DropdownMenuGroup>
          {isLoading && <Loader2 className="mx-auto my-2 size-8 animate-spin" />}
          {labeledSystems.map(([label, systems]) => (
            <Fragment key={label}>
              <DropdownMenuLabel>{label}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {systems.map(sys => (
                <DropdownMenuItem
                  key={sys.key}
                  className={cn('w-full', { 'bg-sidebar-accent': sys.key == systemKey })}
                  asChild
                >
                  <button onClick={() => fetcher.submit({ systemKey: sys.key }, { method: 'POST' })}>
                    {sys.systemName}
                  </button>
                </DropdownMenuItem>
              ))}
            </Fragment>
          ))}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function ProfileDisplay() {
  const { profile } = useLoaderData<typeof loader>();

  const { displayName, firstName, lastName, email } = profile;

  const initials = (firstName && lastName ? firstName[0] + lastName[0] : displayName[0] + displayName[1]).toUpperCase();

  return (
    <div className="flex items-center gap-2 p-2">
      <Avatar className="h-8 w-8 rounded-lg">
        <AvatarFallback className="rounded-lg">{initials}</AvatarFallback>
      </Avatar>
      <div className="grid flex-1 text-left text-sm leading-tight">
        <span className="truncate font-semibold">{displayName}</span>
        {displayName != email && <span className="truncate text-xs">{email}</span>}
      </div>
    </div>
  );
}

export function AccountSidebar() {
  const { systemKey, systems, subscription } = useLoaderData<typeof loader>();

  return (
    <Sidebar width="22rem">
      <SidebarHeader className="p-12">
        <h1 className="font-bold">
          All Aware
          <br />
          Account Manager
        </h1>
        <SidebarMenu>
          <SidebarMenuItem>
            <Suspense fallback={<SystemSelector systemKey="loading" systems={[]} />}>
              <Await resolve={systems}>{systems => <SystemSelector systemKey={systemKey} systems={systems} />}</Await>
            </Suspense>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent
        className={cn('px-12', {
          ['pointer-events-none opacity-50']: !subscription?.id,
        })}
      >
        {!!systemKey && (
          <SidebarContentBuilder
            content={{
              root: [
                {
                  label: 'Dashboard',
                  icon: LayoutDashboard,
                  pathname: '/account',
                },
                {
                  label: 'Devices',
                  icon: Zap,
                  pathname: '/account/devices',
                },
                {
                  label: 'Subscription',
                  icon: CreditCard,
                  pathname: '/account/subscription',
                  subItems: [
                    {
                      label: 'Overview',
                      pathname: '/account/subscription',
                    },
                    {
                      label: 'Invoice history',
                      pathname: '/account/subscription/invoice-history',
                    },
                    {
                      label: 'Payment method',
                      external: true,
                      pathname: `/account/settings/billing-dashboard?${new URLSearchParams({
                        return_to: '/account/subscription',
                        target: `/subscriptions/${subscription?.id}/update-payment-method/changePaymentMethodFromHome`,
                      }).toString()}`,
                    },
                  ],
                },
                {
                  label: 'App Users',
                  icon: UserCircle,
                  pathname: '/account/users',
                },
              ],
              'Switch to the All Aware App': [
                {
                  label: 'All Aware App',
                  icon: () => <img src={AppIcon} alt="All Aware App Logo" className="size-6" />,
                  pathname: '/app-login',
                  external: true,
                },
              ],
            }}
          />
        )}
      </SidebarContent>
      <SidebarFooter className="px-12">
        <SidebarContentBuilder content={SETTINGS_MENU} secondary />
        <Separator className="mt-4" />
        <ProfileDisplay />
      </SidebarFooter>
    </Sidebar>
  );
}
