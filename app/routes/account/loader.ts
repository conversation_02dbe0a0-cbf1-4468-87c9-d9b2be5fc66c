import { SubscriptionRepo } from '@/business/core/repositories/subscription';
import { SystemRole } from '@/business/core/types';
import { AccountHandler } from '@/business/handlers/account.handler';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { MigrationHandler } from '@/business/handlers/migration.handler';
import { getSystemOwnerType } from '@/lib/utils';
import { LoaderFunctionArgs, redirect } from 'react-router';
import Stripe from 'stripe';

export async function loader({ context, request }: LoaderFunctionArgs) {
  const requiresMigrationCheck = context.resolve(MigrationHandler).checkIfCustomerNeedsToMigrate();
  const customerId = await context.resolve(AuthHandler).getLoggedInCustomerId({ redirectToLogin: true });

  const handler = context.resolve(AccountHandler);
  const subscriptionRepo = context.resolve(SubscriptionRepo);

  const profileQuery = handler.getCustomerProfile(customerId);

  const systems = handler.getCustomerSystems(customerId);
  const access = await handler.customerHasAccessToSystem(customerId);

  let ownerId: string | undefined = undefined;
  let subscription: Stripe.Subscription | undefined = undefined;
  let systemRole: SystemRole = 'read';
  let systemKey: string | undefined = undefined;

  if (!access.hasAccess) {
    const systemsArray = await systems;
    const selectedSystem = handler.getSelectedOrFirstAvailableSystem(systemsArray);

    if (selectedSystem) {
      handler.setAccountSession({ ownerId: selectedSystem.ownerId, systemKey: selectedSystem.key });

      const { pathname, search, hash } = new URL(request.url);
      throw redirect(pathname + search + hash);
    } else {
      ownerId = customerId;
      systemKey = undefined;
      systemRole = 'read';

      handler.setAccountSession({ ownerId: customerId });
    }
  } else {
    ownerId = access.system.ownerId;
    systemKey = access.system.key;
    subscription = access.system.subscriptionId
      ? await subscriptionRepo.getSystemSubscription(access.system.subscriptionId)
      : undefined;
    systemRole = access.role;
  }

  const systemType = getSystemOwnerType(ownerId!);
  const devices = handler.getSystemDevices(ownerId, systemKey);
  const { features, scheduledFeatures } = subscription
    ? await subscriptionRepo.getCurrentAndScheduledFeatureQuantities(subscription)
    : { features: {}, scheduledFeatures: {} };

  await requiresMigrationCheck;
  return {
    profile: (await profileQuery)!,
    systemKey,
    systemType,
    systemRole,
    subscription,
    features,
    scheduledFeatures,
    systems,
    devices,
  };
}
