import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { Outlet, useFetcher } from 'react-router';
import { action } from './action';
import { AccountSidebar } from './components/account-sidebar';
import { loader } from './loader';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { useIsMounted } from '@/hooks/use-mounted';
import PageInfiniteLoader from '@/components/PageInfiniteLoader';

export { action, loader };

const MOBILE_BREAKPOINT = 768;

export default function AccountLayout() {
  const isMobile = useIsMobileBreakpoint(MOBILE_BREAKPOINT);
  const [animateRef] = useAutoAnimate();
  const isMounted = useIsMounted();
  const fetcher = useFetcher({ key: 'setup' });

  return (
    <>
      <PageInfiniteLoader navigating={fetcher.state != 'idle'} />
      <SidebarProvider breakpoint={MOBILE_BREAKPOINT}>
        <AccountSidebar />
        <main className="w-full">
          {isMobile && <SidebarTrigger />}
          <section
            ref={isMounted ? animateRef : undefined}
            className={cn('relative mx-auto w-full max-w-(--breakpoint-md) space-y-4 p-4')}
          >
            <Outlet />
          </section>
        </main>
        {/* <ChatLauncher /> */}
      </SidebarProvider>
    </>
  );
}
