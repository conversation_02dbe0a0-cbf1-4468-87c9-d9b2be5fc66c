import { AccountHandler } from '@/business/handlers/account.handler';
import { ActionFunctionArgs } from 'react-router';

export async function action({ context, request }: ActionFunctionArgs) {
  const formData = await request.formData();

  const handler = context.resolve(AccountHandler);
  const systemKey = formData.get('systemKey')?.toString();
  if (systemKey) handler.setAccountSession({ systemKey });

  return null;
}
