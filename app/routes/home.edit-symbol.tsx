import { BuilderHandler } from '@/business/handlers/builder.handler';
import Builder from '@/components/Builder';
import { useLoaderData } from 'react-router';
import { LoaderFunctionArgs } from 'react-router';

export const loader = async ({ request, context }: LoaderFunctionArgs) => {
  const builderHandler = context.resolve(BuilderHandler);

  const page = await builderHandler.handlePageFetch({
    request,
    model: 'symbol',
    pathnamePrefix: '/home',
  });

  return { page };
};

export default function Page() {
  const { page } = useLoaderData<typeof loader>();

  return (
    <div>
      <h1 className="block text-center text-purple-900">Edit Symbol Page</h1>
      <hr></hr>
      <Builder content={page as any} model="symbol" />
    </div>
  );
}
