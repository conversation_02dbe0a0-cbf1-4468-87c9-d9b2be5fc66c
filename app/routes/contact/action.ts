import { ActionFunctionArgs, json } from 'react-router';
import { getValidatedFormData } from 'remix-hook-form';
import { SchemaType, resolver } from './resolver';
import { SendgridClient } from '@/business/clients/sendgrid-client';

export async function action({ request, context }: ActionFunctionArgs) {
  const { data, errors } = await getValidatedFormData<SchemaType>(request, resolver);
  const emailClient = context.resolve(SendgridClient);

  if (errors) {
    return { success: false, errors };
  }

  try {
    await emailClient.sendRaw('<EMAIL>', {
      subject: 'Contact Form Submission',
      text: `
        Name: ${data.firstName} ${data.lastName}
        Email: ${data.email}
        Message: ${data.message}
      `,
    });
    return { success: true };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      error: 'Failed to send message. Please try again.',
    };
  }
}
