import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

export const schema = z
  .object({
    firstName: z.string().min(1, { message: '*Missing first name' }),
    lastName: z.string().min(1, { message: '*Missing last name' }),
    email: z.string().email({ message: '*Invalid email address' }),
    message: z.string().min(1, { message: '*Missing message' }),
  })
  .required();
export const resolver = zodResolver(schema);
export type SchemaType = z.infer<typeof schema>;
