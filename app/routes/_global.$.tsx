import Builder from '@/components/Builder';
import { GLOBAL_MODEL } from '@/business/core/constants/config';
import { generatePageTitle } from '@/lib/utils';
import { BuilderHandler } from '@/business/handlers/builder.handler';
import { MetaFunction, useLoaderData } from 'react-router';
import { LoaderFunctionArgs } from 'react-router';

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [{ title: generatePageTitle(data?.page?.data?.title) }];
};

export const loader = async ({ request, context }: LoaderFunctionArgs) => {
  const builderHandler = context.resolve(BuilderHandler);

  const page = await builderHandler.handlePageFetch({ request, model: GLOBAL_MODEL, pathnamePrefix: '' });

  return { page };
};

// Define and render the page.
export default function Page() {
  // Use the useLoaderData hook to get the Page data from `loader` above.
  const { page } = useLoaderData<typeof loader>();

  // Render the page content from Builder.io
  return <Builder model={GLOBAL_MODEL} content={page as any} />;
}
