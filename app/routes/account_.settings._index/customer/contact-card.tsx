import { useRemixForm } from 'remix-hook-form';
import { SerializedContactInfo } from '../loader';
import { type SchemaType, resolver } from '../resolver';
import { Form } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import TextField from '@/components/TextField';
import { Button } from '@/components/ui/button';

export function CustomerContactCard({ info }: { info: SerializedContactInfo & { __typename: 'Customer' } }) {
  const form = useRemixForm<SchemaType>({
    mode: 'onBlur',
    resolver,
    values: {
      type: 'customer',
      firstName: info.firstName || undefined,
      lastName: info.lastName || undefined,
      phone: info.phone || undefined,
    },
  });

  return (
    <Form form={form}>
      <Card>
        <CardHeader>
          <CardTitle>Contact</CardTitle>
          <CardDescription>
            You&apos;re logged into <b>{info.email}</b>. Update your contact information below.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <TextField label="First name" name="firstName" />
            <TextField label="Last name" name="lastName" />
          </div>
          <TextField label="Phone" name="phone" />
        </CardContent>
        <CardFooter className="justify-end">
          <Button
            type="submit"
            variant="primary"
            disabled={!form.formState.isDirty}
            loading={form.formState.isSubmitting}
            className="w-32"
          >
            Save
          </Button>
        </CardFooter>
      </Card>
    </Form>
  );
}
