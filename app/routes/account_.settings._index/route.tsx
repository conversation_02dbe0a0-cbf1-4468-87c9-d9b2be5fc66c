import { Await, useLoaderData } from 'react-router';

import { action } from './action';
import { CompanyContactCard } from './company/contact-card';
import { CustomerAddressesCard } from './customer/addresses-card';
import { CustomerContactCard } from './customer/contact-card';
import { loader } from './loader';
import { CompanyLocationsCard } from './company/locations-card';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
export { action, loader };

export default function ContactPage() {
  const { info, locations } = useLoaderData<typeof loader>();

  if (info.__typename == 'Customer')
    return (
      <>
        <CustomerContactCard info={info} />
        <CustomerAddressesCard addresses={info.addresses} defaultAddressId={info?.defaultAddress?.id} />
      </>
    );
  else if (info.__typename == 'Company')
    return (
      <>
        <CompanyContactCard info={info} />
        <Suspense fallback={<Skeleton className="h-48 w-full" />}>
          <Await resolve={locations}>{locations => <CompanyLocationsCard locations={locations || []} />}</Await>
        </Suspense>
      </>
    );
  return null;
}
