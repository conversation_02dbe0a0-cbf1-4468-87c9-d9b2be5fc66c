import { CompanyRepo } from '@/business/core/repositories/company';
import { CustomerRepo } from '@/business/core/repositories/customer';
import { ActionFunctionArgs, json, TypedResponse } from 'react-router';
import { getValidatedFormData } from 'remix-hook-form';
import { resolver, type SchemaType } from './resolver';
import { CompanyAddressType, CountryCode } from 'admin.types';
import { LOCATION_ADMIN_ROLE } from '@/business/core/constants/company';
import { FieldErrors } from 'react-hook-form';
import { AccountHandler } from '@/business/handlers/account.handler';

export async function action({ context, request }: ActionFunctionArgs): Promise<{ errors?: FieldErrors<SchemaType> }> {
  const { ownerId, contacts } = await context.resolve(AccountHandler).validateOwnerAccess();

  const { data, errors } = await getValidatedFormData<SchemaType>(request, resolver);

  if (errors) return { errors };

  const customerRepo = context.resolve(CustomerRepo);
  const companyRepo = context.resolve(CompanyRepo);
  try {
    const selectedContact = contacts.find(contact => contact.company.id == ownerId);
    if (data.type == 'company') {
      if (!selectedContact?.isMainContact) {
        throw new Response('Unauthorized request', { status: 401 });
      }
      if (data.name) await companyRepo.updateCompany(ownerId, { name: data.name });
      if (data.mainContactId && data.mainContactId != selectedContact.id)
        await companyRepo.updateMainContact(ownerId, data.mainContactId);
    } else if (
      data.type == 'updateLocationDetails' ||
      data.type == 'updateLocationAddress' ||
      data.type == 'updateLocationContacts' ||
      data.type == 'revokeContactRole' ||
      data.type == 'inviteContact'
    ) {
      if (
        !selectedContact?.isMainContact &&
        (await customerRepo.getContactLocationAssignment(selectedContact?.id!, data.id))?.role?.name !=
          LOCATION_ADMIN_ROLE
      ) {
        throw new Response('Unauthorized request', { status: 401 });
      }

      if (data.type == 'updateLocationDetails') {
        await companyRepo.updateCompanyLocation(data.id, {
          name: data.name,
          phone: data.phone,
        });
      } else if (data.type == 'updateLocationAddress') {
        const assignTo: CompanyAddressType[] = [];
        if (data.shipping) assignTo.push(CompanyAddressType.SHIPPING);
        if (data.billing) assignTo.push(CompanyAddressType.BILLING);
        if (!assignTo.length) throw new Error('Must assign address to either billing or shipping.');
        await companyRepo.assignCompanyLocationAddress(
          data.id,
          {
            address1: data.address1 || '',
            address2: data.address2 || '',
            city: data.city || '',
            countryCode: CountryCode.US,
            firstName: data.firstName || '',
            lastName: data.lastName || '',
            zip: data.zip || '',
            recipient: data.recipient || '',
          },
          assignTo,
        );
      } else if (data.type == 'updateLocationContacts' || data.type == 'inviteContact') {
        const roles = await companyRepo.getCompanyRoles(ownerId);
        const roleId = data.assignRole == 'write' ? roles.adminRole?.id : roles.readRole?.id;

        if (!roleId) throw new Error('Missing contact role for company...');

        if (data.type == 'updateLocationContacts') {
          await companyRepo.assignContactsRole(data.id, data.assignContactIds, roleId);
        } else if (data.type == 'inviteContact') {
          await companyRepo.inviteContact(data.id, data.email, roleId);
        }
      } else if (data.type == 'revokeContactRole') {
        await companyRepo.revokeContactsRole(data.id, data.revokeContactIds, true);
      }
    } else if (data.type == 'customer') {
      await customerRepo.updateCustomer({
        id: ownerId,
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        phone: data.phone || '',
      });
    } else if (data.type == 'address') {
      const addressId = await customerRepo.updateAddress(ownerId, data);
      if (data.makeDefault) await customerRepo.setDefaultAddress(ownerId, addressId);
    } else if (data.type == 'removeAddress') {
      await customerRepo.updateAddress(ownerId, { id: data.id, delete: true });
    }
  } catch (error) {
    console.error(error);
    return {
      errors: {
        root: {
          message: 'There was an unexpected error. Please try again. If this issue persists, please contact support!',
        } as any,
      },
    };
  }

  return {};
}
