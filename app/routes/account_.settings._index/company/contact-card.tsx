import { SelectField } from '@/components/SelectField';
import TextField from '@/components/TextField';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { Await, useLoaderData } from 'react-router';
import { useRemixForm } from 'remix-hook-form';
import { loader, SerializedContactInfo, SerializedContactLocation } from '../loader';
import { type SchemaType, resolver } from '../resolver';
import { Suspense, useMemo } from 'react';

function PrimaryContactSelector({ locations }: { locations: SerializedContactLocation[] }) {
  const { isPrimaryOwner, info } = useLoaderData<typeof loader>();

  const companyInfo = info as SerializedContactInfo & { __typename: 'Company' };
  const values = useMemo(() => {
    const values = companyInfo.mainContact
      ? [
          {
            label: companyInfo.mainContact.customer.displayName,
            value: companyInfo.mainContact.id,
          },
        ]
      : [];

    const assignments = locations.flatMap(l => l.assignments);
    for (const assignment of assignments) {
      if (values.some(v => v.value == assignment.companyContact.id)) continue;

      values.push({
        label: assignment.companyContact.customer.displayName,
        value: assignment.companyContact.id,
      });
    }
    return values;
  }, [companyInfo.mainContact, locations]);

  for (const location of locations) {
    const assignments = location.assignments;
    for (const assignment of assignments) {
      if (values.some(v => v.value == assignment.companyContact.id)) continue;

      values.push({
        label: assignment.companyContact.customer.displayName,
        value: assignment.companyContact.id,
      });
    }
  }

  return (
    <SelectField
      disabled={!isPrimaryOwner}
      name="mainContactId"
      label="Primary contact"
      placeholder="No primary contact"
      // TODO: Add contacts card
      values={values}
    />
  );
}

export function CompanyContactCard({ info }: { info: SerializedContactInfo & { __typename: 'Company' } }) {
  const form = useRemixForm<SchemaType>({
    mode: 'onBlur',
    resolver,
    values: {
      type: 'company',
      name: info.name,
      mainContactId: info.mainContact?.id,
    },
  });

  const { isPrimaryOwner, locations } = useLoaderData<typeof loader>();

  return (
    <Form form={form}>
      <Card>
        <CardHeader>
          <CardTitle>Contact</CardTitle>
          <CardDescription>Update your company&apos;s contact information below.</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <TextField disabled={!isPrimaryOwner} label="Company name" name="name" />
          <Suspense fallback={<PrimaryContactSelector locations={[]} />}>
            <Await resolve={locations}>{locations => <PrimaryContactSelector locations={locations!} />}</Await>
          </Suspense>
        </CardContent>
        <CardFooter className="justify-end">
          <Button
            type="submit"
            variant="primary"
            disabled={!isPrimaryOwner || !form.formState.isDirty}
            loading={form.formState.isSubmitting}
            className="w-32"
          >
            Save
          </Button>
        </CardFooter>
      </Card>
    </Form>
  );
}
