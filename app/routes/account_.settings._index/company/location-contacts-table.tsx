import { <PERSON><PERSON>ield } from '@/components/SelectField';
import Text<PERSON>ield from '@/components/TextField';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Form, useFormOnSubmit } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { LOCATION_ADMIN_ROLE, LOCATION_READ_ROLE } from '@/business/core/constants/company';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { CheckedState } from '@radix-ui/react-checkbox';
import { ChevronDown, Mail } from 'lucide-react';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { useRemixForm } from 'remix-hook-form';
import { SerializedContactLocation } from '../loader';
import { resolver, SchemaType } from '../resolver';

function InviteContact({ children, locationId }: { children?: ReactNode; locationId: string }) {
  const [open, setOpen] = useState(false);
  const initialValues: SchemaType = {
    type: 'inviteContact',
    id: locationId,
    email: '',
    assignRole: 'read',
  };
  const form = useRemixForm<SchemaType>({
    resolver,
    values: initialValues,
  });
  useEffect(() => {
    if (open) form.reset(initialValues);
  }, [open]);
  useFormOnSubmit(form, { onSuccess: () => setOpen(false) });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger
        onClick={event => {
          event.preventDefault();
          setOpen(true);
        }}
        asChild
      >
        {children}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Invite contact</DialogTitle>
          <DialogDescription>
            You are inviting a contact to the selected location. Enter the email address of the contact and the role,
            and then click &quot;Invite&quot; to confirm.
          </DialogDescription>
        </DialogHeader>
        <Form form={form} className="flex flex-col gap-4">
          <TextField name="email" label="Email" />
          <SelectField
            name="assignRole"
            label="Role"
            values={[
              {
                label: LOCATION_ADMIN_ROLE,
                value: 'write',
              },
              {
                label: LOCATION_READ_ROLE,
                value: 'read',
              },
            ]}
          />
          <DialogFooter>
            <Button type="submit" variant="primary" loading={form.formState.isSubmitting} className="min-w-32">
              Invite <Mail className="size-4" />
            </Button>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function ChangeContactRole({
  children,
  locationId,
  selectedIds,
}: {
  children?: ReactNode;
  locationId: string;
  selectedIds: string[];
}) {
  const [open, setOpen] = useState(false);
  const initialValues: SchemaType = {
    type: 'updateLocationContacts',
    id: locationId,
    assignContactIds: selectedIds,
    assignRole: 'write',
  };
  const form = useRemixForm<SchemaType>({
    resolver,
    values: initialValues,
  });
  useEffect(() => {
    if (open) form.reset(initialValues);
  }, [open]);
  useFormOnSubmit(form, { onSuccess: () => setOpen(false) });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger
        onClick={event => {
          event.preventDefault();
          setOpen(true);
        }}
        asChild
      >
        {children}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update contact role</DialogTitle>
          <DialogDescription>
            You are updating {selectedIds.length} contact(s). Select the role you would like, and then click
            &quot;Update&quot; to confirm.
          </DialogDescription>
        </DialogHeader>
        <Form form={form} className="flex flex-col gap-4">
          <SelectField
            name="assignRole"
            label="Role"
            values={[
              {
                label: LOCATION_ADMIN_ROLE,
                value: 'write',
              },
              {
                label: LOCATION_READ_ROLE,
                value: 'read',
              },
            ]}
          />
          <DialogFooter>
            <Button type="submit" variant="primary" loading={form.formState.isSubmitting} className="min-w-32">
              Update
            </Button>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function RevokeContactRole({
  children,
  locationId,
  selectedIds,
}: {
  children?: ReactNode;
  locationId: string;
  selectedIds: string[];
}) {
  const [open, setOpen] = useState(false);
  const initialValues: SchemaType = {
    type: 'revokeContactRole',
    id: locationId,
    revokeContactIds: selectedIds,
  };
  const form = useRemixForm<SchemaType>({
    resolver,
    values: initialValues,
  });
  useEffect(() => {
    if (open) form.reset(initialValues);
  }, [open]);
  useFormOnSubmit(form, { onSuccess: () => setOpen(false) });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger
        onClick={event => {
          event.preventDefault();
          setOpen(true);
        }}
        asChild
      >
        {children}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Revoke contact role</DialogTitle>
          <DialogDescription>
            You are revoking {selectedIds.length} contact(s) from the selected location. Click &quot;Revoke&quot; to
            confirm.
          </DialogDescription>
        </DialogHeader>
        <Form form={form} className="flex flex-col gap-4">
          <DialogFooter>
            <Button type="submit" variant="destructive" loading={form.formState.isSubmitting} className="min-w-32">
              Revoke
            </Button>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function LocationActions({
  location,
  selectedIds,
  children,
}: {
  location: SerializedContactLocation;
  selectedIds: string[];
  children?: ReactNode;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent>
        <InviteContact locationId={location.info.id}>
          <DropdownMenuItem>Invite contact</DropdownMenuItem>
        </InviteContact>
        <ChangeContactRole locationId={location.info.id} selectedIds={selectedIds}>
          <DropdownMenuItem disabled={!selectedIds?.length}>Change role(s)</DropdownMenuItem>
        </ChangeContactRole>
        <RevokeContactRole locationId={location.info.id} selectedIds={selectedIds}>
          <DropdownMenuItem disabled={!selectedIds?.length}>Remove contact(s)</DropdownMenuItem>
        </RevokeContactRole>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function LocationContactsTable({ location }: { location: SerializedContactLocation }) {
  const [search, setSearch] = useState('');
  const searchSanitized = search.trim().toUpperCase();
  const searchResults = useMemo(
    () =>
      location?.assignments?.filter(
        assignment =>
          !searchSanitized ||
          assignment.companyContact.customer.firstName?.toUpperCase()?.includes(searchSanitized) ||
          assignment.companyContact.customer.lastName?.toUpperCase()?.includes(searchSanitized) ||
          assignment.companyContact.customer.email?.toUpperCase()?.includes(searchSanitized) ||
          assignment.role.name?.toUpperCase()?.includes(searchSanitized),
      ),
    [searchSanitized, location?.assignments],
  );

  const isMobile = useIsMobileBreakpoint();
  const [selected, setSelected] = useState<string[]>([]);

  useEffect(() => {
    setSelected(selected.filter(id => location?.assignments?.some(assignment => assignment.companyContact.id == id)));
  }, [location?.assignments]);

  const allSelected = useMemo(() => {
    const notInSelection =
      !searchResults.length || searchResults.some(assignment => !selected.includes(assignment.companyContact.id));
    return !notInSelection;
  }, [selected, searchResults]);
  const selectAllChanged = useCallback(
    (state: CheckedState) => {
      const ids = searchResults.map(assign => assign.companyContact.id);
      if (state) {
        setSelected(sel => [...new Set<string>([...sel, ...ids])]);
      } else {
        setSelected(sel => sel.filter(id => !ids.includes(id)));
      }
    },
    [searchResults],
  );

  return (
    <div>
      <div className="flex flex-row gap-4 px-4">
        <Input
          placeholder="Search contacts..."
          value={search}
          onChange={event => setSearch(event.target.value)}
          className="w-full"
        />
        <LocationActions location={location} selectedIds={selected}>
          <Button variant="link">
            Actions
            <ChevronDown />
          </Button>
        </LocationActions>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <Checkbox
                checked={allSelected ? true : selected?.length ? 'indeterminate' : false}
                onCheckedChange={selectAllChanged}
              />
            </TableHead>
            <TableHead>Name</TableHead>
            {!isMobile && <TableHead>Email</TableHead>}
            <TableHead>Role</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {searchResults.length ? (
            searchResults.map(assignment => (
              <TableRow key={assignment.id}>
                <TableCell>
                  <Checkbox
                    onCheckedChange={checked =>
                      setSelected(
                        checked
                          ? [...selected, assignment.companyContact.id]
                          : selected.filter(id => id != assignment.companyContact.id),
                      )
                    }
                    checked={selected.includes(assignment.companyContact.id)}
                  />
                </TableCell>
                <TableCell>
                  {assignment.companyContact.customer.firstName} {assignment.companyContact.customer.lastName}
                  {isMobile && (
                    <>
                      <br /> {assignment.companyContact.customer.email}
                    </>
                  )}
                </TableCell>
                {!isMobile && <TableCell>{assignment.companyContact.customer.email}</TableCell>}
                <TableCell>{assignment.role.name}</TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={isMobile ? 3 : 4} className="h-24 text-center">
                {location.assignments?.length
                  ? 'There are no contacts for the selected location.'
                  : 'No search results.'}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
