import { AccountRepo } from '@/business/core/repositories/account';
import { CompanyRepo } from '@/business/core/repositories/company';
import { CustomerRepo } from '@/business/core/repositories/customer';
import { AccountHandler } from '@/business/handlers/account.handler';
import { defer, LoaderFunctionArgs, SerializeFrom } from 'react-router';

export async function loader({ context }: LoaderFunctionArgs) {
  const { ownerId, contacts, customer } = await context.resolve(AccountHandler).validateOwnerAccess();

  const accountRepo = context.resolve(AccountRepo);
  const customerRepo = context.resolve(CustomerRepo);
  const companyRepo = context.resolve(CompanyRepo);

  const selectedContact = contacts.find(contact => contact.company.id == ownerId);

  const locations = selectedContact
    ? customerRepo.getContactLocationsInfo(selectedContact).then(async locations =>
        Promise.all(
          locations.map(async ({ access, info }) => {
            const assignments = await companyRepo.getLocationAssignments(info.id);

            return {
              access,
              info,
              assignments,
            };
          }),
        ),
      )
    : undefined;

  const info = await accountRepo.getSystemOwnerContactInfo(ownerId);

  return {
    info: info!,
    isPrimaryOwner: ownerId == customer.id || selectedContact?.isMainContact || false,
    locations,
  };
}

export type SerializedContactInfo = NonNullable<SerializeFrom<typeof loader>['info']>;
export type SerializedContactLocation = Awaited<NonNullable<SerializeFrom<typeof loader>['locations']>>[number];
