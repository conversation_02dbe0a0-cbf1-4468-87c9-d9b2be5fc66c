import { roleSchema } from '@/business/core/types';
import {
  address1Schema,
  address2Schema,
  citySchema,
  companyNameSchema,
  nameSchema,
  phoneSchema,
  stateSchema,
  zipSchema,
} from '@/business/core/types/common';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

export const schema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('company'),
    name: companyNameSchema.optional(),
    mainContactId: z.string().optional(),
  }),
  z.object({
    type: z.literal('customer'),
    firstName: nameSchema.optional(),
    lastName: nameSchema.optional(),
    phone: phoneSchema,
  }),
  z.object({
    type: z.literal('address'),
    id: z.string().optional(),
    makeDefault: z.boolean({ coerce: true }).optional(),
    firstName: nameSchema.optional(),
    lastName: nameSchema.optional(),
    company: nameSchema.optional(),
    phone: phoneSchema,
    address1: address1Schema,
    address2: address2Schema,
    city: citySchema,
    provinceCode: stateSchema,
    zip: zipSchema,
  }),
  z.object({
    type: z.literal('removeAddress'),
    id: z.string(),
  }),
  z.object({
    type: z.literal('updateLocationDetails'),
    id: z.string(),
    name: nameSchema.optional(),
    phone: phoneSchema,
  }),
  z.object({
    type: z.literal('updateLocationAddress'),
    id: z.string(),
    billing: z.boolean().optional(),
    shipping: z.boolean().optional(),
    firstName: nameSchema.optional(),
    lastName: nameSchema.optional(),
    recipient: nameSchema.optional(),
    address1: address1Schema,
    address2: address2Schema.optional(),
    city: citySchema,
    zip: zipSchema,
  }),
  z.object({
    type: z.literal('updateLocationContacts'),
    id: z.string(),
    assignContactIds: z.string().array(),
    assignRole: roleSchema,
  }),
  z.object({
    type: z.literal('revokeContactRole'),
    id: z.string(),
    revokeContactIds: z.string().array(),
  }),
  z.object({
    type: z.literal('inviteContact'),
    id: z.string(),
    email: z.string().email('Invalid email address.'),
    assignRole: roleSchema,
  }),
]);

export type SchemaType = z.infer<typeof schema>;

export const resolver = zodResolver(schema);
