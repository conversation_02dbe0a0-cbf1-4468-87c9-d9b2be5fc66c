import { AuthHandler } from '@/business/handlers/auth.handler';
import { ActionFunctionArgs, LoaderFunctionArgs } from 'react-router';

export async function action({ context, request }: ActionFunctionArgs) {
  const authHandler = context.resolve(AuthHandler);
  const customerInfo = await authHandler.queryCustomerInfo();

  if (!customerInfo) throw new Response('Unauthorized', { status: 401 });

  const formData = await request.formData();
  const returnTo = formData.get('return_to')?.toString()?.startsWith('/')
    ? formData.get('return_to')?.toString()
    : undefined;
  const { searchParams } = new URL(request.url);
  const return_to = searchParams.get('return_to');

  await authHandler.sendSSOLink(customerInfo.email, returnTo || return_to || '/account');

  return null;
}

export async function loader({ context, request }: LoaderFunctionArgs) {
  const { searchParams } = new URL(request.url);

  return await context.resolve(AuthHandler).handleSSOToken(searchParams.get('token') || '');
}
