import { DeviceRepo } from '@/business/core/repositories/device';
import { SubscriptionRepo } from '@/business/core/repositories/subscription';
import { DeviceService } from '@/business/core/services/devices';
import { AccountHandler } from '@/business/handlers/account.handler';
import { stripeDate } from '@/lib/utils';
import { ActionFunctionArgs } from 'react-router';

export async function action({ request, context }: ActionFunctionArgs) {
  const { system } = await context.resolve(AccountHandler).validateSystemAccess();
  const formData = await request.formData();

  const cancelConsent = parseInt(formData.get('cancel_consent')?.toString() || '0');

  if (isNaN(cancelConsent) || cancelConsent < Date.now() - 15 * 60 * 1000) {
    throw new Response('Invalid cancel consent', { status: 400 });
  }

  const subscriptionRepo = context.resolve(SubscriptionRepo);
  const deviceRepo = context.resolve(DeviceRepo);
  const deviceService = context.resolve(DeviceService);

  const { subscription } = await subscriptionRepo.pauseSystemSubscription({
    systemSubscriptionId: system.subscriptionId!,
  });

  const devicesToDeactivate = (await deviceRepo.getDevices(system.ownerId)).filter(
    device => device.systemKey == system.key && device.state != 'inactive',
  );

  const deactivatedDevices = await Promise.all(
    devicesToDeactivate.map(device =>
      deviceService.scheduleDeviceForDeactivation(device, {
        consentAt: new Date(cancelConsent),
        scheduledFor: stripeDate(subscription.current_period_end),
      }),
    ),
  );

  await deviceRepo.setDevices(system.ownerId, deactivatedDevices);

  return { success: true };
}
