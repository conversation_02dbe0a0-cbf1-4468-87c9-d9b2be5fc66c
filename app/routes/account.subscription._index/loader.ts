import { SubscriptionRepo } from '@/business/core/repositories/subscription';
import { AccountHandler } from '@/business/handlers/account.handler';
import { LoaderFunctionArgs } from 'react-router';

export async function loader({ context }: LoaderFunctionArgs) {
  const { system } = await context.resolve(AccountHandler).validateSystemAccess();

  const invoice = await context
    .resolve(SubscriptionRepo)
    .previewUpcomingInvoice({ systemSubscriptionId: system.subscriptionId! });

  return { invoice };
}
