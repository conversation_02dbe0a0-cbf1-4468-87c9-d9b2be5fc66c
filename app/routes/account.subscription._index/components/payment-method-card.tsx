import { Button } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAccountLoader } from '@/routes/account/hooks';
import { Link } from 'react-router';
import { ArrowUpRight } from 'lucide-react';

export function PaymentMethodCard() {
  const { subscription } = useAccountLoader();
  return (
    <Card>
      <CardHeader className="block">
        <Button variant="link" size="fit" className="float-right" asChild>
          <Link
            to={`/account/settings/billing-dashboard?${new URLSearchParams({
              return_to: '/account/subscription',
              target: `/subscriptions/${subscription?.id}/update-payment-method/changePaymentMethodFromHome`,
            }).toString()}`}
            target="_blank"
          >
            Update <ArrowUpRight />
          </Link>
        </Button>
        <CardTitle className="mt-0!">Payment method</CardTitle>
        <CardDescription>Click here to edit your payment method.</CardDescription>
      </CardHeader>
    </Card>
  );
}
