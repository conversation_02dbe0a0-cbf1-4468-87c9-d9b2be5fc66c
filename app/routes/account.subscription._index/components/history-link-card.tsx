import { But<PERSON> } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router';
import { ArrowUpRight } from 'lucide-react';

export function HistoryLinkCard() {
  return (
    <Card>
      <CardHeader className="block">
        <Button variant="link" size="fit" className="float-right" asChild>
          <Link to="/account/subscription/invoice-history" prefetch="intent">
            History <ArrowUpRight />
          </Link>
        </Button>
        <CardTitle className="mt-0!">Invoices</CardTitle>
        <CardDescription>Click here to view your invoice history.</CardDescription>
      </CardHeader>
    </Card>
  );
}
