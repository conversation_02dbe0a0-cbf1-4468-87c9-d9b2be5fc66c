import { LoaderFunctionArgs, redirect } from 'react-router';
import Page, { loader as wildLoader, meta } from './_global.$';

export { meta };

export const loader = async (args: LoaderFunctionArgs) => {
  // If this is the embedded admin request, redirect to the Shopify admin home
  const { pathname, searchParams } = new URL(args.request.url);
  if ((!pathname || pathname == '/') && searchParams.get('shop')) {
    throw redirect(`/admin?${searchParams.toString()}`);
  }

  return wildLoader(args);
};

export default Page;
