import { z } from 'zod';
import { CAR_DEVICE_HANDLES } from '../vin-tool.$orderNumber._index/resolver';
import { zodResolver } from '@hookform/resolvers/zod';

export const paramsSchema = z.object({
  deviceType: z.enum(CAR_DEVICE_HANDLES),
  ownerNumber: z.number({ coerce: true }).int().positive(),
});

export const schema = z.object({
  qrText: z.string().max(2048).optional(),
  deviceNumber: z.number({ coerce: true }).int().min(1).max(100).optional(),
});

export type SchemaType = z.infer<typeof schema>;

export const resolver = zodResolver(schema);
