import { LoaderFunctionArgs } from 'react-router';
import { findDevice } from './action';
import { paramsSchema } from './resolver';

export async function loader({ params, context, request }: LoaderFunctionArgs) {
  const parsed = paramsSchema.safeParse(params);

  if (!parsed.success) throw new Error('Invalid params');

  const { searchParams } = new URL(request.url);

  let searchDevice: Awaited<ReturnType<typeof findDevice>> | undefined;
  if (searchParams.has('SN') || searchParams.has('IMEI')) {
    searchDevice = await findDevice({
      context,
      ...parsed.data,
      qrText: request.url,
    });
  } else if (searchParams.has('device')) {
    searchDevice = await findDevice({
      context,
      ...parsed.data,
      deviceNumber: parseInt(searchParams.get('device') || '0'),
    });
  }

  return { ...parsed.data, searchDevice };
}
