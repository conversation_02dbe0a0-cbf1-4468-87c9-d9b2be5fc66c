import { DeviceRepo } from '@/business/core/repositories/device';
import { VINRepo } from '@/business/core/repositories/vin';
import { VINMakeModelYear } from '@/business/core/types/vin';
import { convertIdToGid } from '@/lib/utils';
import { ActionFunctionArgs } from 'react-router';
import { AppLoadContext } from 'react-router';
import { FieldErrors } from 'react-hook-form';
import { getValidatedFormData } from 'remix-hook-form';
import { CarSystemDevice } from '../vin-tool.$orderNumber._index/resolver';
import { paramsSchema, resolver, SchemaType } from './resolver';

type CarSystemDevicePlugInView = Pick<CarSystemDevice, 'IMEI' | 'serialNumber' | 'VIN' | 'nickname'> &
  Partial<VINMakeModelYear> & {
    deviceNumber: number;
  };

export async function findDevice({
  context,
  deviceType,
  ownerNumber,
  qrText,
  deviceNumber,
}: {
  context: AppLoadContext;
  deviceType: CarSystemDevice['handle'];
  ownerNumber: number;
  qrText?: string;
  deviceNumber?: number;
}): Promise<{
  errors?: FieldErrors<SchemaType>;
  device?: CarSystemDevicePlugInView;
}> {
  const ownerId = convertIdToGid(deviceType == 'fleet-tracker' ? 'Company' : 'Customer', ownerNumber)!;

  const devices = (
    ((await context.resolve(DeviceRepo).getDevices(ownerId))
      ?.filter(device => device.handle == deviceType)
      ?.sort((a, b) => a.key.localeCompare(b.key)) as CarSystemDevice[]) || []
  ).map(({ IMEI, serialNumber, VIN, nickname }, index) => ({
    IMEI,
    serialNumber,
    VIN,
    nickname,
    deviceNumber: index + 1,
  }));

  let foundDevice: CarSystemDevicePlugInView | undefined;

  if (qrText) {
    foundDevice = devices.find(
      device =>
        (device.IMEI && qrText.includes(device.IMEI)) || (device.serialNumber && qrText.includes(device.serialNumber)),
    );
  } else if (deviceNumber) {
    const device = devices?.[deviceNumber - 1];

    if (device) {
      foundDevice = {
        IMEI: device.IMEI,
        serialNumber: device.serialNumber,
        VIN: device.VIN,
        nickname: device.nickname,
        deviceNumber: device.deviceNumber,
      };
    }
  } else {
    return { errors: { root: { message: 'QR code or device number is required.' } as any } };
  }

  if (!foundDevice) {
    return {
      errors: {
        qrText: { message: 'Sorry, we could not find a vehicle associated with this Fleet Tracker.', type: 'value' },
      },
    };
  }

  const MMY = foundDevice.VIN ? await context.resolve(VINRepo).decodeVIN(foundDevice.VIN) : undefined;

  return { device: { ...foundDevice, ...MMY } };
}

export async function action({ params, context, request }: ActionFunctionArgs): Promise<{
  errors?: FieldErrors<SchemaType>;
  device?: CarSystemDevicePlugInView;
}> {
  const parsed = paramsSchema.safeParse(params);

  if (!parsed.success) return { errors: { root: { message: parsed.error.message } as any } };

  const { errors, data } = await getValidatedFormData<SchemaType>(request, resolver);

  if (errors) return { errors };

  return await findDevice({ context, ...parsed.data, ...data });
}
