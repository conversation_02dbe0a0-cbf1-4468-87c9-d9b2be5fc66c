import AnimatedBlob from '@/components/AnimatedBlob';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { GuideStepsProvider } from './components/context';
import { PLUG_IN_STEP_NAME, PlugInStep } from './components/PlugInStep';
import { QUICK_TIP_STEP_NAME, QuickTipStep } from './components/QuickTipStep';
import { SCAN_STEP_NAME, ScanStep } from './components/ScanStep';
import { WELCOME_STEP_NAME, WelcomeStep } from './components/WelcomeStep';
import { action } from './action';
import { loader } from './loader';
import { useLoaderData } from 'react-router';

export { action, loader };

export default function Page() {
  const [animateRef] = useAutoAnimate();
  const { searchDevice } = useLoaderData<typeof loader>();

  return (
    <GuideStepsProvider
      initialStep={searchDevice ? PLUG_IN_STEP_NAME : undefined}
      steps={[WELCOME_STEP_NAME, QUICK_TIP_STEP_NAME, SCAN_STEP_NAME, PLUG_IN_STEP_NAME]}
    >
      <main ref={animateRef} className="flex min-h-svh w-full flex-col">
        <AnimatedBlob zIndex={-10} left="50%" top="70%" width="60px" height="200px" />
        <AnimatedBlob zIndex={-10} left="50%" top="90%" delay="-7s" width="200px" height="600px" />
        <WelcomeStep />
        <QuickTipStep />
        <ScanStep />
        <PlugInStep />
      </main>
    </GuideStepsProvider>
  );
}
