import { ShopifyClient } from '@/business/clients/shopify-client';
import { useLoaderData } from 'react-router';
import { LoaderFunctionArgs } from 'react-router';
import { BlockStack, Card, Page, Text } from '@shopify/polaris';

export const loader = async ({ request, context }: LoaderFunctionArgs) => {
  const shopify = context.resolve(ShopifyClient);
  const { session } = await shopify.authenticate.admin(request);

  const offlineSession = (await shopify.sessionStorage.findSessionsByShop(session.shop)).find(
    session => !session.isOnline,
  );

  return { offlineSession };
};

export default function RevealToken() {
  const { offlineSession } = useLoaderData<typeof loader>();

  return (
    <Page title="Offline Access Token" backAction={{ url: '/admin' }}>
      <Card>
        <BlockStack gap={'300'}>
          <Text variant="heading2xl" as="h1" tone="success" fontWeight="medium" alignment="center">
            {offlineSession?.accessToken}
          </Text>
          <Text as="p" tone="subdued" variant="bodySm">
            This is the offline AdminAPI access token for the hydrogen storefront... Guard it with your life! It should
            only change if the scopes of the app change or the app needs to be reinstalled for some reason.
          </Text>
          <Text as="p" tone="critical" variant="bodySm">
            In the event that this token changes, it should be set on the "SHOPIFY_OFFLINE_ADMIN_ACCESS_TOKEN" Oxygen
            environment variable.
          </Text>
        </BlockStack>
      </Card>
    </Page>
  );
}
