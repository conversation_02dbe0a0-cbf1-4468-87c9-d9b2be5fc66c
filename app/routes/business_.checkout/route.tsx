import { But<PERSON> } from '@/components/ui/button';
import { Link, useFetcher, useLoaderData } from 'react-router';
import { ChevronLeft } from 'lucide-react';
import { useCallback } from 'react';
import { action } from './action';
import { CheckoutFormProvider, CheckoutStepsProvider, useCheckoutForm } from './components/checkout-context';
import { CompanyStep, useCompanyStep } from './components/company-step';
import { CompleteStep } from './components/complete-step';
import { ContactStep, useContactStep } from './components/contact-step';
import { OTPStep, useOTPStep } from './components/otp-step';
import StepIndicator from './components/step-indicator';
import { loader } from './loader';
import { SchemaType } from './resolver';
import ThemeProvider from '@/components/ThemeProvider';
import { cn } from '@/lib/utils';
import { FordTransitQuestionStep, useFordTransitQuestion } from './components/ford-transit-question';
import { FordTransitExtensionStep, useFordTransitExtension } from './components/ford-transit-extension';
import { CartProvider } from '@/components/Cart/CartProvider';

export { action, loader };

export default function BusinessCheckout() {
  const { cart } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<typeof action>({ key: 'form-checkout' });

  const submit = useCallback((data: SchemaType) => {
    fetcher.submit(data, { method: 'POST' });
  }, []);

  return (
    <CartProvider overwriteCart={cart}>
      <ThemeProvider theme="personal">
        {themeClassName => (
          <div className={cn('min-h-screen w-full', themeClassName)}>
            <div className="mx-auto w-full max-w-(--breakpoint-sm) px-4 py-8">
              <Button variant="link" className="mb-4 text-primary sm:absolute sm:left-12" asChild>
                <Link to="/business/cart">
                  <ChevronLeft className="inline size-4" /> Back to Cart
                </Link>
              </Button>
              <CheckoutFormProvider onSubmit={submit}>
                <BusinessCheckoutSteps />
              </CheckoutFormProvider>
            </div>
          </div>
        )}
      </ThemeProvider>
    </CartProvider>
  );
}

function BusinessCheckoutSteps() {
  const { submit } = useCheckoutForm();
  return (
    <CheckoutStepsProvider
      steps={[
        useContactStep(), //No need to include the complete step here because it's handled by the form provider
        useCompanyStep(),
        useOTPStep(),
        useFordTransitQuestion(),
        useFordTransitExtension(),
      ]}
      onComplete={submit}
    >
      <StepIndicator />
      {/* Step rendering order is reversed so that errors are shown in the correct order */}
      <CompleteStep />
      <FordTransitExtensionStep />
      <FordTransitQuestionStep />
      <OTPStep />
      <CompanyStep />
      <ContactStep />
    </CheckoutStepsProvider>
  );
}
