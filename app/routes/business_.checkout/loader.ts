import { AugmentedCart } from '@/business/core/types/cart';
import { AccountHandler } from '@/business/handlers/account.handler';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { CartHandler } from '@/business/handlers/cart.handler';
import { LoaderFunctionArgs, redirect } from 'react-router';

export async function loader({ context, request }: LoaderFunctionArgs) {
  // const accountHandler = context.resolve(AccountHandler);
  const cartPromise = context.resolve(CartHandler).getCartForDisplay(true);
  const customerId = await context.resolve(AuthHandler).getLoggedInCustomerId();

  // await context.resolve(CartHandler).blockCompanyCheckoutForCustomerWithExistingContact(customerId);

  const customerProfile = await context.resolve(AccountHandler).getCustomerProfile(customerId);

  const cart: AugmentedCart = await cartPromise;
  if (!cart?.lines?.nodes?.length || cart?.lines?.nodes?.some(node => node?.error)) return redirect('/business/cart');

  return {
    customerProfile,
    cart: await cartPromise,
  };
}
