import { CompanyRepo } from '@/business/core/repositories/company';
import { CustomerRepo } from '@/business/core/repositories/customer';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { ActionFunctionArgs, redirect } from 'react-router';
import { CountryCode } from 'admin.types';
import { getValidatedFormData } from 'remix-hook-form';
import { resolver, SchemaType } from './resolver';

const UNEXPECTED_ERROR = 'Unexpected error... please try again or contact support.';

export async function action({ request, context }: ActionFunctionArgs) {
  const { data, errors } = await getValidatedFormData<SchemaType>(request, resolver);

  if (errors) return { errors };

  const authHandler = context.resolve(AuthHandler);
  const customerRepo = context.resolve(CustomerRepo);
  const companyRepo = context.resolve(CompanyRepo);

  const otpVerified = await authHandler.verifyOTP(data.OTP);
  if (otpVerified?.error) {
    return { errors: { OTP: { message: otpVerified.error } } };
  }

  const { email } = authHandler.getOIDCSession();

  if (!email) return { errors: { email: { message: 'Email is required' } } };

  let customerId = await customerRepo.getCustomerIdFromEmail(email);

  if (customerId) {
    const companies = await customerRepo.getCustomerCompanies(customerId);

    if (companies?.[0]?.id) throw redirect(`/account/login?${new URLSearchParams({ return_to: '/business/cart' })}`);
  } else {
    customerId = await customerRepo.createCustomer({
      email,
      firstName: data.firstName,
      lastName: data.lastName,
    });
  }

  if (!customerId) return { errors: { root: { message: UNEXPECTED_ERROR } } };

  const company = await companyRepo.createCompany({
    company: {
      name: data.name,
    },
    companyLocation: {
      name: data.name,
      billingSameAsShipping: true,
      shippingAddress: {
        address1: data.address1,
        address2: data.address2,
        city: data.city,
        countryCode: CountryCode.US,
        zoneCode: data.state,
        zip: data.zip,
      },
      buyerExperienceConfiguration: {
        editableShippingAddress: true,
      },
      phone: data.phone,
    },
  });

  const companyId = company?.id;
  const locationId = company?.locations?.nodes?.[0]?.id;

  if (!companyId || !locationId) return { errors: { root: { message: UNEXPECTED_ERROR } } };

  const contactId = await companyRepo.assignContact(companyId, customerId);

  const { adminRole } = await companyRepo.getCompanyRoles(companyId);

  if (!contactId || !adminRole) return { errors: { root: { message: UNEXPECTED_ERROR } } };

  await companyRepo.assignContactsRole(locationId, [contactId], adminRole.id);
  await companyRepo.updateMainContact(companyId, contactId);

  const queryParams = new URLSearchParams({
    return_to: `/share-b2b-auth?${new URLSearchParams({
      return_to: '/business/cart?checkout=true',
    })}`,
  });

  throw redirect(`/account/login?${queryParams.toString()}`);
}
