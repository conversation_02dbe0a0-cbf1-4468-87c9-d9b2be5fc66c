import { cn } from '@/lib/utils';
import { Check, Timer } from 'lucide-react';
import { useCheckoutSteps } from './checkout-context';

export default function StepIndicator() {
  let { stepIndex, step } = useCheckoutSteps();

  const complete = stepIndex >= 3 || step == 'complete';

  return (
    <div className="relative flex flex-col items-center justify-center gap-2">
      {complete && (
        <Check className="size-8 rounded-full bg-gradient-to-r from-gradient-start to-gradient-end p-2 text-background" />
      )}
      <h4 className="text-sm font-semibold text-muted-foreground">
        {!complete ? `Step ${stepIndex + 1} of 3` : 'Account Setup Complete!'}
      </h4>
      <div className="space-x-2">
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className={cn(
              'inline-block h-1 w-20 rounded-full transition-colors',
              index === stepIndex || complete ? 'bg-primary' : 'bg-muted',
            )}
          ></div>
        ))}
      </div>
      <p className="text-sm font-light text-muted-foreground">
        <Timer className="inline size-4" /> Estimated completion 45 seconds
      </p>
    </div>
  );
}
