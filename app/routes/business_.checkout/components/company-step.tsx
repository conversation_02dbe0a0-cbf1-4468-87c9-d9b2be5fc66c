import { PROVINCE_CODES } from '@/business/handlers/constants/province-codes';
import { SelectField } from '@/components/SelectField';
import TextField from '@/components/TextField';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { CheckoutStep, createCheckoutPartialFormHook } from './checkout-context';

const STEP_NAME = 'company';

export const useCompanyStep = () => STEP_NAME;

const [useCompanyPart] = createCheckoutPartialFormHook(
  ['name', 'phone', 'address1', 'address2', 'city', 'state', 'zip'],
  STEP_NAME,
);

export function CompanyStep() {
  const form = useCompanyPart();

  return (
    <CheckoutStep name={STEP_NAME}>
      <h1 className="mt-12 text-center text-3xl font-bold">Create Business Account</h1>
      <p className="my-4 text-center text-sm text-muted-foreground">
        This information will be used to create your company.
      </p>
      <Form form={form} className="space-y-2">
        <TextField label="Company name*" name="name" autoComplete="off" />
        <TextField label="Phone*" name="phone" autoComplete="tel" />
        <h3 className="mt-8! text-lg font-bold">Primary address</h3>
        <TextField label="Address 1*" name="address1" />
        <TextField label="Address 2" name="address2" />
        <div className="grid grid-cols-[1fr_1fr_6rem] gap-2">
          <TextField label="City*" name="city" />
          <SelectField label="State*" name="state" values={PROVINCE_CODES} />
          <TextField label="Zip*" name="zip" />
        </div>
        <Button variant="primary" type="submit" className="mt-8! w-full">
          Continue
        </Button>
      </Form>
    </CheckoutStep>
  );
}
