import TextField from '@/components/TextField';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { useLoaderData } from 'react-router';
import { loader } from '../loader';
import { CheckoutStep, createCheckoutPartialFormHook } from './checkout-context';

const STEP_NAME = 'contact';

export function useContactStep() {
  const { customerProfile } = useLoaderData<typeof loader>();

  return {
    name: STEP_NAME,
    skip: <PERSON><PERSON><PERSON>(customerProfile?.email),
  };
}

const [useContactForm] = createCheckoutPartialFormHook(['firstName', 'lastName', 'email'], STEP_NAME);

export function ContactStep() {
  const form = useContactForm();

  return (
    <CheckoutStep name={STEP_NAME}>
      <h1 className="mt-12 text-center text-3xl font-bold">Create Business Account</h1>
      <p className="my-4 text-center text-sm text-muted-foreground">
        This information will be used to create your company.
      </p>
      <Form form={form} className="flex flex-col items-center">
        <div className="my-8 w-full space-y-4">
          <TextField placeholder="First*" name="firstName" autoComplete="given-name" />
          <TextField placeholder="Last*" name="lastName" autoComplete="family-name" />
          <TextField placeholder="Email*" name="email" autoComplete="email" />
        </div>
        <Button variant="primary" type="submit" loading={form.formState.isSubmitting}>
          Continue
        </Button>
      </Form>
    </CheckoutStep>
  );
}
