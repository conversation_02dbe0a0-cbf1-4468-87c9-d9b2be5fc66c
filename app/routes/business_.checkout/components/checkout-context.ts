import { createWizardFormContext, createWizardStepsContext } from '@/components/Wizard/ctx-factory';
import { schema, SchemaType } from '../resolver';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFetcher } from 'react-router';
import { action } from '../action';
import { useRemixForm } from 'remix-hook-form';
import { util } from 'zod';
import { useEffect } from 'react';

export const { WizardFormProvider: CheckoutFormProvider, useWizardForm: useCheckoutForm } =
  createWizardFormContext<SchemaType>();

export const {
  WizardStep: CheckoutStep,
  WizardStepsProvider: CheckoutStepsProvider,
  useWizardSteps: useCheckoutSteps,
} = createWizardStepsContext();

export const createCheckoutPartialFormHook = <K extends keyof SchemaType>(formKeys: K[], stepName: string) => {
  const mask = Object.fromEntries(formKeys.map(key => [key, true])) as util.Exactly<
    { [k in keyof SchemaType]?: true },
    { [k in K]: true }
  >;
  const partSchema = schema.pick(mask);
  const resolver = zodResolver(partSchema);

  function useCheckoutFormPart() {
    const fetcher = useFetcher<typeof action>({ key: 'form-checkout' });
    const { commit } = useCheckoutForm();
    const { nextStep, goToStep } = useCheckoutSteps();

    const form = useRemixForm<Pick<SchemaType, K>>({
      fetcher,
      mode: 'onSubmit',
      resolver,
      defaultValues: Object.fromEntries(formKeys.map(key => [key, ''])) as any,
      submitHandlers: {
        onValid: data => {
          commit(data);
          nextStep();
        },
      },
    });

    const errors = form.formState.errors;

    useEffect(() => {
      if (errors && (errors.root || Object.keys(errors).some(key => formKeys.includes(key as K)))) {
        goToStep(stepName);
      }
    }, [errors]);

    return form;
  }

  return [useCheckoutFormPart];
};
