import { SystemDeviceHandle } from '@/business/core/types/device';
import { Button } from '@/components/ui/button';
import { BaseStepState } from '@/components/Wizard/ctx-factory';
import { useCart } from '@/hooks/use-cart';
import { Image } from '@shopify/hydrogen';
import { CheckoutStep, useCheckoutForm, useCheckoutSteps } from './checkout-context';

const STEP_NAME = 'ford-transit-question';

export const useFordTransitQuestion = (): BaseStepState => {
  const { formData } = useCheckoutForm();
  const { cart } = useCart();
  const hasFleetTracker = cart?.lines?.nodes?.some(
    line => line.merchandise.product.deviceHandle?.value == ('fleet-tracker' as SystemDeviceHandle),
  );
  return {
    name: STEP_NAME,
    skip: typeof formData.fordTransit != 'undefined' || !hasFleetTracker,
  };
};

export const FordTransitQuestionStep = () => {
  return (
    <CheckoutStep name={STEP_NAME}>
      <FordTransitQuestionStepImpl />
    </CheckoutStep>
  );
};

function FordTransitQuestionStepImpl() {
  const { nextStep } = useCheckoutSteps();
  const { commit } = useCheckoutForm();

  return (
    <>
      <h1 className="text-3xl font-bold mt-12 text-center">One Last Question</h1>
      <h3 className="w-full text-center my-6">
        Are any of your vehicles <b>Ford Transits?</b>
      </h3>
      <div className="flex flex-row justify-center gap-4 my-6">
        <Button
          variant="outline"
          size="md"
          onClick={() => {
            commit({ fordTransit: false });
            nextStep();
          }}
        >
          No
        </Button>
        <Button
          variant="outline"
          size="md"
          onClick={() => {
            commit({ fordTransit: true });
            nextStep();
          }}
        >
          Yes
        </Button>
      </div>
      <Image
        data={{
          altText: 'Ford Transit',
          url: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/ford-transit.png?v=1747783768',
          id: '38860623937787',
          width: 1221,
          height: 573,
        }}
        sizes="(max-width: 400px) 100vw, 400px"
        className="w-full max-w-[400px] mx-auto"
      />
    </>
  );
}
