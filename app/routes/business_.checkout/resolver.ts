import {
  address1Schema,
  address2Schema,
  citySchema,
  companyNameSchema,
  phoneSchema,
  stateSchema,
  zipSchema,
} from '@/business/core/types/common';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { otpSchema } from '../otp/resolver';

export const schema = z.object({
  //Contanct
  email: z.string().trim().email().optional(),
  firstName: z
    .string()
    .trim()
    .min(1, 'First name is required.')
    .max(50, 'First name must be less than 50 characters.')
    .optional(),
  lastName: z
    .string()
    .trim()
    .min(1, 'Last name is required.')
    .max(50, 'Last name must be less than 50 characters.')
    .optional(),
  //Company
  name: companyNameSchema,
  address1: address1Schema,
  address2: address2Schema,
  city: citySchema,
  state: stateSchema,
  zip: zipSchema,
  phone: phoneSchema,
  //OTP:
  OTP: otpSchema,
  //Ford transit:
  fordTransit: z.boolean({ coerce: true }).optional(),
});

export type SchemaType = z.infer<typeof schema>;
export const resolver = zodResolver(schema);
