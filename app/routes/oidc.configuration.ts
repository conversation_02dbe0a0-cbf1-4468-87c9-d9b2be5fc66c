import { LoaderFunctionArgs } from 'react-router';

export async function loader({ context }: LoaderFunctionArgs) {
  const env = context.resolve<Env>('env');
  const HOST = env.HOST;

  return Response.json({
    issuer: `${HOST}`,
    authorization_endpoint: `${HOST}/oidc/authorize`,
    token_endpoint: `${HOST}/oidc/token`,
    jwks_uri: `${HOST}/oidc/jwks`,
    grant_types_supported: ['authorization_code', 'refresh_token'],
    response_types_supported: ['code'],
    scopes_supported: ['openid', 'email'],
    token_endpoint_auth_methods_supported: ['client_secret_basic'],
  });
}
