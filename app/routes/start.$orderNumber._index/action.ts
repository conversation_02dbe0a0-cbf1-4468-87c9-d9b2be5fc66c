import { OrderRepo } from '@/business/core/repositories/order';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { StartHandler } from '@/business/handlers/start.handler';
import { convertIdToGid } from '@/lib/utils';
import { ActionFunctionArgs, redirect } from 'react-router';
import { FieldErrors } from 'react-hook-form';
import { getValidatedFormData } from 'remix-hook-form';
import { resolver, SchemaType } from './resolver';

export async function action({ context, request, params }: ActionFunctionArgs) {
  const { orderNumber } = params;

  const { data, errors } = await getValidatedFormData<SchemaType>(request, resolver);

  if (errors) return { errors };

  const authHandler = context.resolve(AuthHandler);
  const startHandler = context.resolve(StartHandler);

  switch (data.action) {
    case 'sendOTP':
      const orderId = convertIdToGid('Order', orderNumber)!;
      const orderCustomer = await context.resolve(OrderRepo).getCustomer(orderId);

      const send = await authHandler.sendOTP(orderCustomer?.email!);
      return 'error' in send
        ? { errors: { email: { message: send?.error } } as FieldErrors<SchemaType> }
        : { sent: true };
    case 'verifyOTP':
      const { success, error } = await authHandler.verifyOTP(data.OTP);

      if (success) {
        const { pathname } = new URL(request.url);
        throw redirect(`/account/login?${new URLSearchParams({ return_to: pathname + '?confirmed_email=true' })}`);
      }

      return { errors: { OTP: { message: error } } as FieldErrors<SchemaType> };
    case 'upgrade':
      return await startHandler.upgradeService(orderNumber, data.consent);
    case 'quoteUpgrade':
      return await startHandler.quoteUpgradeServiceInvoice(orderNumber);
    case 'startCheckout':
      return await startHandler.startNewServiceCheckout(orderNumber);
  }
}
