import { action } from './action';
import { CheckoutStep, useCheckoutStep } from './components/CheckoutStep';
import { CompleteStep, useCompleteStep } from './components/CompleteStep';
import { FleetUserNoticeStep, useFleetUserNoticeStep } from './components/FleetUserNoticeStep';
import { OTPStep, useOTPStep } from './components/OTPStep';
import { ServicePreviewStep, useServicePreviewStep } from './components/ServicePreviewStep';
import { StartStepsProvider } from './components/start-wizard-context';
import { UpgradeStep, useUpgradeStep } from './components/UpgradeStep';
import { useWelcomeStep, WelcomeStep } from './components/WelcomeStep';
import { loader } from './loader';

export { action, loader };

export default function OrderStart() {
  return (
    <main className="mx-auto grid min-h-svh w-full max-w-md p-4">
      <StartStepsProvider
        steps={[
          useWelcomeStep(),
          useOTPStep(),
          useFleetUserNoticeStep(),
          useServicePreviewStep(),
          useCheckoutStep(),
          useUpgradeStep(),
          useCompleteStep(),
        ]}
      >
        <WelcomeStep />
        <OTPStep />
        <FleetUserNoticeStep />
        <ServicePreviewStep />
        <CheckoutStep />
        <UpgradeStep />
        <CompleteStep />
      </StartStepsProvider>
    </main>
  );
}
