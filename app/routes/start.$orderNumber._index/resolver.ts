import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

export const schema = z.discriminatedUnion('action', [
  z.object({ action: z.literal('sendOTP') }),
  z.object({ action: z.literal('verifyOTP'), OTP: z.string() }),
  z.object({ action: z.literal('quoteUpgrade') }),
  z.object({ action: z.literal('upgrade'), consent: z.number() }),
  z.object({ action: z.literal('startCheckout') }),
]);

export type SchemaType = z.infer<typeof schema>;

export const resolver = zodResolver(schema);
