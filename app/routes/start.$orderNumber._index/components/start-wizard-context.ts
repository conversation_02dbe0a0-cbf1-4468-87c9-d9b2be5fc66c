import { createWizardStepsContext } from '@/components/Wizard/ctx-factory';
import { useLoaderData, useSearchParams } from 'react-router';
import { loader } from '../loader';

export const {
  useWizardSteps: useStartSteps,
  WizardStepsProvider: StartStepsProvider,
  WizardStep: StartStep,
} = createWizardStepsContext();

export const useStartWizardContext = () => {
  const [searchParams] = useSearchParams();
  const confirmedEmail = searchParams.get('confirmed_email') == 'true';
  const { flow, orderCustomerId, loggedInCustomerId, orderState } = useLoaderData<typeof loader>();

  return {
    loggedIn: confirmedEmail && orderCustomerId == loggedInCustomerId,
    upgradeFlow: flow == 'upgrade',
    servicePaid: orderState.servicePaid,
  };
};
