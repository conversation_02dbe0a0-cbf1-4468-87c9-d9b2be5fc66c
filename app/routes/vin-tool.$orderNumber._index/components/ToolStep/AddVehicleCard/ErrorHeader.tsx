import { CircleAlert } from 'lucide-react';
import { ReactNode } from 'react';

export default function ErrorHeader({ title, children }: { title: string; children?: ReactNode }) {
  return (
    <div className="text-destructive">
      <h3 className="font-bold">
        <CircleAlert className="mr-2 inline-block size-4" /> {title}
      </h3>
      <p className="text-sm">{children}</p>
    </div>
  );
}
