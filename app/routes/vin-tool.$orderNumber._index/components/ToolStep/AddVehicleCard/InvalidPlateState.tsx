import { VINIdentificationState } from '@/business/core/types/vin';
import { useCallback, useState } from 'react';
import ErrorHeader from './ErrorHeader';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PROVINCE_CODES } from '@/business/handlers/constants/province-codes';
import { useVINIdentifier } from '@/hooks/use-vin-identifier';

export default function InvalidPlateState({
  state,
  setState,
  onRemove,
}: {
  state: VINIdentificationState;
  setState: (state: VINIdentificationState) => void;
  onRemove?: () => void;
}) {
  const [plateNumber, setPlateNumber] = useState(state.licensePlate?.plate!);
  const [stateCode, setStateCode] = useState(state.licensePlate?.stateCode!);

  const { identify, loading } = useVINIdentifier({
    onSuccess: response => setState(response.results[0]),
  });

  const tryAgain = useCallback(() => {
    identify({ plate: plateNumber, stateCode });
  }, [plateNumber, stateCode, identify]);

  return (
    <div className="space-y-4 p-4">
      <ErrorHeader title="Invalid Plate">
        Sorry, we could not validate this vehicle’s license plate. Please verify the plate number and state and try
        again{onRemove && ' – or click to remove'}.
      </ErrorHeader>
      <div className="grid grid-cols-2 items-end gap-4">
        <div>
          <Label htmlFor="plate">Plate Number</Label>
          <Input id="plate" autoComplete="off" value={plateNumber} onChange={e => setPlateNumber(e.target.value)} />
        </div>
        <Select value={stateCode} onValueChange={setStateCode}>
          <SelectTrigger>
            <SelectValue placeholder="Select a state" />
          </SelectTrigger>
          <SelectContent>
            {PROVINCE_CODES.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="mt-4 grid grid-cols-2 gap-4">
        {onRemove && (
          <Button size="full" variant="outline" onClick={onRemove}>
            Remove
          </Button>
        )}
        <Button
          size="full"
          variant="primary"
          onClick={tryAgain}
          disabled={plateNumber == state.licensePlate?.plate && stateCode == state.licensePlate?.stateCode}
          loading={loading}
        >
          Try Again
        </Button>
      </div>
    </div>
  );
}
