import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { Link, useLoaderData } from 'react-router';
import { loader } from '../loader';
import { useVinToolSteps } from './vin-tool-context';

export default function CloseButton() {
  const { accessToken } = useLoaderData<typeof loader>();
  const { goToStep } = useVinToolSteps();

  return (
    <Button
      className="fixed top-4 right-4"
      onClick={accessToken ? undefined : () => goToStep('welcome')}
      variant="link"
      size="icon"
      asChild
    >
      <Link to={accessToken ? '/account' : '#'} prefetch="none">
        <X />
      </Link>
    </Button>
  );
}
