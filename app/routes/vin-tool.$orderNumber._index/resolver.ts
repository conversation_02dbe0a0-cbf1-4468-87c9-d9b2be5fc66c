import { SystemDevice, VIN } from '@/business/core/types/device';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

export const schema = z
  .object({ VIN, nickname: z.string().max(100, 'Nickname must be less than 100 characters') })
  .required();
export const resolver = zodResolver(schema);
export type SchemaType = z.infer<typeof schema>;

export const CAR_DEVICE_HANDLES = ['car-tracker', 'fleet-tracker'] as const;
export type CarSystemDevice = SystemDevice & {
  handle: (typeof CAR_DEVICE_HANDLES)[number];
};
