import { DeviceRepo } from '@/business/core/repositories/device';
import { SystemRepo } from '@/business/core/repositories/system';
import { DeviceService } from '@/business/core/services/devices';
import { OrderService } from '@/business/core/services/order';
import { convertIdToGid } from '@/lib/utils';
import { type ActionFunctionArgs } from 'react-router';
import { type FieldErrors } from 'react-hook-form';
import { getValidatedFormData } from 'remix-hook-form';
import { type z } from 'zod';
import type { CarSystemDevice, schema, SchemaType } from './resolver';
import { CAR_DEVICE_HANDLES, resolver } from './resolver';

export async function action({
  context,
  request,
  params,
}: ActionFunctionArgs): Promise<{ success: boolean; errors?: FieldErrors<SchemaType> }> {
  const { orderNumber } = params;
  const orderId = convertIdToGid('Order', orderNumber)!;

  const { data, errors } = await getValidatedFormData<z.infer<typeof schema>>(request, resolver);

  if (errors) return { success: false, errors };

  const { ownerId, ownerDevices } = await context.resolve(OrderService).getOrderDevices(orderId);
  const devices = Object.values(ownerDevices).filter(
    device => (device.state == 'activating' || device.orderId == orderId) && CAR_DEVICE_HANDLES.includes(device.handle),
  ) as CarSystemDevice[];

  const existingDevice = devices.find(device => device.VIN == data.VIN);
  if (existingDevice) {
    return {
      success: false,
      errors: {
        root: {
          message: `Sorry, it appears ${
            existingDevice.nickname || existingDevice.VIN
          } has already been added to your account.`,
          type: 'vehicle_already_added',
        } as any,
      },
    };
  }

  const firstEmptyVIN = devices.find(device => !device.VIN);
  if (!firstEmptyVIN) throw new Error('Order has already been fulfilled!');

  const deviceService = context.resolve(DeviceService);
  const systemRepo = context.resolve(SystemRepo);
  const deviceRepo = context.resolve(DeviceRepo);

  const system = firstEmptyVIN.systemKey ? await systemRepo.getSystem(ownerId, firstEmptyVIN.systemKey) : undefined;

  // Attempt to activate the device with a new VIN
  const device = await deviceService.tryActivateDevice(
    { ...firstEmptyVIN, VIN: data.VIN, nickname: data.nickname },
    {
      devices: Object.values(ownerDevices),
      systemSubscription: system?.subscriptionId,
    },
  );

  await deviceRepo.setDevices(ownerId, [device]);

  if ('VIN' in device && device.VIN && firstEmptyVIN?.VIN != device.VIN) {
    await deviceService.triggerDeviceFlow(device, 'vehicle-vin-updated');
  }

  return { success: true };
}
