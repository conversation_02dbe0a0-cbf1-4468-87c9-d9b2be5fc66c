import { OrderService } from '@/business/core/services/order';
import { convertIdToGid } from '@/lib/utils';
import { type LoaderFunctionArgs } from 'react-router';
import type { CarSystemDevice } from './resolver';
import { CAR_DEVICE_HANDLES } from './resolver';
import { CustomerAccountClient } from '@/business/clients/accounts-client';

export async function loader({ context, params }: LoaderFunctionArgs) {
  const { orderNumber } = params;
  const orderId = convertIdToGid('Order', orderNumber)!;

  const accessTokenPromise = context.resolve(CustomerAccountClient).getAccessToken();
  const orderService = context.resolve(OrderService);

  const { ownerDevices } = await orderService.getOrderDevices(orderId);

  const devices = Object.values(ownerDevices).filter(
    device => (device.state == 'activating' || device.orderId == orderId) && CAR_DEVICE_HANDLES.includes(device.handle),
  ) as CarSystemDevice[];

  const totalVINs = devices.length;
  const filledVINs = devices.reduce((prev, device) => prev + (device.VIN ? 1 : 0), 0);

  return {
    accessToken: await accessTokenPromise,
    totalVINs,
    filledVINs,
  };
}
