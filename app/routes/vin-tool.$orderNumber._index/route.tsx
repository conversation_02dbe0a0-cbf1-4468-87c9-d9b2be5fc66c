import AnimatedBlob from '@/components/AnimatedBlob';

import { COMPLETE_STEP_NAME, CompleteStep } from './components/CompleteStep';
import { QUICK_TIP_STEP_NAME, QuickTipStep } from './components/QuickTipStep';
import { TOOL_STEP_NAME, ToolStep } from './components/ToolStep';
import TransitionOnComplete from './components/TransitionOnComplete';
import { WELCOME_STEP_NAME, WelcomeStep } from './components/WelcomeStep';

import { useAutoAnimate } from '@formkit/auto-animate/react';
import { action } from './action';
import CloseButton from './components/CloseButton';
import { VinToolStepsProvider } from './components/vin-tool-context';
import { loader } from './loader';
export { action, loader };

export function shouldRevalidate() {
  return true;
}

export default function Page() {
  const [animateRef] = useAutoAnimate();

  return (
    <VinToolStepsProvider steps={[WELCOME_STEP_NAME, QUICK_TIP_STEP_NAME, TOOL_STEP_NAME, COMPLETE_STEP_NAME]}>
      <main ref={animateRef} className="flex min-h-svh w-full flex-col">
        <CloseButton />
        <AnimatedBlob zIndex={-10} left="50%" top="70%" width="60px" height="200px" />
        <AnimatedBlob zIndex={-10} left="50%" top="90%" delay="-7s" width="200px" height="600px" />
        <WelcomeStep />
        <QuickTipStep />
        <ToolStep />
        <CompleteStep />
      </main>
      <TransitionOnComplete />
    </VinToolStepsProvider>
  );
}
