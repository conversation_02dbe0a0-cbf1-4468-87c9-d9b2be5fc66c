import { ShopifyClient } from '@/business/clients/shopify-client';
import { DeviceRepo } from '@/business/core/repositories/device';
import { DeviceService } from '@/business/core/services/devices';
import { type ActionFunctionArgs } from 'react-router';

export async function action({ request, context }: ActionFunctionArgs) {
  const { payload } = await context.resolve(ShopifyClient).authenticate.flow(request);

  if (payload.handle != 'activate-device-on-adc') throw new Response('Incorrect flow action hit.', { status: 403 });

  const ownerId: string = payload?.properties?.owner_id;
  if (typeof ownerId !== 'string') throw new Response('No owner id found.', { status: 400 });

  const deviceKey: string = payload?.properties?.device_key;
  if (typeof deviceKey !== 'string') throw new Response('No device key found.', { status: 400 });

  const devices = await context.resolve(DeviceRepo).getDevicesDictionary(ownerId);

  if (!Object.keys(devices)?.length) throw new Response(`No devices found for owner ${ownerId}.`, { status: 404 });

  const device = devices[deviceKey];
  if (!device) throw new Response(`Device ${deviceKey} not found.`, { status: 404 });

  const resultDevice = await context.resolve(DeviceService).activateDevice(device);

  return Response.json({
    return_value: {
      activated: resultDevice.state == 'active',
      error: resultDevice.activationErrors ? JSON.stringify(resultDevice.activationErrors) : undefined,
      nickname: resultDevice.nickname,
      deviceId: resultDevice.deviceId,
    },
  });
}
