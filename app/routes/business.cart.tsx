import { CustomerAccountClient } from '@/business/clients/accounts-client';
import { BUSINESS_HEADER_MODEL } from '@/business/core/constants/config';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { BuilderHandler } from '@/business/handlers/builder.handler';
import { CartHandler } from '@/business/handlers/cart.handler';
import Builder from '@/components/Builder';
import CartMain from '@/components/Cart';
import ThemeProvider from '@/components/ThemeProvider';
import { cn, generatePageTitle } from '@/lib/utils';
import { TooltipProvider } from '@radix-ui/react-tooltip';
import { LoaderFunctionArgs, type ActionFunctionArgs } from '@shopify/remix-oxygen';
import { HeadersFunction, useLoaderData, type MetaFunction } from 'react-router';

export const meta: MetaFunction = () => {
  return [{ title: generatePageTitle('Cart') }];
};

export const headers: HeadersFunction = ({ actionHeaders }) => actionHeaders;

export async function action({ context }: ActionFunctionArgs) {
  return context.resolve(CartHandler).handleAction('business');
}

export async function loader({ context, request }: LoaderFunctionArgs) {
  const { searchParams } = new URL(request.url);

  const customerAccountClient = context.resolve(CustomerAccountClient);
  const authHandler = context.resolve(AuthHandler);
  const cartHandler = context.resolve(CartHandler);
  const builderHandler = context.resolve(BuilderHandler);

  if (searchParams.get('checkout') === 'true') {
    return await cartHandler.checkoutCart('business', true);
  }

  const accessTokenPromise = customerAccountClient.getAccessToken();
  const cart = cartHandler.getCartForDisplay(true);

  const customerId = authHandler.getLoggedInCustomerId().then(customerId => customerId || null);
  const businessCartQuery = customerId.then(customerId =>
    customerId ? cartHandler.getSystemsForBusinessCart(customerId) : [],
  );

  const systems = businessCartQuery.then(entries => entries.flatMap(entry => entry.systems) || []);
  const fallbackLocationId = businessCartQuery.then(
    entries => entries?.at(0)?.roleAssignments?.at(0)?.companyLocation?.id || null,
  );

  const header = await builderHandler.handleModelFetch({ request, model: BUSINESS_HEADER_MODEL });

  return {
    accessToken: await accessTokenPromise,
    cart: await cart,
    systems,
    fallbackLocationId,
    orderingCustomerId: customerId,
    header,
  };
}

export default function CartPage() {
  const { header } = useLoaderData<typeof loader>();

  return (
    <TooltipProvider delayDuration={0}>
      <Builder content={header as any} model={BUSINESS_HEADER_MODEL} />
      <ThemeProvider theme="personal">
        {themeClassName => (
          <div className={cn(themeClassName)}>
            <CartMain />
          </div>
        )}
      </ThemeProvider>
    </TooltipProvider>
  );
}
