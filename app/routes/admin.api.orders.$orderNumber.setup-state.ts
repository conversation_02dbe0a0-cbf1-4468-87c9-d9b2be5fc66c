import { ShopifyClient } from '@/business/clients/shopify-client';
import { OrderRepo } from '@/business/core/repositories/order';
import { SystemRepo } from '@/business/core/repositories/system';
import { convertIdToGid } from '@/lib/utils';
import { HeadersFunction, LoaderFunctionArgs } from 'react-router';

export const headers: HeadersFunction = ({ loaderHeaders }) => loaderHeaders;

export async function loader({ context, params, request }: LoaderFunctionArgs) {
  await context.resolve(ShopifyClient).authenticate.admin(request);

  const { orderNumber } = params;
  const orderId = convertIdToGid('Order', orderNumber);

  if (!orderId) throw new Error('Missing order id!');

  const setupState = await context.resolve(OrderRepo).getSetupState(orderId);
  if (!setupState) {
    return Response.json(
      { flowNotRun: true },
      {
        headers: {
          'Access-Control-Allow-Origin': 'https://extensions.shopifycdn.com',
        },
      },
    );
  }

  const system = await context.resolve(SystemRepo).getSystem(setupState.ownerId, setupState.systemKey);

  return Response.json(
    {
      ...setupState,
      ...system,
    },
    {
      headers: {
        'Access-Control-Allow-Origin': 'https://extensions.shopifycdn.com',
      },
    },
  );
}
