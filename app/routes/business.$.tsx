import { BUSINESS_MODEL } from '@/business/core/constants/config';
import { BuilderHandler } from '@/business/handlers/builder.handler';
import Builder from '@/components/Builder';
import { generatePageTitle } from '@/lib/utils';
import { LoaderFunctionArgs, MetaFunction } from 'react-router';
import { HeadersFunction, useLoaderData } from 'react-router';

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [{ title: generatePageTitle(data?.page?.data?.title) }];
};

// export const headers: HeadersFunction = () =>
//   new Headers({
//     'Oxygen-Cache-Control': 'public, max-age=3600, s-maxage=7200, stale-while-revalidate=600',
//     Vary: 'Accept-Encoding, Sec-Ch-Ua-Mobile',
//   });

export const loader = async ({ request, context }: LoaderFunctionArgs) => {
  const builderHandler = context.resolve(BuilderHandler);

  const page = await builderHandler.handlePageFetch({
    request,
    model: BUSINESS_MODEL,
    pathnamePrefix: '/business',
  });

  return { page };
};

// Define and render the page.
export default function Page() {
  // Use the useLoaderData hook to get the Page data from `loader` above.
  const { page } = useLoaderData<typeof loader>();

  // Render the page content from Builder.io
  return <Builder model={BUSINESS_MODEL} content={page as any} />;
}
