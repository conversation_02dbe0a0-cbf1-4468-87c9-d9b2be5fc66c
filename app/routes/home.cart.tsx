import { CustomerAccountClient } from '@/business/clients/accounts-client';
import { PERSONAL_HEADER_MODEL } from '@/business/core/constants/config';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { BuilderHandler } from '@/business/handlers/builder.handler';
import { CartHandler } from '@/business/handlers/cart.handler';
import Builder from '@/components/Builder';
import CartMain from '@/components/Cart';
import { TooltipProvider } from '@/components/ui/tooltip';
import { generatePageTitle } from '@/lib/utils';
import { LoaderFunctionArgs, type ActionFunctionArgs } from 'react-router';
import { HeadersFunction, useLoaderData, type MetaFunction } from 'react-router';

export const meta: MetaFunction = () => {
  return [{ title: generatePageTitle('Cart') }];
};

export const headers: HeadersFunction = ({ actionHeaders }) => actionHeaders;

export async function action({ context }: ActionFunctionArgs) {
  return context.resolve(CartHandler).handleAction('personal');
}

export async function loader({ context, request }: LoaderFunctionArgs) {
  const { searchParams } = new URL(request.url);

  const customerAccountClient = context.resolve(CustomerAccountClient);
  const cartHandler = context.resolve(CartHandler);
  const authHandler = context.resolve(AuthHandler);
  const builderHandler = context.resolve(BuilderHandler);

  if (searchParams.get('checkout') === 'true') {
    return await cartHandler.checkoutCart('personal', true);
  }

  const accessTokenPromise = customerAccountClient.getAccessToken();
  const cart = cartHandler.getCartForDisplay(false);

  const customerId = authHandler.getLoggedInCustomerId().then(customerId => customerId || null);
  const systems = customerId.then(customerId => (customerId ? cartHandler.getOwnerSystems(customerId) : []));

  const header = await builderHandler.handleModelFetch({ request, model: PERSONAL_HEADER_MODEL });

  return {
    accessToken: await accessTokenPromise,
    cart: await cart,
    systems,
    customerId,
    header,
  };
}

export default function CartPage() {
  const { header } = useLoaderData<typeof loader>();

  return (
    <TooltipProvider delayDuration={0}>
      <Builder content={header as any} model={PERSONAL_HEADER_MODEL} />
      <div className="md:h-[calc(100vh-144px)]">
        <CartMain />
      </div>
    </TooltipProvider>
  );
}
