import { MetafieldRepo } from '@/business/core/repositories/metafield';
import { ShopRepo } from '@/business/core/repositories/shop';
import { ActionFunctionArgs } from 'react-router';
import { FieldErrors } from 'react-hook-form';
import { getValidatedFormData } from 'remix-hook-form';
import { resolver, SchemaType } from './resolver';

type Coverage = {
  zip: string;
  carrier: 'verizon' | 'att' | null;
  coverage: number;
};

export type CoverageResponse = {
  success?: boolean;
  data?: Coverage;
  errors?: FieldErrors<SchemaType>;
};

export async function action({ request, context }: ActionFunctionArgs): Promise<CoverageResponse> {
  const { data: formData, errors } = await getValidatedFormData<SchemaType>(request, resolver);
  const metafieldRepo = context.resolve(MetafieldRepo);
  const shopRepo = context.resolve(ShopRepo);
  const shopId = await shopRepo.getShopID();

  if (errors) return { success: false, errors };
  const { zip } = formData;

  const coverage = (await metafieldRepo.getJSONMetafield(shopId, 'coverage', zip)) as Omit<Coverage, 'zip'>;

  if (!coverage) return { success: false, errors: { zip: { message: 'Zip code not found', type: 'validate' } } };

  return { success: true, data: { ...coverage, zip } };
}
