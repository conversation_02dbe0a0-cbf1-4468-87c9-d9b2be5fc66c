import { PERSONAL_MODEL } from '@/business/core/constants/config';
import { BuilderHandler } from '@/business/handlers/builder.handler';
import Builder from '@/components/Builder';
import { generatePageTitle } from '@/lib/utils';
import { HeadersFunction, MetaFunction, useLoaderData } from 'react-router';
import { LoaderFunctionArgs } from 'react-router';

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [{ title: generatePageTitle(data?.page?.data?.title) }];
};

// export const headers: HeadersFunction = () =>
//   new Headers({
//     'Oxygen-Cache-Control': 'public, max-age=3600, s-maxage=7200, stale-while-revalidate=600',
//     Vary: 'Accept-Encoding, Sec-Ch-Ua-Mobile',
//   });

export const loader = async ({ request, context }: LoaderFunctionArgs) => {
  const builderHandler = context.resolve(BuilderHandler);

  const page = await builderHandler.handlePageFetch({
    request,
    model: PERSONAL_MODEL,
    pathnamePrefix: '/home',
  });

  return { page };
};

export default function Page() {
  const { page } = useLoaderData<typeof loader>();

  return <Builder model={PERSONAL_MODEL} content={page as any} />;
}
