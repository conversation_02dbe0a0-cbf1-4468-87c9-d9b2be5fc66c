import { ResponsiveDataTable } from '@/components/ResponsiveDataTable';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { ADCLoginRole } from '@/business/core/types/adc';
import { useLoaderData } from 'react-router';
import { loader } from '../loader';

export function UsersCard() {
  const { users } = useLoaderData<typeof loader>();

  return (
    <Card>
      <ResponsiveDataTable
        rows={users.map(user => ({
          Login: user.loginName,
          Email: user.loginEmail,
          Name: ((user.profileFirstName || '') + ' ' + (user.profileLastName || ''))?.trim() || '-',
          Role: <Badge variant="default">{user.roles.map(role => ADCLoginRole[role]).join(', ')}</Badge>,
        }))}
      />
    </Card>
  );
}
