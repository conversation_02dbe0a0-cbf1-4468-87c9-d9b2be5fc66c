import { ADCRepo } from '@/business/core/repositories/adc';
import { AccountHandler } from '@/business/handlers/account.handler';
import { LoaderFunctionArgs } from 'react-router';

export async function loader({ context }: LoaderFunctionArgs) {
  const { system } = await context.resolve(AccountHandler).validateSystemAccess();

  const users = await context.resolve(ADCRepo).getCustomerLogins(system.adcCustomerId);

  return { users };
}
