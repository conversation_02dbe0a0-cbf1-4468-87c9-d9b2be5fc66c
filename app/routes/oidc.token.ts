import { Auth<PERSON><PERSON><PERSON>, OIDCTokenParams, OIDCTokenResponse } from '@/business/handlers/auth.handler';
import { ActionFunctionArgs } from 'react-router';

export async function action({ request, context }: ActionFunctionArgs) {
  const body = await request.formData();
  const params: OIDCTokenParams = {
    code: body.get('code')?.toString()!,
    refreshToken: body.get('refresh_token')?.toString()!,
    redirectUri: body.get('redirect_uri')?.toString()!,
    grantType: body.get('grant_type')?.toString()!,
    authorization: request.headers.get('Authorization') || '',
  };

  const tokenResponse: OIDCTokenResponse = await context.resolve(AuthHandler).handleTokenRequest(params);

  return Response.json(tokenResponse);
}
