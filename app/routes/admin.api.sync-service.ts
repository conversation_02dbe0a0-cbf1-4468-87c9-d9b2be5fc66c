import { ShopifyClient } from '@/business/clients/shopify-client';
import { SyncService } from '@/business/core/services/sync';
import { ActionFunctionArgs } from 'react-router';

export const action = async ({ request, context }: ActionFunctionArgs) => {
  await context.resolve(ShopifyClient).authenticate.admin(request);

  await context.resolve(SyncService).synchronizeAllSystems();

  return null;
};
