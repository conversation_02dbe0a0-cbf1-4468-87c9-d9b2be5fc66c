import { LoaderFunctionArgs, redirect } from 'react-router';

//TODO: This is a temporary fix to log in b2b customers while the old theme/customer accounts are still live.
// Without this customers would not be logged into checkout.
export async function loader({ context, request }: LoaderFunctionArgs) {
  const env = context.resolve<Env>('env');
  const { searchParams } = new URL(request.url);
  const returnTo = searchParams.get('return_to');

  const accountRedirectUrl = `${env.PUBLIC_CUSTOMER_ACCOUNT_API_URL}/account/locations?${new URLSearchParams({
    return_to: env.HOST + returnTo,
  })}`;

  return redirect(accountRedirectUrl);
}
