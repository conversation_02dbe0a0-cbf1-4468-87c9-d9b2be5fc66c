import { ShopifyClient } from '@/business/clients/shopify-client';
import { Outlet, useLoaderData, useRouteError } from 'react-router';
import { HeadersFunction, LoaderFunctionArgs } from 'react-router';
import polarisStyles from '@shopify/polaris/build/esm/styles.css?url';
import polarisTranslations from '@shopify/polaris/locales/en.json';
import { AppProvider } from '@brandboostinggmbh/shopify-app-react-router/react';
import { boundary } from '@brandboostinggmbh/shopify-app-react-router/server';

export const links = () => [{ rel: 'stylesheet', href: polarisStyles }];

export const loader = async ({ request, context }: LoaderFunctionArgs) => {
  await context.resolve(ShopifyClient).authenticate.admin(request);

  const env = context.resolve<Env>('env');

  return { apiKey: env.SHOPIFY_API_KEY || '', polarisTranslations };
};

export default function App() {
  const { apiKey, polarisTranslations } = useLoaderData<typeof loader>();

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey} i18n={polarisTranslations}>
      <Outlet />
    </AppProvider>
  );
}

// Shopify needs Remix to catch some thrown responses, so that their headers are included in the response.
export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers: HeadersFunction = headersArgs => {
  return boundary.headers(headersArgs);
};
