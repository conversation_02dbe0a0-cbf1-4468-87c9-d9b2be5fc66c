import { SubscriptionRepo } from '@/business/core/repositories/subscription';
import { AccountHandler } from '@/business/handlers/account.handler';
import { paginateStripeList } from '@/lib/utils';
import { LoaderFunctionArgs } from 'react-router';
import { getPaginationVariables } from '@shopify/hydrogen';

export async function loader({ context, request }: LoaderFunctionArgs) {
  const { system } = await context.resolve(AccountHandler).validateSystemAccess();

  const paginationVariables = getPaginationVariables(request, { pageBy: 10 });
  const subscriptionRepo = context.resolve(SubscriptionRepo);

  const invoices = await paginateStripeList(paginationVariables, stripeQuery =>
    subscriptionRepo.getPastInvoices(system.subscriptionId!, stripeQuery),
  );

  return { invoices };
}
