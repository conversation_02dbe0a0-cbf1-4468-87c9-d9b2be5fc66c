import { ResponsiveDataTable } from '@/components/ResponsiveDataTable';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { centsToDollars, formatMoney, formatStripeDate } from '@/lib/utils';
import { Link, useLoaderData } from 'react-router';
import { Pagination } from '@shopify/hydrogen';
import { ArrowUpRight, ChevronLeft, ChevronRight } from 'lucide-react';
import type { loader } from '../loader';
import { Badge } from '@/components/ui/badge';

export function HistoryCard() {
  const { invoices } = useLoaderData<typeof loader>();

  return (
    <Card>
      {!invoices?.nodes?.length ? (
        <p>There are no invoices for this system yet.</p>
      ) : (
        <Pagination connection={invoices}>
          {({ nodes, hasNextPage, hasPreviousPage, isLoading, NextLink, PreviousLink }) => (
            <>
              <ResponsiveDataTable
                labelClassNames={{
                  Link: 'w-16',
                }}
                rows={nodes.map(invoice => ({
                  Date: formatStripeDate(invoice.effective_at || invoice.created, {}),
                  Amount: formatMoney(centsToDollars(invoice.total)),
                  Status: (
                    <Badge variant={invoice.status == 'paid' ? 'success' : 'outline'}>
                      {invoice.status?.toUpperCase()}
                    </Badge>
                  ),
                  Link: invoice.hosted_invoice_url ? (
                    <Button variant="link" size="sm" className="h-4 p-0" asChild>
                      <Link to={invoice.hosted_invoice_url} target="_blank">
                        View Invoice <ArrowUpRight />
                      </Link>
                    </Button>
                  ) : (
                    'Processing...'
                  ),
                }))}
              />
              <div className="flex items-center justify-end">
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-r-none"
                  disabled={!hasPreviousPage || isLoading}
                  asChild
                >
                  <PreviousLink>
                    <ChevronLeft />
                  </PreviousLink>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-l-none"
                  disabled={!hasNextPage || isLoading}
                  asChild
                >
                  <NextLink>
                    <ChevronRight />
                  </NextLink>
                </Button>
              </div>
            </>
          )}
        </Pagination>
      )}
    </Card>
  );
}
