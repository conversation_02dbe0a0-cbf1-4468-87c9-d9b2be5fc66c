import { SessionClient } from '@/business/clients/session-client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, OIDCAuthParams } from '@/business/handlers/auth.handler';
import AuthHeader from '@/components/Header/AuthHeader';
import Icon from '@/components/Icon';
import { OTPField } from '@/components/OTPField';
import { SeparatorWithText } from '@/components/SeparatorWithText';
import StepLayout from '@/components/StepLayout';
import TextField from '@/components/TextField';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { createWizardFormContext, createWizardStepsContext } from '@/components/Wizard/ctx-factory';
import { useOTP } from '@/hooks/use-otp';
import { Link } from 'react-router';
import { LoaderFunctionArgs } from 'react-router';
import { Loader2 } from 'lucide-react';

export async function loader({ context, request }: LoaderFunctionArgs) {
  const { searchParams } = new URL(request.url);

  const params: OIDCAuthParams = {
    nonce: searchParams.get('nonce')!,
    state: searchParams.get('state')!,
    clientId: searchParams.get('client_id')!,
    redirectUrl: searchParams.get('redirect_uri')!,
    responseType: searchParams.get('response_type')!,
    scope: searchParams.get('scope')!,
  };

  // console.log('oidcAuth', JSON.stringify(context.resolve(SessionClient).get('customerAccount')));

  const result = await context.resolve(AuthHandler).handleOIDCAuth(params);

  return result;
}

const { WizardFormProvider: LoginFormProvider, useWizardForm: useLoginForm } = createWizardFormContext<{
  email?: string;
}>();

const {
  WizardStep: LoginStep,
  WizardStepsProvider: LoginStepsProvider,
  useWizardSteps: useLoginSteps,
} = createWizardStepsContext();

export default function Page() {
  return (
    <TooltipProvider delayDuration={0}>
      <LoginFormProvider>
        <AuthHeader />
        <main className={'relative flex h-[80vh] w-full items-center justify-center'}>
          <LoginSteps />
        </main>
      </LoginFormProvider>
    </TooltipProvider>
  );
}

function LoginSteps() {
  return (
    <LoginStepsProvider steps={['email', 'code', 'complete']}>
      <LoginStep name="email">
        <EmailStep />
      </LoginStep>
      <LoginStep name="code">
        <CodeStep />
      </LoginStep>
      <LoginStep name="complete">
        <div className="relative flex h-40 w-full flex-col items-center justify-center gap-1">
          <p className="font-semibold text-muted-foreground">Logging in...</p>
          <Loader2 className="size-16 animate-spin text-primary" />
        </div>
      </LoginStep>
    </LoginStepsProvider>
  );
}

function EmailStep() {
  const { commit } = useLoginForm();
  const { nextStep } = useLoginSteps();

  const { form } = useOTP({
    email: '',
    blockUnknown: true,
    onSuccess: () => {
      commit({ email: form.getValues('email') });
      nextStep();
    },
  });

  return (
    <Form form={form}>
      <StepLayout
        title="Log into Your Account"
        description="Manage your subscription, setup devices, and see order history."
      >
        <div className="mb-6 flex flex-col justify-start gap-2">
          <TextField name="email" autoComplete="email" placeholder="Enter your email" />
        </div>
        <Button
          type="submit"
          variant="primary"
          size="full"
          disabled={!form.getValues('email')}
          loading={form.formState.isSubmitting || form.formState.isLoading}
        >
          Send Login Code
        </Button>
        <SeparatorWithText text="OR" className="mt-8 mb-6" />
        <Button variant="link" size="full" asChild className="text-xs font-normal text-body-normal">
          <Link to="/app-login">
            <img src="/images/app-launch-icon.png" alt="Google" className="h-6 w-6 text-xs" />
            Switch to All Aware App
            <Tooltip>
              <TooltipTrigger>
                <Icon icon="info" className="h-4 w-4" />
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-background">
                <p className="text-xs font-normal text-body-normal">
                  Log into the All Aware App Manager to setup and manage your subscription, devices, and see order
                  history.
                </p>
              </TooltipContent>
            </Tooltip>
          </Link>
        </Button>
      </StepLayout>
    </Form>
  );
}

function CodeStep() {
  const {
    formData: { email },
  } = useLoginForm();
  const { nextStep, prevStep } = useLoginSteps();
  const { form } = useOTP({
    onSuccess: nextStep,
  });

  const submitting = form.formState.isSubmitting || form.formState.isLoading;
  return (
    <StepLayout
      title={`Enter the 6-digit code to continue`}
      description={
        <>
          Please enter the 6 digit code sent to the email <b>{email}</b>. This code may take a moment to arrive and will
          expire in 15 minutes.
        </>
      }
    >
      <Form form={form}>
        <div className="mb-6 flex flex-col justify-center space-y-2">
          <OTPField name="OTP" containerClassName="justify-center" resendEmail={email} disabled={submitting} />
        </div>
        <div className="flex flex-col gap-2">
          <Button type="submit" variant="primary" size="full" loading={submitting}>
            Login with Code
          </Button>
          <Button variant="outline" type="button" size="full" className="text-xs" onClick={prevStep}>
            Back
          </Button>
        </div>
      </Form>
    </StepLayout>
  );
}
