import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

export const GENERIC_EMAIL_ERROR = 'Invalid email address';

export const otpSchema = z.string({ coerce: true }).regex(/^\d{6}$/, 'OTP must be exactly 6 digits');

export const schema = z
  .object({
    email: z.string().email(GENERIC_EMAIL_ERROR).optional(),
    blockUnknown: z.boolean().optional(),
    OTP: otpSchema.optional(),
    customerId: z.string().optional(),
    returnTo: z.string().optional(),
  })
  .refine(data => data.email !== undefined || data.OTP !== undefined || data.customerId !== undefined, {
    message: 'At least one of email, otp, or customerId must be provided',
  });

export type SchemaType = z.infer<typeof schema>;

export const resolver = zodResolver(schema);
