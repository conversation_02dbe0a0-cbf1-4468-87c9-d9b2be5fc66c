import { CustomerRepo } from '@/business/core/repositories/customer';
import { AuthHandler } from '@/business/handlers/auth.handler';
import { convertIdToGid } from '@/lib/utils';
import { LoaderFunctionArgs, redirect } from 'react-router';
import { getValidatedFormData } from 'remix-hook-form';
import { GENERIC_EMAIL_ERROR, resolver, SchemaType } from './resolver';

export async function action({ request, context }: LoaderFunctionArgs) {
  const { data, errors } = await getValidatedFormData<SchemaType>(request, resolver);

  if (errors) return { errors };

  let email = data?.email || null;
  const customerId = convertIdToGid('Customer', data?.customerId);

  const authHandler = context.resolve(AuthHandler);
  const customerRepo = context.resolve(CustomerRepo);

  if (customerId) {
    email = (await customerRepo.getCustomerEmailFromGid(customerId!)) || null;
  }

  if (email) {
    if (data?.blockUnknown) {
      const customer = await customerRepo.getCustomerIdFromEmail(email!);
      if (!customer) return { errors: { email: { message: GENERIC_EMAIL_ERROR } } };
    }

    const send = await authHandler.sendOTP(email!);

    return 'error' in send ? { errors: { email: { message: send?.error } } } : { sent: true };
  }

  if (data?.OTP) {
    const { success, error } = await authHandler.verifyOTP(data.OTP!);

    if (!success || error) return { errors: { OTP: { message: error || 'Invalid OTP' } } };

    if (data.returnTo) throw redirect(`/account/login?${new URLSearchParams({ return_to: data.returnTo })}`);
  }

  return null;
}
