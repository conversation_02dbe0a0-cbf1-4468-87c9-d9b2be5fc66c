import { CustomerAccountClient } from '@/business/clients/accounts-client';
import { LoaderFunctionArgs, redirect } from 'react-router';

export async function loader({ context }: LoaderFunctionArgs) {
  const customerAccount = context.resolve(CustomerAccountClient);
  await customerAccount.handleAuthStatus();

  const env = context.resolve<Env>('env');

  return redirect(`${env.PUBLIC_CUSTOMER_ACCOUNT_API_URL}/account/orders`);
}
