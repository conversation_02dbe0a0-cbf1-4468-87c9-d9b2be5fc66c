import HorizontalScrollJack from '@/components/HorizontalScrollJack';

export default function Tests() {
  return (
    <>
      <div className="h-svh w-full bg-slate-500"></div>
      <HorizontalScrollJack align="bottom">
        <div className="aspect-square h-full max-h-[60vh] max-w-[60vw] bg-blue-500"></div>
        <div className="aspect-square h-full max-h-[60vh] max-w-[60vw] bg-blue-400"></div>
        <div className="aspect-square h-full max-h-[60vh] max-w-[60vw] bg-blue-500"></div>
        <div className="aspect-square h-full max-h-[60vh] max-w-[60vw] bg-blue-400"></div>
        <div className="aspect-square h-full max-h-[60vh] max-w-[60vw] bg-blue-500"></div>
      </HorizontalScrollJack>
      <div className="h-svh w-full bg-slate-500">
        <div className="aspect-square h-full max-h-[60vh] max-w-[60vw] bg-red-600"></div>
      </div>
    </>
  );
}
