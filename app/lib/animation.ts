export function initializeAnimation() {
  const viewedElements: Set<Element> = new Set();
  const entryObserver = new IntersectionObserver(
    entries =>
      entries.forEach(entry => {
        if (viewedElements.has(entry.target)) return;
        if (entry.isIntersecting) {
          entry.target.classList.remove('out-of-view');
          viewedElements.add(entry.target);
        } else {
          entry.target.classList.add('out-of-view');
        }
      }),
    {
      rootMargin: '0% 0px 15% 0px',
    },
  );

  const animatedElements = document.querySelectorAll('.animate-in-view');
  animatedElements.forEach(element => entryObserver.observe(element));

  // const mutationObserver = new MutationObserver(mutations => {
  //   mutations.forEach(mutation => {
  //     mutation.addedNodes.forEach(node => {
  //       if (node instanceof Element && node.classList.contains('animate-in-view')) {
  //         console.info('added', node);
  //         entryObserver.observe(node);
  //         exitObserver.unobserve(node);
  //       }
  //     });
  //     mutation.removedNodes.forEach(node => {
  //       if (node instanceof Element && node.classList.contains('animate-in-view')) {
  //         console.info('removed', node);
  //         exitObserver.unobserve(node);
  //         entryObserver.unobserve(node);
  //       }
  //     });
  //   });
  // });
  // mutationObserver.observe(document.body, { childList: true, subtree: true });
}
