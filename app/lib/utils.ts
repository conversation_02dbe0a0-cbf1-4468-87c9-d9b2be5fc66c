import { ADDON_SYSTEM_FEATURES } from '@/business/core/constants/features';
import { AddonQuantities, FeatureQuantities, GIDTypes, PaginationVariables } from '@/business/core/types';
import { ADCFeature } from '@/business/core/types/adc';
import { ADCAddon } from '@/business/core/types/addons';
import { SystemDevice } from '@/business/core/types/device';
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { z } from 'zod';

type QuantityRecord = Record<string | number, number>;
/**
 * Sums all values in a quantity record
 */
export const sumQuantities = <T extends QuantityRecord>(record: T): number => {
  let sum = 0;
  for (const key in record) {
    sum += record[key];
  }
  return sum;
};
/**
 * Check if quantities has value greater than zero
 */
export const hasQuantities = <T extends QuantityRecord>(record: T) => {
  return Object.entries(record).some(([_, qty]) => qty);
};
/**
 * Adds a quantity to a specific key in the record, creating it if it doesn't exist
 */
export const addQuantityToKey = <T extends QuantityRecord>(
  record: T,
  key: keyof T | string | number,
  amount: number,
): T => {
  const result = { ...record } as Record<string, number>;
  result[key as string] = (result[key as string] || 0) + (amount || 0);
  return cleanQuantities(result as T);
};

/**
 * Adds two quantity records together
 */
export const addQuantities = <T extends QuantityRecord>(a: T, b: Partial<T>): T => {
  const result = { ...a } as Record<string, number>;

  for (const key in b) {
    result[key] = (result[key] || 0) + (b[key] || 0);
  }

  return cleanQuantities(result as T);
};

/**
 * Subtracts one quantity record from another
 */
export const subtractQuantities = <T extends QuantityRecord>(a: T, b: Partial<T>): T => {
  const result = { ...a } as Record<string, number>;

  for (const key in b) {
    result[key] = (result[key] || 0) - (b[key] || 0);
  }

  return cleanQuantities(result as T);
};

/**
 * Multiplies all values in a quantity record by a scalar
 */
export const multiplyQuantities = <T extends QuantityRecord>(record: T, scalar: number): T => {
  const result = { ...record } as Record<string, number>;

  for (const key in record) {
    result[key] = record[key] * scalar;
  }

  return cleanQuantities(result as T);
};

export const ceilDivideQuantities = <T extends QuantityRecord>(numerator: T, denominator: Partial<T>): number => {
  let divisor = 0;

  for (const key in denominator) {
    if (!denominator[key]) continue;
    divisor = Math.max(divisor, Math.ceil((numerator[key] || 0) / denominator[key]));
  }

  return divisor;
};

/**
 * Creates a new record with the minimum values from both inputs
 */
export const minQuantities = <T extends QuantityRecord>(a: T, b: Partial<T>): T => {
  const result = {} as Record<string, number>;

  // Check all keys in a
  for (const key in { ...a, ...b }) {
    result[key] = Math.min(a[key] || 0, b[key] || 0);
  }

  return cleanQuantities(result as T);
};

/**
 * Creates a new record with the maximum values from both inputs
 */
export const maxQuantities = <T extends QuantityRecord>(a: T, b: Partial<T>): T => {
  const result = {} as Record<string, number>;

  // Check all keys in a
  for (const key in { ...a, ...b }) {
    result[key] = Math.max(a[key] || 0, b[key] || 0);
  }

  return cleanQuantities(result as T);
};

export const filterQuantities = <T extends QuantityRecord>(
  a: T,
  filter: (key: keyof T, quantity: number) => boolean,
) => {
  const result = {} as Record<string, number>;

  for (const key in a) {
    const quantity = a[key];

    if (filter(key, quantity)) result[key] = quantity;
  }

  return result as Partial<T>;
};

/**
 * Removes all zero or falsy values from a quantity record
 */
export const cleanQuantities = <T extends QuantityRecord>(record: T): T => {
  const result = { ...record } as Record<string, number>;

  for (const key in result) {
    if (!result[key]) delete result[key];
  }

  return result as T;
};

export const getSystemOwnerType = (ownerId?: string): SystemType => {
  if (!ownerId) return 'personal';
  return ownerId.startsWith('gid://shopify/Customer') ? 'personal' : 'business';
};

export const convertDevicesToFeatureQuantities = (devices: SystemDevice[]) =>
  devices.reduce<FeatureQuantities>((totalFeatures, device) => addQuantityToKey(totalFeatures, device.handle, 1), {});

export const convertADCFeaturesToAddonQuantities = (adcFeatures: ADCFeature[]) =>
  Object.fromEntries(adcFeatures.map(({ feature, maxQuantity }) => [feature, maxQuantity])) as AddonQuantities;

export const convertAddonQuantitiesToFeatureQuantities = (addons: AddonQuantities) =>
  Object.entries(addons).reduce<FeatureQuantities>(
    (totalFeatures, [addonId, addonQty]) =>
      addQuantities(
        totalFeatures,
        multiplyQuantities(ADDON_SYSTEM_FEATURES[parseInt(addonId) as ADCAddon] || {}, addonQty),
      ),
    {},
  );

export const covertAddonQuantitiesToAddonIds = (addonQuantities: AddonQuantities) =>
  Object.entries(addonQuantities).flatMap(([addonIdStr, quantity]) =>
    new Array<number>(quantity).fill(parseInt(addonIdStr)),
  );

export const convertAddonIdsToAddonQuantities = (addonIds: number[]) =>
  addonIds.reduce<AddonQuantities>((quantities, addonId) => addQuantityToKey(quantities, addonId, 1), {});

export function getRedirectUrlFromRequest(request: Request, fallback: string) {
  const url = new URL(request.url);
  const redirectTo = url.searchParams.get('return_to') || url.searchParams.get('redirect_url') || fallback;

  const r = new RegExp('^(?:[a-z+]+:)?//', 'i');
  if (r.test(redirectTo)) return fallback;

  return redirectTo;
}

export function generatePageTitle(...parts: (undefined | string | string[])[]) {
  let title = 'All Aware';

  const prefix = parts
    .flat()
    .filter(part => !!part)
    .join(' ');
  if (prefix) title = prefix + ` | ${title}`;

  return title;
}

export function generateImageSrcSet(url: string, sizes = [100, 200, 400, 800, 1200, 1600, 2000]) {
  return sizes.map(size => `${url}?width=${size} ${size}w`).join(', ');
}

/** Tailwind */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
/** Tailwind */

/** Math */
export const lerp = (from: number, to: number, t: number) => from + (to - from) * t;
export const clamp01 = (t: number) => Math.min(1, Math.max(0, t));
export const clamp = (t: number, min: number, max: number) => Math.min(max, Math.max(min, t));
/** Math */

/** Graphql */
export function convertGidToId(gid: string | undefined): number {
  if (!gid) return -1;
  const pos = gid.lastIndexOf('/');
  if (pos >= 0) return parseInt(gid.substring(pos + 1));

  return -1;
}

export const convertIdToGid = (type: GIDTypes, id?: string | number | null) =>
  id ? `gid://shopify/${type}/${id}` : undefined;

export const gidRegex = (type: GIDTypes) => new RegExp(`^gid://shopify/${type}/[0-9]{5,15}$`, 's');
export const gidSchema = (type: GIDTypes, message?: string) =>
  z.string().regex(gidRegex(type), message || `Invalid ${type} ID`);

export const sanitizePhone = (phone: string) => {
  const removed = phone?.replace(/[^\d]/g, '')?.slice(-10);

  if (removed?.length != 10) return undefined;

  return '+1' + removed;
};

export const queryAllConnections = async <N>(
  query: (
    cursor?: string | null,
  ) => Promise<{ nodes: N[]; pageInfo?: { hasNextPage: boolean; endCursor?: string | undefined | null } }>,
) => {
  const nodes: N[] = [];

  let data: Awaited<ReturnType<typeof query>> | undefined = undefined;
  do {
    data = await query(data?.pageInfo?.endCursor);

    nodes.push(...(data?.nodes || []));
  } while (data?.pageInfo?.hasNextPage);

  return nodes;
};

export function getUrlSearchParam(url: string, param: string) {
  const { searchParams } = new URL(url);
  return searchParams.get(param);
}

export function setUrlSearchParam(url: string, param: string, value: string) {
  const updatedUrl = new URL(url);
  updatedUrl.searchParams.set(param, value);
  return updatedUrl;
}

export const centsToDollars = (cents: number | undefined | null) => ((cents || 0) * 1.0) / 100.0;

const moneyFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
});

export const formatMoney = (dollars: number) => moneyFormatter.format(dollars * 1.0).replace(/(\.|,)00$/g, '');

export const stripeDate = (timestamp: number) => new Date(timestamp * 1000);
export const formatStripeDate = (
  timestamp: number,
  options?: Intl.DateTimeFormatOptions,
  locales?: Intl.LocalesArgument,
) => stripeDate(timestamp).toLocaleDateString(locales || 'en-US', options || { month: 'long', day: 'numeric' });

export const hashSHA256 = async (input: string) =>
  String.fromCharCode(...new Uint8Array(await crypto.subtle.digest('SHA-256', new TextEncoder().encode(input))));

export function partition<T>(
  array: T[],
  filter: (element: T, index: number, array: T[]) => boolean,
): [pass: T[], fail: T[]] {
  const pass: T[] = [];
  const fail: T[] = [];
  for (let i = 0; i < array.length; i++) {
    const element = array[i];
    if (filter(element, i, array)) pass.push(element);
    else fail.push(element);
  }
  return [pass, fail];
}

export function matchArray<MatchItem, SourceItem, DestinationItem>(
  source: SourceItem[],
  destination: DestinationItem[],
  updateItem: (sourceItem: SourceItem, destinationItem: DestinationItem, index: number) => MatchItem,
  addItem: (item: DestinationItem, index: number) => MatchItem,
  removeItem: (item: SourceItem, index: number) => MatchItem,
  skip?: (sourceItem: SourceItem, destinationItem: DestinationItem, index: number) => boolean,
) {
  const length = Math.max(source.length, destination.length);
  const responses: MatchItem[] = [];
  for (let i = 0; i < length; i++) {
    if (i < destination.length && i < source.length) {
      if (!skip || !skip(source[i], destination[i], i)) {
        responses.push(updateItem(source[i], destination[i], i));
      }
      continue;
    }

    if (i < destination.length) {
      responses.push(addItem(destination[i], i));
      continue;
    }

    responses.push(removeItem(source[i], i));
  }

  return responses;
}

export function toOrdinal(i: number) {
  const j = i % 10,
    k = i % 100;
  if (j === 1 && k !== 11) {
    return i + 'st';
  }
  if (j === 2 && k !== 12) {
    return i + 'nd';
  }
  if (j === 3 && k !== 13) {
    return i + 'rd';
  }
  return i + 'th';
}

export function extractSearchParams<Schema extends { [key: string]: 'optional' | 'required' }>(
  request: Request,
  schema: Schema,
): { [key in keyof Schema as Schema[key] extends 'required' ? key : never]: string } & {
  [key in keyof Schema as Schema[key] extends 'optional' ? key : never]?: string;
} {
  const { searchParams } = new URL(request.url);
  const result: any = {};

  for (const param in schema) {
    const value = searchParams.get(param);
    if (schema[param] === 'required' && !value) {
      throw new Error(`Missing required parameter: ${param}`);
    }
    if (value) {
      result[param] = value;
    }
  }

  return result;
}

export function parseSearchParams<T extends z.ZodTypeAny>(request: Request, schema: T) {
  const data = Object.fromEntries(new URL(request.url).searchParams.entries());
  return schema.parse(data) as z.infer<T>;
  //                        ^^^^^^^^^^^^^^ <- add this
}
/** Graphql */

/**
 * Validates a HMACSHA512 signature against a webhook body and secret.
 * @param body - Raw request body (as a string).
 * @param secret - Shared secret key (as a string).
 * @param signatureHeader - Received signature header (hex-encoded).
 * @returns Promise<boolean> - True if the signature is valid.
 */
export async function verifyHMACSHA512Signature(
  body: string,
  secret: string,
  signatureHeader: string,
): Promise<boolean> {
  const encoder = new TextEncoder();

  // 1. Import the secret key
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-512' },
    false,
    ['sign'], // Only "sign" is needed for validation
  );

  // 2. Generate HMAC from the body
  const generatedSignature = await crypto.subtle.sign('HMAC', key, encoder.encode(body));

  // 3. Decode received signature (handle hex encoding)
  let receivedSignature: ArrayBuffer;
  try {
    receivedSignature = hexToBuffer(signatureHeader);
  } catch (error) {
    console.error('Invalid signature header:', error);
    return false;
  }

  // 4. Compare signatures securely
  return constantTimeCompare(generatedSignature, receivedSignature);
}

// Helper to convert hex string to ArrayBuffer
export function hexToBuffer(hex: string): ArrayBuffer {
  if (hex.length % 2 !== 0) {
    throw new Error('Hex string must have even length');
  }

  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
  }
  return bytes.buffer;
}

// Helper to convert base64 string to ArrayBuffer
export function base64ToBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

// Constant-time comparison to prevent timing attacks
export function constantTimeCompare(a: ArrayBuffer, b: ArrayBuffer): boolean {
  const aBytes = new Uint8Array(a);
  const bBytes = new Uint8Array(b);

  if (aBytes.length !== bBytes.length) return false;

  let result = 0;
  for (let i = 0; i < aBytes.length; i++) {
    result |= aBytes[i] ^ bBytes[i];
  }
  return result === 0;
}

export async function paginateStripeList<T extends { id: string }>(
  pageQuery: PaginationVariables,
  request: (stripeParams: {
    limit?: number;
    starting_after?: string;
    ending_before?: string;
  }) => Promise<{ data: T[]; has_more: boolean }>,
) {
  let pageSize = 10;
  let after: string | undefined = undefined;
  let before: string | undefined = undefined;
  const forward = 'first' in pageQuery;
  if (forward) {
    pageSize = pageQuery.first;
    after = pageQuery.endCursor || undefined;
  } else {
    pageSize = pageQuery.last;
    before = pageQuery.startCursor || undefined;
  }

  const response = await request({ limit: pageSize, starting_after: after, ending_before: before });

  const nodes = forward ? response.data : response.data.reverse();

  const shopifyStyleResponse = {
    nodes,
    pageInfo: forward
      ? {
          hasNextPage: response.has_more,
          hasPreviousPage: !!after,
          endCursor: nodes.at(-1)?.id,
          startCursor: nodes.at(0)?.id,
        }
      : {
          hasPreviousPage: response.has_more,
          hasNextPage: !!before,
          endCursor: nodes.at(-1)?.id,
          startCursor: nodes.at(0)?.id,
        },
  };

  return shopifyStyleResponse;
}

export function getCookie(name: string) {
  if (typeof document == 'undefined') return null;
  const cookies = document.cookie.split('; ');
  const cookie = cookies.find(c => c.startsWith(name + '='));
  return cookie ? cookie.split('=')[1] : null;
}

export function setCookie(name: string, value: string, days: number) {
  let expires = '';
  if (days) {
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    expires = '; expires=' + date.toUTCString();
  }
  document.cookie = name + '=' + value + expires + '; path=/';
}

export function deleteCookie(name: string) {
  document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
}
