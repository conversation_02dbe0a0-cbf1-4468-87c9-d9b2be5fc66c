import { CART_QUERY_FRAGMENT } from '@/business/handlers/constants/fragments';
import { createHydrogenContext, createWithCache, createStorefrontClient, InMemoryCache } from '@shopify/hydrogen';
import { container } from 'tsyringe';
import { SessionClient } from '@/business/clients/session-client';
import { StorefrontClient } from '@/business/clients/storefront-client';
import { CartClient } from '@/business/clients/cart-client';
import { CustomerAccountClient } from '@/business/clients/accounts-client';
import crypto from 'node:crypto';

export async function createAppLoadContext(
  request: Request,
  env: Env,
  executionContext: ExecutionContext,
): Promise<any> {
  /**
   * Create session and storefront client for Node.js environment.
   */
  if (!env?.SESSION_SECRET) {
    throw new Error('SESSION_SECRET environment variable is not set');
  }

  // For Node.js, we use InMemoryCache instead of worker cache
  const cache = new InMemoryCache();
  const waitUntil = executionContext?.waitUntil || (() => {});

  const session = await SessionClient.init(request, [env.SESSION_SECRET]);

  // Create storefront client for Node.js
  const { storefront } = createStorefrontClient({
    cache,
    waitUntil: null, // Not needed in Node.js
    i18n: { language: 'EN', country: 'US' },
    publicStorefrontToken: env.PUBLIC_STOREFRONT_API_TOKEN,
    privateStorefrontToken: env.PRIVATE_STOREFRONT_API_TOKEN,
    storeDomain: env.PUBLIC_STORE_DOMAIN,
    storefrontId: env.PUBLIC_STOREFRONT_ID,
    storefrontHeaders: {
      requestGroupId: crypto.randomUUID(),
      buyerIp: request.headers.get('x-forwarded-for') || '127.0.0.1',
      cookie: request.headers.get('cookie'),
    },
  });

  const withCache = createWithCache({ cache, waitUntil, request });
  const context = createHydrogenContext({
    env,
    request,
    cache,
    waitUntil,
    session,
    storefront,
    i18n: { language: 'EN', country: 'US' },
    cart: {
      queryFragment: CART_QUERY_FRAGMENT,
    },
  });

  const app = container.createChildContainer();
  // app.register('version', { useValue: Math.floor(Math.random() * 100000) });
  app.register('request', { useValue: request });
  app.register('env', { useValue: context.env });
  app.register('waitUntil', { useValue: context.waitUntil });
  app.register('withCache', { useValue: withCache });
  app.register(SessionClient, { useValue: context.session });
  app.register(StorefrontClient, { useValue: new StorefrontClient(context.storefront) });
  app.register(CartClient, { useValue: new CartClient(context.cart) });
  app.register(CustomerAccountClient, { useValue: new CustomerAccountClient(context.customerAccount) });

  return app as AppLoadContext;
}
