import { CART_QUERY_FRAGMENT } from '@/business/handlers/constants/fragments';
import { createHydrogenContext, createWithCache } from '@shopify/hydrogen';
import { AppLoadContext } from '@shopify/remix-oxygen';
import { container } from 'tsyringe';
import { SessionClient } from '@/business/clients/session-client';
import { StorefrontClient } from '@/business/clients/storefront-client';
import { CartClient } from '@/business/clients/cart-client';
import { CustomerAccountClient } from '@/business/clients/accounts-client';

export async function createAppLoadContext(
  request: Request,
  env: Env,
  executionContext: ExecutionContext,
): Promise<AppLoadContext> {
  /**
   * Open a cache instance in the worker and a custom session instance.
   */
  if (!env?.SESSION_SECRET) {
    throw new Error('SESSION_SECRET environment variable is not set');
  }

  const waitUntil = executionContext.waitUntil.bind(executionContext);
  const [cache, session] = await Promise.all([
    caches.open('hydrogen'),
    SessionClient.init(request, [env.SESSION_SECRET]),
  ]);

  const withCache = createWithCache({ cache, waitUntil, request });
  const context = createHydrogenContext({
    env,
    request,
    cache,
    waitUntil,
    session,
    i18n: { language: 'EN', country: 'US' },
    cart: {
      queryFragment: CART_QUERY_FRAGMENT,
    },
  });

  const app = container.createChildContainer();
  // app.register('version', { useValue: Math.floor(Math.random() * 100000) });
  app.register('request', { useValue: request });
  app.register('env', { useValue: context.env });
  app.register('waitUntil', { useValue: context.waitUntil });
  app.register('withCache', { useValue: withCache });
  app.register(SessionClient, { useValue: context.session });
  app.register(StorefrontClient, { useValue: new StorefrontClient(context.storefront) });
  app.register(CartClient, { useValue: new CartClient(context.cart) });
  app.register(CustomerAccountClient, { useValue: new CustomerAccountClient(context.customerAccount) });

  return app as AppLoadContext;
}
