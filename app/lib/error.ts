export class StorefrontError extends Error {
  constructor(message: string) {
    super(message);
  }
}

export async function validateStorefrontResponse<T>(
  request: Promise<T> | (() => Promise<T> | T),
): Promise<{ data?: T; error?: string }> {
  try {
    const response = typeof request == 'function' ? await request() : await request;
    return {
      error: undefined,
      data: response,
    };
  } catch (error) {
    if (error instanceof StorefrontError) {
      console.error(`StorefrontError`, error.stack);
      return {
        error: error.message,
        data: undefined,
      };
    }

    throw error;
  }
}

export async function processStorefrontRequest<T>(
  request: Promise<T> | (() => Promise<T> | T),
  storefrontErrorMessage: string,
) {
  try {
    const response = typeof request == 'function' ? await request() : await request;
    return response;
  } catch (error) {
    if (error instanceof StorefrontError) {
      throw error;
    }

    console.error(error);

    throw new StorefrontError(storefrontErrorMessage);
  }
}

export function throwStorefrontErrorInstead<E>(storefrontErrorMessage: string) {
  return (error: E) => {
    if (error instanceof StorefrontError) {
      throw error;
    }

    console.error(error);

    throw new StorefrontError(storefrontErrorMessage);
  };
}

export const throwGenericStorefrontErrorInstead = throwStorefrontErrorInstead(
  'There was an unexpected error. Please contact support!',
);

export async function expectNonNullable<T>(value: T | undefined) {
  if (!value) throw new Error('Value was undefined');

  return value;
}
