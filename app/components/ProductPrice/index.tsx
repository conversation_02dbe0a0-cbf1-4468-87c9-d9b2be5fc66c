import { usePromiseEffect } from '@/hooks/use-promise-effect';
import { Money } from '@shopify/hydrogen';
import { useProduct } from '../ProductDataProvider/context';
import { Skeleton } from '../ui/skeleton';

interface IProps {
  type: 'price' | 'compareAtPrice' | 'subscription';
  template?: string;
  attributes?: any;
}

export default function ProductPrice({ type, template, attributes }: IProps) {
  const { subscription: subscriptionPromise, selectedVariant } = useProduct();
  const { value: subscription } = usePromiseEffect(subscriptionPromise);

  if (type != 'subscription' && !selectedVariant) return <Skeleton {...attributes} className="size-12" />;

  const price =
    type == 'price'
      ? selectedVariant?.price
      : type == 'compareAtPrice'
        ? selectedVariant?.compareAtPrice
        : subscription;

  if (!price?.amount) return null;

  if (!template) return <Money as="span" {...attributes} data={price} withoutTrailingZeros />;

  return (
    <p {...attributes}>
      {template
        .split('$')
        .flatMap((value, index) =>
          index == 0
            ? [value]
            : [<Money className="inline" key={index} as="span" data={price} withoutTrailingZeros />, value],
        )}
    </p>
  );
}
