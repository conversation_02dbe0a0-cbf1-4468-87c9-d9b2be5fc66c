import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import ProductPrice from '.';

export const PRODUCT_PRICE_COMPONENT: RegisteredComponent = {
  name: 'Product Price',
  component: ProductPrice,
  noWrap: true,
  requiresParent: {
    message: 'Product Price must be inside a Product Data Provider',
    component: 'Product Data Provider',
  },
  defaultStyles: {
    display: 'block',
    margin: '0',
  },
  inputs: [
    {
      name: 'type',
      type: 'string',
      enum: ['price', 'compareAtPrice', 'subscription'],
      defaultValue: 'price',
    },
    {
      name: 'template',
      type: 'string',
      defaultValue: '+ $ monthly subscription',
      helperText: `Use dollar sign character ($) will be replaced by the price.`,
    },
  ],
};
