import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { DialogProps } from '@radix-ui/react-dialog';
import { Drawer as DrawerPrimitive } from 'vaul';
import { Drawer } from '../ui/drawer';
import { Dialog } from '../ui/dialog';

export function Modal(props: React.ComponentProps<typeof DrawerPrimitive.Root> & DialogProps) {
  const isMobile = useIsMobileBreakpoint();
  return isMobile ? <Drawer {...props} /> : <Dialog {...props} />;
}
