import { Textarea } from '@/components/ui/textarea';
import { useRemixFormContext } from 'remix-hook-form';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';

interface TextAreaFieldProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  name: string;
  label?: string;
  placeholder?: string;
  description?: string;
  disabled?: boolean;
  className?: string;
}

export default function TextAreaField({
  name,
  label,
  placeholder,
  description,
  disabled,
  className,
  ...rest
}: TextAreaFieldProps) {
  const form = useRemixFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Textarea
              autoComplete="off"
              disabled={disabled}
              placeholder={placeholder}
              {...field}
              {...rest}
              className={className}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
