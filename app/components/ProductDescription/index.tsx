import HTMLText from '../HTML';
import HTMLReplacer, { HTMLSection } from '../HTMLReplacer';
import Icon from '../Icon';
import { useProduct } from '../ProductDataProvider/context';
import { Skeleton } from '../ui/skeleton';

function UndefinedTag({ afterHtml }: HTMLSection) {
  return <HTMLText className="prose-lg text-body-normal" HTML={afterHtml} />;
}

function LiTag({ innerHtml }: HTMLSection) {
  return (
    <li className="flex gap-1 font-bold text-body-highlight">
      <Icon icon="check" className="size-6 text-emphasis" /> {innerHtml}
    </li>
  );
}

function UlTag(props: HTMLSection) {
  return (
    <>
      <ul className="my-4 grid gap-2 sm:my-6 sm:grid-cols-2">
        <HTMLReplacer HTML={props.innerHtml!} map={{ li: LiTag }} />
      </ul>
      <UndefinedTag {...props} />
    </>
  );
}

function RenderDescription({ afterHtml }: HTMLSection) {
  return <HTMLReplacer HTML={afterHtml} map={{ ul: UlTag, undefined: UndefinedTag }} />;
}

export default function ProductDescription() {
  const { product } = useProduct();

  if (!product)
    return (
      <div className="space-y-1">
        <Skeleton className="h-6 w-[98%]" />
        <Skeleton className="h-6 w-full" />
        <Skeleton className="h-6 w-[95%]" />
        <Skeleton className="h-6 w-2/3" />
      </div>
    );

  return <HTMLReplacer HTML={product?.descriptionHtml} map={{ undefined: RenderDescription, h6: undefined }} />;
}
