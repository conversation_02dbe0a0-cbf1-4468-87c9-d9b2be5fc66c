import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import TextWithReadMore from '.';

export const TEXT_WITH_READ_MORE_COMPONENT: RegisteredComponent = {
  name: 'Text With Read More',
  component: TextWithReadMore,
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  inputs: [
    {
      name: 'text',
      type: 'string',
      required: true,
      defaultValue:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    },
    {
      name: 'maxCharacters',
      type: 'number',
      required: true,
      defaultValue: 150,
    },
    {
      name: 'openDialogInEditor',
      type: 'boolean',
      defaultValue: false,
      helperText: 'If true, the dialog will be open in the editor. This has no effect on the live page.',
    },
    {
      name: 'buttonTextColor',
      type: 'color',
    },
    {
      name: 'blocks',
      type: 'list',
      hideFromUI: true,
      defaultValue: [],
    },
  ],
};
