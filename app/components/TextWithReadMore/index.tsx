import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Blocks, BuilderBlock, isEditing } from '@builder.io/sdk-react/edge';

interface IProps {
  text: string;
  maxCharacters: number;
  builderBlock: BuilderBlock;
  blocks: Builder<PERSON>lock[];
  openDialogInEditor?: boolean;
  buttonTextColor?: string;
}

export default function TextWithReadMore({
  text,
  maxCharacters,
  builderBlock,
  blocks,
  openDialogInEditor,
  buttonTextColor,
}: IProps) {
  const isTruncated = text.length > maxCharacters;
  const displayText = isTruncated ? text.substring(0, maxCharacters) + '...' : text;

  if (openDialogInEditor && isEditing()) {
    return (
      <div className="max-h-[400px] overflow-y-auto sm:max-w-[425px]">
        <Blocks parent={builderBlock?.id} path="blocks" blocks={blocks} className="flex flex-1" />
      </div>
    );
  }

  return (
    <Dialog>
      <p>
        {displayText}
        {isTruncated && (
          <DialogTrigger asChild>
            <Button
              variant="link"
              size="fit"
              className="ml-2 h-auto p-0"
              style={{ fontSize: 'inherit', color: buttonTextColor || 'var(--text-foreground)' }}
            >
              Read More
            </Button>
          </DialogTrigger>
        )}
      </p>
      <DialogContent className="m-0 max-h-screen overflow-y-auto p-0 sm:max-h-[525px] sm:max-w-[625px]">
        <Blocks parent={builderBlock?.id} path="blocks" blocks={blocks} className="flex flex-1" />
      </DialogContent>
    </Dialog>
  );
}
