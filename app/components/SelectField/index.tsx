import { ReactNode } from 'react';
import { useRemixFormContext } from 'remix-hook-form';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

export type SelectFieldProps = {
  placeholder?: string;
  label?: string;
  description?: ReactNode;
  disabled?: boolean;
  name: string;
  values: { value: string; label: string }[];
};

export function SelectField({ placeholder, label, description, values, name, disabled }: SelectFieldProps) {
  const form = useRemixFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      disabled={disabled}
      render={({ field: { onChange, value, ref, ...field } }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={onChange} defaultValue={value} {...field}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent ref={ref}>
              {values.map(val => (
                <SelectItem key={val.value} value={val.value}>
                  {val.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
