import { ColumnContentType, DataTable, Text } from '@shopify/polaris';
import { ReactNode } from 'react';

interface DictionaryTableProps {
  numberColumn?: boolean;
  dictionaryRows: { [header: string]: ReactNode }[];
}

export function DictionaryTable({ dictionaryRows, numberColumn }: DictionaryTableProps) {
  if (!dictionaryRows.length) return null;

  const headings = [...new Set(dictionaryRows.flatMap(dict => Object.keys(dict))).values()];
  const rows = dictionaryRows.map(data => headings.map(key => (data as any)[key] || '—'));
  const types = new Array<ColumnContentType>(headings.length).fill('text');

  if (numberColumn) {
    headings.splice(0, 0, '#');
    types.splice(0, 0, 'text');
    rows.forEach((row, index) =>
      row.splice(
        0,
        0,
        <Text as="p" variant="bodyMd">
          {index.toString()}
        </Text>,
      ),
    );
  }

  const table = (
    <DataTable
      headings={headings}
      rows={rows}
      columnContentTypes={types}
      increasedTableDensity
      verticalAlign="middle"
    />
  );

  if (!numberColumn) {
    return table;
  }

  return <div className="w-full [&_tr>th:first-child]:w-[24px]">{table}</div>;
}
