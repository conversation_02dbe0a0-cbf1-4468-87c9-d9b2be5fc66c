import { TextField, TextFieldProps } from '@shopify/polaris';
import { useRemixFormContext } from 'remix-hook-form';
import { FormControl, FormField, FormItem } from '../ui/form';
import React from 'react';

const TextFieldRef = React.forwardRef<React.JSX.Element, TextFieldProps>((props, ref) => <TextField {...props} />);

export type FormTextFieldProps = Omit<TextFieldProps, 'label' | 'autoComplete'> & {
  name: string;
  path?: string;
  label?: string;
  autoComplete?: string;
};

export const FormTextField = ({ name, path, ...fieldProps }: FormTextFieldProps) => {
  const form = useRemixFormContext();

  return (
    <FormField
      control={form.control}
      name={path ? `${path}.${name}` : name}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <TextFieldRef label="" autoComplete="off" {...field} {...fieldProps} />
          </FormControl>
        </FormItem>
      )}
    />
  );
};
