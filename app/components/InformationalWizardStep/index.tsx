import { But<PERSON> } from '@/components/ui/button';
import { <PERSON> } from 'react-router';
import AllAwareLogo from '/aa-logo-colored.svg';

export default function InformationalWizardStep({
  children,
  CTA,
  onClick,
  to,
}: {
  children?: React.ReactNode;
  CTA?: string;
  onClick?: () => void;
  to?: string;
}) {
  return (
    <div className="mx-auto flex w-full max-w-sm grow flex-col items-center justify-between p-8 text-center">
      <img src={AllAwareLogo} alt="All Aware Logo" className="w-[90px]" />
      <div className="flex grow flex-col items-center justify-center gap-8">{children}</div>
      {CTA && (
        <Button variant="primary" className="w-full max-w-md" onClick={onClick} asChild={!!to}>
          {to ? (
            <Link to={to} prefetch="intent">
              {CTA}
            </Link>
          ) : (
            CTA
          )}
        </Button>
      )}
    </div>
  );
}
