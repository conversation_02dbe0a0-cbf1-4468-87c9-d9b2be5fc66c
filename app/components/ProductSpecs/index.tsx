import { cn } from '@/lib/utils';
import HTMLText from '../HTML';
import HTMLReplacer, { HTMLSection } from '../HTMLReplacer';
import { useProduct } from '../ProductDataProvider/context';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '../ui/accordion';
import { Skeleton } from '../ui/skeleton';

function SpecTag({ index, innerHtml, afterHtml }: HTMLSection) {
  const value = `item-${index}`;
  return (
    <AccordionItem key={value} value={value}>
      <AccordionTrigger className="text-lg">{innerHtml}</AccordionTrigger>
      <AccordionContent>
        <HTMLText HTML={afterHtml} className="prose-lg opacity-80" />
      </AccordionContent>
    </AccordionItem>
  );
}

export default function ProductSpecs({ attributes }: { attributes?: any }) {
  const { product } = useProduct();

  if (!product)
    return (
      <div {...attributes} className={cn('w-full space-y-2', attributes.className)}>
        <Skeleton className="h-10 w-[80%] rounded-sm xl:h-14" />
        <Skeleton className="h-10 w-[90%] rounded-sm xl:h-14" />
        <Skeleton className="h-10 w-[85%] rounded-sm xl:h-14" />
      </div>
    );

  return (
    <Accordion type="single" collapsible {...attributes}>
      <HTMLReplacer HTML={product?.descriptionHtml} map={{ h6: SpecTag }} />
    </Accordion>
  );
}
