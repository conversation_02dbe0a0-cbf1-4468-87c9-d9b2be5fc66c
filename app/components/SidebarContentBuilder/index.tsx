import { CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { Collapsible } from '@radix-ui/react-collapsible';
import { Link, useLocation } from 'react-router';
import { ChevronRight, ExternalLink } from 'lucide-react';
import { FC, type ReactNode } from 'react';

export interface SidebarContentMenu {
  root?: (SidebarItem | FC)[] | undefined;
  [label: string]: (SidebarItem | FC)[] | undefined;
}

interface SidebarItem extends SidebarSubItem {
  subItems?: SidebarSubItem[];
}

interface SidebarSubItem {
  label: string;
  pathname: string;
  external?: boolean;
  icon?: React.FC;
}

function SidebarSubItem({
  item,
  activeIfStartsWith,
  children,
  secondary,
}: {
  item: SidebarSubItem;
  activeIfStartsWith?: boolean;
  children?: ReactNode;
  secondary?: boolean;
}) {
  const { pathname } = useLocation();

  return (
    <SidebarMenuButton
      isActive={activeIfStartsWith ? pathname.startsWith(item.pathname) : pathname == item.pathname}
      asChild
    >
      <Link to={item.pathname} prefetch="intent" target={item.external ? '_blank' : undefined}>
        {item.icon && <item.icon />}
        <span className={!secondary ? 'font-semibold' : 'text-sm text-muted-foreground'}>{item.label}</span>{' '}
        {item.external && <ExternalLink className="ml-auto" />}
        {children}
      </Link>
    </SidebarMenuButton>
  );
}

function SidebarItem({ item, secondary }: { item: SidebarItem | FC; secondary?: boolean }) {
  const { pathname } = useLocation();

  if (typeof item == 'function') {
    const Component = item;
    return (
      <SidebarMenuItem>
        <Component />
      </SidebarMenuItem>
    );
  }

  if (!item.subItems?.length) {
    return (
      <SidebarMenuItem>
        <SidebarSubItem item={item} secondary={secondary} />
      </SidebarMenuItem>
    );
  }

  return (
    <Collapsible className="group/collapsible" open={pathname.startsWith(item.pathname)}>
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarSubItem item={item} activeIfStartsWith secondary={secondary}>
            <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
          </SidebarSubItem>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub>
            {item.subItems.map((subItem, index) => (
              <SidebarMenuSubItem key={index}>
                <SidebarSubItem item={subItem} secondary={secondary} />
              </SidebarMenuSubItem>
            ))}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
}

export function SidebarContentBuilder({ content, secondary }: { content: SidebarContentMenu; secondary?: boolean }) {
  return (
    <>
      {Object.entries(content).map(([label, items]) => {
        const content = (
          <SidebarMenu key={label}>
            {items?.map((item, index) => <SidebarItem key={index} item={item} secondary={secondary} />)}
          </SidebarMenu>
        );

        return label == 'root' ? (
          content
        ) : (
          <SidebarGroup key={label} className="p-0">
            <SidebarGroupLabel>{label}</SidebarGroupLabel>
            <SidebarGroupContent>{content}</SidebarGroupContent>
          </SidebarGroup>
        );
      })}
    </>
  );
}
