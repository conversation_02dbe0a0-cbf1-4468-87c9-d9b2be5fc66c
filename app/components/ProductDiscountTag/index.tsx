import { cn } from '@/lib/utils';
import { useProduct } from '../ProductDataProvider/context';

export default function ProductDiscountTag({ attributes }: { attributes?: any }) {
  const { selectedVariant } = useProduct();

  if (!selectedVariant) return null;
  // const variant = product?.variants?.nodes.find(v => v.id === convertIdToGid('ProductVariant', variantId));

  const compareAtPrice = Number(selectedVariant?.compareAtPrice?.amount) || 0;
  const price = Number(selectedVariant?.price?.amount) || 0;

  const discount = compareAtPrice > 0 ? ((compareAtPrice - price) / compareAtPrice) * 100 : 0;

  if (discount > 0) {
    return (
      <div
        {...attributes}
        className={cn(
          'w-28 rounded-xs bg-emphasis px-2 py-1 text-center font-bold text-background',
          attributes?.className,
        )}
      >
        {Math.round(discount)}% OFF
      </div>
    );
  }

  return null;
}
