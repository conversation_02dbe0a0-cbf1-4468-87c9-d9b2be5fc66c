import Icon from '../Icon';

// Helper function to get nth day of month
function getNthDayOfMonth(year: number, month: number, dayOfWeek: number, n: number): Date {
  const date = new Date(year, month, 1);
  const firstDayOfWeek = date.getDay();
  const offset = (dayOfWeek - firstDayOfWeek + 7) % 7;
  date.setDate(1 + offset + (n - 1) * 7);
  return date;
}

// Helper function to get last day of month
function getLastDayOfMonth(year: number, month: number, dayOfWeek: number): Date {
  const date = new Date(year, month + 1, 0);
  const lastDayOfWeek = date.getDay();
  const offset = (lastDayOfWeek - dayOfWeek + 7) % 7;
  date.setDate(date.getDate() - offset);
  return date;
}

function getHolidaysForYear(year: number): Date[] {
  const holidays: Date[] = [
    new Date(year, 0, 1), // New Year's Day
    new Date(year, 6, 4), // Independence Day
    new Date(year, 10, 11), // Veterans Day
    new Date(year, 11, 25), // Christmas Day
  ];

  // Add variable date holidays
  holidays.push(
    getNthDayOfMonth(year, 0, 1, 3), // MLK Day (3rd Monday)
    getNthDayOfMonth(year, 1, 1, 3), // Presidents' Day (3rd Monday)
    getLastDayOfMonth(year, 4, 1), // Memorial Day (Last Monday)
    getNthDayOfMonth(year, 8, 1, 1), // Labor Day (1st Monday)
    getNthDayOfMonth(year, 9, 1, 2), // Columbus Day (2nd Monday)
    getNthDayOfMonth(year, 10, 4, 4), // Thanksgiving Day (4th Thursday)
  );

  return holidays;
}

function isHoliday(date: Date): boolean {
  const holidays = getHolidaysForYear(date.getFullYear());
  return holidays.some(holiday => date.getMonth() === holiday.getMonth() && date.getDate() === holiday.getDate());
}

function isWeekend(date: Date): boolean {
  const day = date.getDay();
  return day === 0 || day === 6; // 0 is Sunday, 6 is Saturday
}

function isBusinessDay(date: Date): boolean {
  return !isWeekend(date) && !isHoliday(date);
}

function addBusinessDays(date: Date, days: number): Date {
  const result = new Date(date);
  let remainingDays = days;

  while (remainingDays > 0) {
    result.setDate(result.getDate() + 1);
    if (isBusinessDay(result)) {
      remainingDays--;
    }
  }

  return result;
}

function calculateDeliveryDate() {
  const now = new Date();
  const cstNow = new Date(now.toLocaleString('en-US', { timeZone: 'America/Chicago' }));
  const isBeforeTwoPM = cstNow.getHours() < 14;

  // Start from the next business day
  const startDate = new Date(now);
  startDate.setDate(startDate.getDate() + 1);

  // Skip to next business day if current day is not a business day
  while (!isBusinessDay(startDate)) {
    startDate.setDate(startDate.getDate() + 1);
  }

  // Add business days based on time of day
  const deliveryDate = addBusinessDays(startDate, isBeforeTwoPM ? 2 : 3);

  const formattedDate = deliveryDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return formattedDate;
}

export default function DeliversBy() {
  const deliveryDate = calculateDeliveryDate();
  return (
    <div className="flex gap-3 text-body-normal">
      <Icon icon="delivery-truck" className="h-6" />
      <div className="flex flex-col gap-2">
        <p className="text-body-xs">
          <span className="font-bold">Delivers by</span>
          <br />
          {deliveryDate}
        </p>
      </div>
    </div>
  );
}
