import { RegisteredComponent } from '@builder.io/sdk-react/edge';
import { Separator } from '../ui/separator';

const BuilderSeparator = ({
  orientation,
  attributes,
}: {
  orientation?: 'horizontal' | 'vertical';
  attributes: any;
}) => <Separator orientation={orientation} {...attributes} />;

export const SEPARATOR_COMPONENT: RegisteredComponent = {
  name: 'Separator',
  component: BuilderSeparator,
  noWrap: true,
  defaultStyles: {
    margin: '24px 0px',
    width: '100%',
  },
  inputs: [
    {
      type: 'string',
      name: 'orientation',
      enum: ['horizontal', 'vertical'],
      defaultValue: 'horizontal',
    },
  ],
};
