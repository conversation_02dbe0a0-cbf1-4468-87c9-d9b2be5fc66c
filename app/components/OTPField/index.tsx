import { REGEXP_ONLY_DIGITS } from 'input-otp';
import { ReactNode, useCallback, useEffect, useRef } from 'react';
import { useRemixFormContext } from 'remix-hook-form';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '../ui/input-otp';
import { Button } from '../ui/button';
import { useOTP } from '@/hooks/use-otp';
import { Loader2 } from 'lucide-react';

export type OTPFieldProps = {
  label?: string;
  description?: ReactNode;
  disabled?: boolean;
  containerClassName?: string;
  className?: string;
  name: string;
  resendEmail?: string;
  onResendCode?: () => void;
  sendOnLoad?: boolean;
};

export function OTPField({
  label,
  description,
  name,
  disabled,
  containerClassName,
  className,
  resendEmail,
  onResendCode,
  sendOnLoad,
}: OTPFieldProps) {
  const form = useRemixFormContext();
  const { resendCode, isResending } = useOTP();

  const resend = useCallback(() => {
    if (resendEmail) {
      resendCode(resendEmail);
      onResendCode?.();
    }
  }, [resendCode, resendEmail, onResendCode]);

  const sentEmailRef = useRef(false);
  useEffect(() => {
    if (sendOnLoad && !sentEmailRef.current) {
      sentEmailRef.current = true;
      resend();
    }
  }, [sendOnLoad, resend]);

  return (
    <>
      <FormField
        control={form.control}
        name={name}
        disabled={disabled}
        render={({ field: { ...fields } }) => (
          <FormItem className={className}>
            {label && <FormLabel>{label}</FormLabel>}
            <FormControl>
              <InputOTP
                {...fields}
                maxLength={6}
                pattern={REGEXP_ONLY_DIGITS}
                containerClassName={containerClassName}
                onComplete={form.handleSubmit}
              >
                <InputOTPGroup>
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
            </FormControl>
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </FormItem>
        )}
      />
      {Boolean(resendEmail) && (
        <Button
          variant="link"
          size="fit"
          type="button"
          onClick={resend}
          disabled={isResending || disabled}
          className="text-xs text-emphasis underline"
        >
          Resend code
          {isResending && <Loader2 className="size-4 animate-spin text-emphasis" />}
        </Button>
      )}
    </>
  );
}
