import { RegisteredComponent } from '@builder.io/sdk-react/edge';
import MultiColorHeading from '.';

export const MULTI_COLOR_HEADING_COMPONENT: RegisteredComponent = {
  name: 'Multi Color Heading',
  component: MultiColorHeading,
  inputs: [
    {
      name: 'textStart',
      required: true,
      type: 'string',
      defaultValue: 'Enter text here...',
    },
    {
      name: 'textStartColor',
      type: 'color',
    },
    {
      name: 'isTextStartGradientInPersonalTheme',
      type: 'boolean',
      defaultValue: false,
      helperText: 'If true, only in the personal theme will be use the gradient for the start text.',
    },
    {
      name: 'textEnd',
      required: true,
      type: 'string',
      defaultValue: 'Enter text here...',
    },
    {
      name: 'textEndColor',
      type: 'color',
    },
    {
      name: 'isTextEndGradientInPersonalTheme',
      type: 'boolean',
      defaultValue: false,
      helperText: 'If true, only in the personal theme will be use the gradient for the end text.',
    },
    {
      name: 'textFinal',
      type: 'string',
      helperText: 'This is the 3rd text. Bad nomenclature, I know.',
    },
    {
      name: 'textFinalColor',
      type: 'color',
    },
    {
      name: 'heading',
      type: 'enum',
      defaultValue: 'h1',
      enum: [
        { label: 'Heading 1', value: 'h1' },
        { label: 'Heading 2', value: 'h2' },
        { label: 'Heading 3', value: 'h3' },
        { label: 'Heading 4', value: 'h4' },
        { label: 'Heading 5', value: 'h5' },
        { label: 'Heading 6', value: 'h6' },
      ],
    },
    {
      name: 'isOneLine',
      type: 'boolean',
      defaultValue: false,
    },
  ],
};
