import useTheme from '@/hooks/use-theme';

interface IProps {
  textStart: string;
  textEnd: string;
  heading: string;
  isOneLine: boolean;
  textStartColor: string;
  textEndColor: string;
  isTextStartGradientInPersonalTheme: boolean;
  isTextEndGradientInPersonalTheme: boolean;
  textFinal: string;
  textFinalColor: string;
}

export default function MultiColorHeading({
  textStart,
  textEnd,
  heading,
  isOneLine,
  textStartColor,
  textEndColor,
  isTextStartGradientInPersonalTheme,
  isTextEndGradientInPersonalTheme,
  textFinal,
  textFinalColor,
}: IProps) {
  const theme = useTheme();
  const isPersonalGradientStart = isTextStartGradientInPersonalTheme && theme == 'personal';
  const isPersonalGradientEnd = isTextEndGradientInPersonalTheme && theme == 'personal';

  const content = (
    <span className="inline">
      <span
        className={`inline ${
          isPersonalGradientStart
            ? 'bg-linear-to-r from-(--green-one) to-(--green-three) bg-clip-text text-transparent'
            : ''
        }`}
        style={!isPersonalGradientStart ? { color: textStartColor || 'var(--heading-one)' } : undefined}
      >
        {textStart}
      </span>
      {isOneLine ? null : <br />}
      <span
        className={`inline ${
          isPersonalGradientEnd
            ? 'bg-linear-to-r from-(--green-one) to-(--green-three) bg-clip-text text-transparent'
            : ''
        }`}
        style={!isPersonalGradientEnd ? { color: textEndColor || 'var(--heading-two)' } : undefined}
      >
        {textEnd}
      </span>
      {textFinal && (
        <span className={'inline'} style={{ color: textFinalColor || 'var(--heading-one)' }}>
          {textFinal}
        </span>
      )}
    </span>
  );

  switch (heading) {
    case 'h1':
      return <h1>{content}</h1>;
    case 'h2':
      return <h2>{content}</h2>;
    case 'h3':
      return <h3>{content}</h3>;
    case 'h4':
      return <h4>{content}</h4>;
    case 'h5':
      return <h5>{content}</h5>;
    case 'h6':
      return <h6>{content}</h6>;
    default:
      return content;
  }
}
