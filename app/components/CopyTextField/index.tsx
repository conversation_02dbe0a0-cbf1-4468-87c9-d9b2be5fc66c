import { useIsMobileDevice } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { CheckSquare, Copy } from 'lucide-react';
import { useCallback, useState } from 'react';

export function CopyTextField({
  preview,
  value,
  onCopied,
}: {
  preview?: string;
  value: string;
  onCopied?: () => void;
}) {
  const { toast } = useToast();
  const [isCopied, setIsCopied] = useState(false);
  const isMobileDevice = useIsMobileDevice();

  const handleCopy = useCallback(() => {
    navigator.clipboard.writeText(value);
    setIsCopied(true);
    setTimeout(() => {
      setIsCopied(false);
      onCopied?.();
      if (!isMobileDevice)
        toast({
          variant: 'success',
          title: 'Copied!',
        });
    }, 1000);
  }, [toast, onCopied, value]);

  return (
    <div className="flex w-full items-center rounded-sm border bg-background text-muted-foreground">
      <p className="w-full overflow-hidden px-4 text-xs text-ellipsis whitespace-nowrap">{preview || value}</p>
      <button className="aspect-square h-full border-l border-border p-2" onClick={handleCopy}>
        {isCopied ? <CheckSquare /> : <Copy />}
      </button>
    </div>
  );
}
