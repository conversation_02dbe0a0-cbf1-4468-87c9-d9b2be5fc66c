import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Checkbox } from '@/components/ui/checkbox';
import { centsToDollars, formatMoney, formatStripeDate, toOrdinal } from '@/lib/utils';
import { CheckedState } from '@radix-ui/react-checkbox';
import { Link } from 'react-router';
import { useCallback, useMemo, useRef, useState } from 'react';
import type Stripe from 'stripe';

export default function InvoiceConfirmationField({
  invoice,
  consent,
  setConsent,
}: {
  invoice: Stripe.Invoice;
  consent: number;
  setConsent: (consent: number) => void;
}) {
  const [expired, setExpired] = useState(false);
  const hasProration = invoice.lines.data.some(line => line.proration && line.type == 'invoiceitem');

  const paidNow = useMemo(
    () =>
      formatMoney(
        centsToDollars(
          invoice.lines.data.reduce(
            (prev, line) => prev + (line.proration && line.type == 'invoiceitem' ? line.amount : 0),
            0,
          ),
        ),
      ),
    [invoice],
  );
  const paidLater = useMemo(
    () =>
      formatMoney(
        centsToDollars(
          invoice.lines.data.reduce(
            (prev, line) => prev + (!line.proration && line.type == 'subscription' ? line.amount : 0),
            0,
          ),
        ),
      ),
    [invoice],
  );
  const laterDate = new Date(invoice.period_end * 1000);
  const laterDateStr = formatStripeDate(invoice.period_end);

  const timeout = useRef<any>();
  const consentChanged = useCallback((checked: CheckedState) => {
    const value = checked.valueOf().toString() == 'true';

    if (timeout.current) {
      clearTimeout(timeout.current);
      timeout.current = undefined;
    }

    setExpired(false);

    if (!value) {
      setConsent(0);
      return;
    }

    setConsent(Date.now());
    timeout.current = setTimeout(
      () => {
        setConsent(0);
        setExpired(true);
      },
      14.9 * 60 * 1000,
    );
  }, []);

  return (
    <>
      <Accordion type="single" collapsible className="w-full">
        {hasProration && (
          <AccordionItem value="item-1">
            <AccordionTrigger>Pro-rated until next bill ({paidNow})</AccordionTrigger>
            <AccordionContent>
              <p>
                Your new monthly bill is {paidLater}. Your pro-rated amount today is {paidNow}. This pro-rated covers
                your service on these devices until your next bill for {paidLater} on {laterDateStr}.
              </p>
            </AccordionContent>
          </AccordionItem>
        )}
        <AccordionItem value="item-2">
          <AccordionTrigger>Your new monthly bill ({paidLater})</AccordionTrigger>
          <AccordionContent>
            <p>
              Starting on {laterDateStr}, you will be charged {paidLater} per month to your credit card or payment
              method on file until you cancel.
            </p>
            <ul className="text-md mt-4 ml-2 text-muted-foreground">
              {invoice.lines.data
                .filter(line => !line.proration && line.type == 'subscription')
                .map(line => (
                  <li key={line.id} className="flex justify-between">
                    <span>{line.description}</span>
                    <span className="font-bold">{formatMoney(centsToDollars(line.amount))}</span>
                  </li>
                ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      <div>
        {expired && (
          <p className="text-sm text-destructive">
            Consent expired, please re-confirm your consent and upgrade your service.
          </p>
        )}
        <Checkbox id="consent" checked={!!consent} onCheckedChange={consentChanged} />
        <label
          htmlFor="consent"
          className="ml-2 cursor-pointer text-xs leading-3 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          By clicking this box, you agree that your monthly subscription will automatically renew on the{' '}
          {toOrdinal(laterDate.getDate())} of every month you will be charged the updated fee of {paidLater} per month
          to your credit card or payment method on file until you cancel. See the auto-renewal service{' '}
          <Link to={'/policies/terms-and-conditions'} className="underline">
            Terms and Conditions
          </Link>{' '}
          for more information. You can cancel any time by visiting the "Account" tab in your profile page or by calling
          us at (855) 71-AWARE. For more information on how to cancel your monthly subscription, please visit{' '}
          <Link to="/policies/cancellation-policy" className="underline">
            Cancellation Policy.
          </Link>
        </label>
      </div>
    </>
  );
}
