import { ReactNode } from 'react';
import { Card, CardTitle, CardDescription, CardHeader, CardContent } from '../ui/card';

interface IProps {
  title: string;
  description?: ReactNode;
  mainActionLabel?: string;
  footer?: React.ReactNode;
  children: React.ReactNode;
  actionComponent?: React.ReactNode;
  onClickMainAction?: () => void;
}

export default function StepLayout({ title, description, children }: IProps) {
  return (
    <Card className="m-4 max-w-md p-6 shadow-lg md:w-[619px]">
      <CardHeader className="flex flex-col items-center space-y-3">
        <CardTitle className="text-xxl text-center font-bold">{title}</CardTitle>
        {description && <CardDescription className="max-w-sm text-center text-gray-500">{description}</CardDescription>}
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}
