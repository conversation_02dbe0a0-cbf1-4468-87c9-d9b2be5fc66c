import { Content } from '@builder.io/sdk-react/edge';
import { CUSTOM_COMPONENTS, CUSTOM_CONTEXT } from '@/builder/registry';
import { Link } from 'react-router';
import type { ContentVariantsPrps } from 'node_modules/@builder.io/sdk-react/types/components/content-variants/content-variants.types';
import { useRootData } from '@/root';
import useTheme from '@/hooks/use-theme';

type ContentProps = Omit<ContentVariantsPrps, 'data' | 'customComponents' | 'apiKey'>;

function LinkComponent(props: any) {
  return <Link {...props} to={props.href} prefetch="intent" />;
}

export default function Builder(props: ContentProps) {
  const { builderApiKey } = useRootData();

  const theme = useTheme();
  const themeHandle = theme == 'business' ? 'business' : 'home';

  return (
    <Content
      canTrack={false}
      enrich={true}
      apiKey={builderApiKey}
      data={{ theme, themeHandle }}
      customComponents={CUSTOM_COMPONENTS}
      linkComponent={LinkComponent}
      {...props}
    />
  );
}
