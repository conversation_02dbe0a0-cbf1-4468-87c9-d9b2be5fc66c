import { useRemixFormContext } from 'remix-hook-form';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { NumberInput, NumberInputProps } from '../ui/number-input';

interface IProps extends Omit<NumberInputProps, 'form'> {
  name: string;
  label?: string;
  description?: string;
}

export default function NumberField({ name, label, description, ...rest }: IProps) {
  const form = useRemixFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <NumberInput {...field} {...rest} />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
