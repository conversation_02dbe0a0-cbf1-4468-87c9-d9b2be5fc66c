import { Search } from 'lucide-react';
import { Input } from '../ui/input';
import useTheme from '@/hooks/use-theme';
import { cn } from '@/lib/utils';

export default function ExternalSupportSearchBar({ attributes }: { attributes?: any }) {
  const theme = useTheme();
  const url =
    theme == 'business'
      ? 'https://support-business.allaware.com/hc/en-us/search'
      : 'https://support.allaware.com/hc/en-us/search';

  return (
    <form
      onSubmit={event => {
        if (!(event.target as HTMLElement)?.querySelector('input')?.value) event.preventDefault();
      }}
      action={url}
      method="GET"
      target="_blank"
      {...attributes}
      className={cn('relative h-[50px] w-full sm:h-[70px]', attributes?.className)}
    >
      <Input
        type="text"
        name="query"
        className="size-full rounded-full bg-white px-[25px] text-base text-[#2D2D2D] placeholder-[#525252] sm:px-[35px] sm:text-lg"
        placeholder="Search the help center"
      />
      <button type="submit" className="absolute right-0 size-[50px] p-[15px] sm:size-[70px]">
        <Search className="size-full text-[#2D2D2D]" />
      </button>
    </form>
  );
}
