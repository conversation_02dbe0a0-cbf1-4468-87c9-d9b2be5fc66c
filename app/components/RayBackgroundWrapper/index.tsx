export default function RayBackgroundWrapper({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative h-full w-full overflow-hidden bg-inherit">
      <img
        src="https://cdn.shopify.com/s/files/1/0608/4762/0347/files/lightblur_3_1.png?v=1746136229"
        alt="Light blur-sm effect"
        className="pointer-events-none absolute bottom-0 z-10 w-full object-contain opacity-80 mix-blend-screen blur-xs"
      />

      <div className="relative z-20">{children}</div>
    </div>
  );
}
