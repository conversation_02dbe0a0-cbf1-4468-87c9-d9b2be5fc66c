import { RegisteredComponent } from '@builder.io/sdk-react/edge';
import Tracker from '.';

export const TRACKER_COMPONENT: RegisteredComponent = {
  name: 'Tracker',
  component: Tracker,
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  inputs: [
    {
      name: 'title',
      type: 'string',
      defaultValue: 'Total miles tracked',
    },
    {
      name: 'knownTime',
      helperText: 'The start date since known tracking began.',
      type: 'date',
      defaultValue: 1663823371428.6048,
    },
    {
      name: 'knownTracked',
      helperText: 'The number of units tracked at the start time.',
      type: 'number',
      defaultValue: 0,
    },
    {
      name: 'knownDeployed',
      helperText: 'The number of devices deployed at the start time.',
      type: 'number',
      defaultValue: 0,
    },
    {
      name: 'trackRate',
      helperText: 'Average units tracked per day.',
      type: 'number',
      defaultValue: 0.0395783775945,
    },
    {
      name: 'deployRate',
      helperText: 'Average units deployed per day.',
      type: 'number',
      defaultValue: 6.20944558522,
    },
    {
      name: 'blocks',
      type: 'uiBlocks',
      hideFromUI: true,
      defaultValue: [],
    },
  ],
};
