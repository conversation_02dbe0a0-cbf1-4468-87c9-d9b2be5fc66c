import useTheme from '@/hooks/use-theme';
import { cn } from '@/lib/utils';
import { useRootData } from '@/root';
import { Blocks, BuilderBlock } from '@builder.io/sdk-react/edge';
import { startTransition, useEffect, useState } from 'react';

interface IProps {
  title: string;
  knownTime: Date | number | string;
  knownTracked: number;
  knownDeployed: number;
  deployRate: number;
  trackRate: number;
  builderBlock: BuilderBlock;
  blocks: BuilderBlock[];
}

function calculateTrackedTotal(
  time: number,
  knownTime: number = 1663823371428.6048,
  knownTracked: number = 0,
  knownDeployed: number = 0,
  trackRate: number = 0.0395783775945,
  deployRate: number = 6.20944558522,
) {
  const MS_PER_DAY = 1000 * 60 * 60 * 24;

  const t_current_days = (time - knownTime) / MS_PER_DAY;

  const coeff_N_linear = knownDeployed * trackRate;
  const coeff_K_quadratic = (deployRate * trackRate) / 2;

  const total = knownTracked + coeff_N_linear * t_current_days + coeff_K_quadratic * Math.pow(t_current_days, 2);

  const totalRounded = Math.round(total);
  const nextTracked = totalRounded + 1;

  const a_quad = coeff_K_quadratic;
  const b_quad = coeff_N_linear;
  const c_quad = knownTracked - nextTracked;

  // Assuming a_quad is not 0 and discriminant will be non-negative
  const discriminant = Math.pow(b_quad, 2) - 4 * a_quad * c_quad;
  const nextT_duration_from_knownTime = (-b_quad + Math.sqrt(discriminant)) / (2 * a_quad);

  return {
    total: totalRounded,
    ttn: (nextT_duration_from_knownTime - t_current_days) * MS_PER_DAY,
  };
}

export default function Tracker({
  title,
  deployRate,
  knownTime,
  knownDeployed,
  knownTracked,
  trackRate,
  builderBlock,
  blocks,
}: IProps) {
  const theme = useTheme();
  const { time } = useRootData();

  const [currentMiles, setCurrentMiles] = useState(
    () =>
      calculateTrackedTotal(time, new Date(knownTime).getTime(), knownTracked, knownDeployed, trackRate, deployRate)
        .total,
  );

  useEffect(() => {
    let running = true;

    function run() {
      if (!running) return;
      const { total, ttn } = calculateTrackedTotal(
        Date.now(),
        new Date(knownTime).getTime(),
        knownTracked,
        knownDeployed,
        trackRate,
        deployRate,
      );

      startTransition(() => {
        setCurrentMiles(total);
      });

      setTimeout(run, ttn);
    }

    setTimeout(run, 1000);

    return () => {
      running = false;
    };
  }, [knownDeployed, knownTime, knownTracked, deployRate, trackRate]);

  return (
    <div className="relative h-[500px]">
      <div className="relative flex h-[500px] blur-2xl">
        <div className="absolute z-10 h-[500px] w-[100vw] backdrop-blur-lg" />
        <div className="absolute h-full w-full scale-75 animate-move-blur-1 rounded-full bg-[rgba(217,216,216,0.1)] opacity-90 blur-3xl will-change-transform" />
        <div className="absolute h-full w-full scale-75 animate-move-blur-2 rounded-full bg-gradient-start opacity-70 blur-3xl will-change-transform" />
      </div>
      <div
        className={cn(
          'absolute inset-0 z-10 m-auto flex h-[80%] w-[80%] items-center justify-center rounded-md bg-background/85',
          theme == 'business' ? 'bg-black/85' : 'bg-background opacity-75',
        )}
      >
        <div className="flex w-full flex-col items-center px-7 text-center">
          <p className="text-sm font-bold text-section-meta">{title.toLocaleUpperCase()}</p>
          <p className="text-title-3 font-bold whitespace-nowrap drop-shadow-sm sm:text-title-2 lg:text-title-1">
            {[...currentMiles.toLocaleString()].map((char, i) => (
              <span key={char + i} className={cn('inline-block', char != ',' && 'w-[0.65em]')}>
                {char}
              </span>
            ))}
          </p>
          <Blocks parent={builderBlock?.id} path="blocks" blocks={blocks} />
        </div>
      </div>
    </div>
  );
}
