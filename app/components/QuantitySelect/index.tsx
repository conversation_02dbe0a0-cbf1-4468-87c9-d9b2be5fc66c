import { SelectProps } from '@radix-ui/react-select';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useCallback } from 'react';

type QuantitySelectProps = {
  limit: number;
  value?: number;
  defaultValue?: number;
  onValueChange?(value: number): void;
} & Omit<SelectProps, 'value' | 'defaultValue' | 'onValueChange'>;

export default function QuantitySelect({
  limit,
  value,
  defaultValue = 1,
  onValueChange,
  ...props
}: QuantitySelectProps) {
  const changed = useCallback((value: string) => onValueChange?.(parseInt(value)), [onValueChange]);

  return (
    <Select {...props} value={value?.toString()} defaultValue={defaultValue?.toString()} onValueChange={changed}>
      <SelectTrigger className="w-20 rounded-full *:w-10">
        <SelectValue />
      </SelectTrigger>
      <SelectContent className="min-w-20 rounded-sm">
        {Array.from({ length: limit }, (_, i) => (
          <SelectItem key={i} value={(i + 1).toString()} className="w-full px-4">
            {i + 1}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
