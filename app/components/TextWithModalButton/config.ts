import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import TextWithModalButton from '.';

export const TEXT_WITH_MODAL_BUTTON_COMPONENT: RegisteredComponent = {
  name: 'Text With Modal Button',
  component: TextWithModalButton,
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  inputs: [
    {
      name: 'textPart1',
      type: 'string',
      required: true,
      defaultValue: 'Powered by ',
    },
    {
      name: 'buttonText',
      type: 'string',
      required: true,
      defaultValue: 'Alarm.com',
    },
    {
      name: 'textPart2',
      type: 'string',
      required: true,
      defaultValue: ' with 9 million customers',
    },
    {
      name: 'openDialogInEditor',
      type: 'boolean',
      defaultValue: false,
      helperText: 'If true, the dialog will be open in the editor. This has no effect on the live page.',
    },
    {
      name: 'buttonTextColor',
      type: 'color',
    },
    {
      name: 'blocks',
      type: 'list',
      hideFromUI: true,
      defaultValue: [],
    },
    {
      name: 'modalHeight',
      type: 'string',
      defaultValue: '525px',
    },
    {
      name: 'modalWidth',
      type: 'string',
      defaultValue: '625px',
    },
  ],
};
