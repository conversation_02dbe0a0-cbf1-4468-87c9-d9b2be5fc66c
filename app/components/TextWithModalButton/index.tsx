import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Blocks, BuilderBlock, isEditing } from '@builder.io/sdk-react/edge';

interface IProps {
  textPart1: string;
  textPart2: string;
  buttonText: string;
  builderBlock: BuilderBlock;
  blocks: BuilderBlock[];
  openDialogInEditor?: boolean;
  modalHeight?: string;
  modalWidth?: string;
}

export default function TextWithModalButton({
  textPart1,
  textPart2,
  buttonText,
  builderBlock,
  blocks,
  openDialogInEditor,
  modalHeight,
  modalWidth,
}: IProps) {
  if (openDialogInEditor && isEditing()) {
    return (
      <div className="max-h-[400px] overflow-y-auto sm:max-w-[425px]">
        <Blocks parent={builderBlock?.id} path="blocks" blocks={blocks} className="flex flex-1" />
      </div>
    );
  }

  return (
    <Dialog>
      <p>
        {textPart1}
        <DialogTrigger asChild>
          <span className="cursor-pointer underline underline-offset-2">{buttonText}</span>
        </DialogTrigger>
        {textPart2}
      </p>
      <DialogContent
        className="m-0 max-h-screen overflow-y-auto p-0 sm:max-h-[80%] sm:max-w-[80%]"
        style={{ height: modalHeight, width: modalWidth }}
      >
        <Blocks parent={builderBlock?.id} path="blocks" blocks={blocks} className="flex flex-1" />
      </DialogContent>
    </Dialog>
  );
}
