import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useState, useEffect } from 'react';
import Icon from '../Icon';

interface IProps {
  avatarUrl: string;
  phoneNumber: string;
  businessHours: Record<string, string>;
}

export default function LiveAgent({ avatarUrl, phoneNumber, businessHours }: IProps) {
  const [isBusinessHours, setIsBusinessHours] = useState(false);

  function handleOpenChat() {
    window?.zE('messenger', 'open');
  }

  useEffect(() => {
    const checkBusinessHours = () => {
      const nowCst = new Date(new Date().toLocaleString('en-US', { timeZone: 'America/Chicago' }));

      const dayOfWeek = nowCst.getDay();
      const daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const today = daysOfWeek[dayOfWeek];

      if (!businessHours[today]) {
        setIsBusinessHours(false);
        return;
      }

      const hours = businessHours[today];
      const [startTime, endTime] = hours.split('-');

      if (!startTime || !endTime) {
        setIsBusinessHours(false);
        return;
      }

      const parseTime = (timeStr: string) => {
        const isPM = timeStr.toUpperCase().includes('PM');
        const timeDigits = timeStr.replace(/[^\d:]/g, '');
        const [hours, minutes] = timeDigits.split(':').map(Number);
        let hour = hours;

        // Adjust for PM
        if (isPM && hour < 12) hour += 12;
        // Adjust for AM 12
        if (!isPM && hour === 12) hour = 0;

        return { hour, minutes: minutes || 0 };
      };

      const start = parseTime(startTime);
      const end = parseTime(endTime);

      const currentHour = nowCst.getHours();
      const currentMinutes = nowCst.getMinutes();

      const isAfterStart = currentHour > start.hour || (currentHour === start.hour && currentMinutes >= start.minutes);
      const isBeforeEnd = currentHour < end.hour || (currentHour === end.hour && currentMinutes <= end.minutes);

      setIsBusinessHours(isAfterStart && isBeforeEnd);
    };

    checkBusinessHours();

    const intervalId = setInterval(checkBusinessHours, 60000);

    return () => clearInterval(intervalId);
  }, [businessHours]);

  return (
    <div className="flex flex-row items-start gap-4">
      <div className="relative overflow-visible">
        <Avatar className="h-10 w-10">
          <AvatarImage src={avatarUrl} alt="Support agent" />
          <AvatarFallback>AG</AvatarFallback>
        </Avatar>
        {isBusinessHours && (
          <div className="absolute right-0 -bottom-1 z-10 h-4 w-4 rounded-full border-[3px] border-background bg-[#008d86]" />
        )}
      </div>
      <div className="flex flex-col gap-1">
        <span className="text-body-sm font-bold text-body-highlight">{`We're here to help!`}</span>

        <div className="w-full text-body-normal">
          <a href={`tel:${phoneNumber}`}>
            <span>{phoneNumber}</span>
          </a>
        </div>

        {isBusinessHours && (
          <div>
            <Button
              variant="ghost"
              size="fit"
              className="text-body-highlight hover:no-underline"
              onClick={handleOpenChat}
            >
              Start Live Chat
              <Icon icon="arrow-chevron-right" className="-ml-1 h-5 w-5" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
