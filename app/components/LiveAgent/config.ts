import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import LiveAgent from '.';

export const LIVE_AGENT_COMPONENT: RegisteredComponent = {
  name: 'Live Agent',
  component: LiveAgent,
  inputs: [
    {
      name: 'avatarUrl',
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/Customer_Service_Rep.png?v=1743029680',
    },
    {
      name: 'phoneNumber',
      type: 'string',
      defaultValue: '(*************',
    },
    {
      name: 'businessHours',
      type: 'object',
      helperText:
        'Enter business hours in this format: 10:00AM-6:00PM. No spaces. Leave blank for closed. Times are in CST by default.',
      defaultValue: {
        monday: '9:30AM-5:00PM',
        tuesday: '9:30AM-5:00PM',
        wednesday: '9:30AM-5:00PM',
        thursday: '9:30AM-5:00PM',
        friday: '9:30AM-5:00PM',
      },
      subFields: [
        {
          name: 'monday',
          type: 'string',
        },
        {
          name: 'tuesday',
          type: 'string',
        },
        {
          name: 'wednesday',
          type: 'string',
        },
        {
          name: 'thursday',
          type: 'string',
        },
        {
          name: 'friday',
          type: 'string',
        },
        {
          name: 'saturday',
          type: 'string',
        },
        {
          name: 'sunday',
          type: 'string',
        },
      ],
    },
  ],
};
