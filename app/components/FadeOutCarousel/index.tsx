import { useRef, useState, useMemo } from 'react';
import gsap from 'gsap';
import Icon from '../Icon';
import { Button } from '../ui/button';
import { Blocks, type BuilderBlock, isEditing } from '@builder.io/sdk-react/edge';
import className from 'classnames';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { useIsomorphicLayoutEffect } from '@/hooks/use-isomorphic-effect';
import { cn } from '@/lib/utils';

const CARD_WIDTH_DESKTOP = 486;
const CARD_WIDTH_MOBILE = 285;
const CARD_GAP_DESKTOP = 32;
const CARD_GAP_MOBILE = 16;

interface IProps {
  slides?: { slideBlocks: BuilderBlock[] }[];
  builderBlock: BuilderBlock;
  showAllSlidesInEditor?: boolean;
  hasStationaryBlock?: boolean;
  blocks: BuilderBlock[];
}

export default function Carousel({
  slides = [],
  showAllSlidesInEditor,
  hasStationaryBlock,
  builderBlock,
  blocks,
}: IProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  const carouselRef = useRef<HTMLDivElement>(null);
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const isAnimating = useRef(false);
  const isMobile = useIsMobileBreakpoint();
  const CARD_WIDTH = isMobile ? CARD_WIDTH_MOBILE : CARD_WIDTH_DESKTOP;
  const CARD_GAP = isMobile ? CARD_GAP_MOBILE : CARD_GAP_DESKTOP;
  const CARD_SPACE = CARD_WIDTH + CARD_GAP;
  const slidesWithId = useMemo(() => slides.map((slide, i) => ({ ...slide, id: `slide-${i}` })), [slides]);

  const duplicationFactor = !isEditing() ? 6 : 1;
  const itemsToRender = useMemo(
    () =>
      Array.from({ length: duplicationFactor }).flatMap((_, setIndex) =>
        slidesWithId.map((slide, slideIndex) => ({
          ...slide,
          id: `slide-${setIndex}-${slideIndex}`,
        })),
      ),
    [duplicationFactor, slidesWithId],
  );

  useIsomorphicLayoutEffect(() => {
    const middleSetIndex = Math.floor((duplicationFactor * slides.length) / 2);
    setActiveIndex(middleSetIndex);

    // Position the carousel to show the active card
    if (carouselRef.current) {
      const offset = -(middleSetIndex * CARD_SPACE);
      carouselRef.current.style.transform = `translateX(${offset}px)`;
    }
  }, [duplicationFactor, slides.length, CARD_SPACE]);

  const nextSlide = () => {
    if (isAnimating.current) return;

    const currentCard = cardRefs.current[activeIndex];
    const nextCard = cardRefs.current[activeIndex + 1];

    if (currentCard && nextCard && carouselRef.current) {
      isAnimating.current = true;

      gsap
        .timeline({
          onComplete: () => {
            setActiveIndex(prev => prev + 1);
            isAnimating.current = false;
          },
        })
        .to(carouselRef.current, {
          translateX: -((activeIndex + 1) * CARD_SPACE),
          duration: 0.6,
        });
    }
  };

  const prevSlide = () => {
    if (isAnimating.current) return;

    const currentCard = cardRefs.current[activeIndex];
    const prevCard = cardRefs.current[activeIndex - 1];

    if (currentCard && prevCard && carouselRef.current) {
      isAnimating.current = true;

      gsap
        .timeline({
          onComplete: () => {
            isAnimating.current = false;
          },
          onStart: () => setActiveIndex(prev => prev - 1),
        })
        .to(carouselRef.current, {
          translateX: -((activeIndex - 1) * CARD_SPACE),
          duration: 0.6,
        });
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
    setTouchEnd(null);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const minSwipeDistance = 50;

    if (Math.abs(distance) >= minSwipeDistance) {
      if (distance > 0) {
        nextSlide();
      } else {
        prevSlide();
      }
    }

    setTouchStart(null);
    setTouchEnd(null);
  };

  function getOriginalSlideNumber(currentSlideIndex: number, totalOriginalSlides: number) {
    return currentSlideIndex % totalOriginalSlides;
  }

  if (showAllSlidesInEditor && isEditing()) {
    return (
      <div className="flex flex-wrap">
        {slidesWithId.map((item, idx) => (
          <div
            key={JSON.stringify(item)}
            className={className(
              'min-h-32 w-[285px] flex-shrink-0 overflow-hidden md:w-[486px]',
              'ml-[16px] md:ml-[32px]',
            )}
            ref={el => (cardRefs.current[idx] = el)}
          >
            <Blocks
              parent={builderBlock?.id}
              path={`slides.${idx}.slideBlocks`}
              blocks={slidesWithId[idx]?.slideBlocks}
            />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(
        'flex w-full flex-col-reverse items-start gap-10 overflow-x-hidden md:gap-0',
        hasStationaryBlock && 'flex-col md:flex-row',
      )}
    >
      <div className={cn('z-10 flex w-full flex-col gap-6 pl-[5%] md:mt-20', hasStationaryBlock && 'md:mt-0 md:w-1/3')}>
        {hasStationaryBlock && <Blocks parent={builderBlock?.id} path={'blocks'} blocks={blocks} />}
        <div className="flex gap-6">
          <Button variant="outline" size="icon" onClick={prevSlide}>
            <Icon icon="arrow-left" />
          </Button>
          <Button variant="outline" size="icon" onClick={nextSlide}>
            <Icon icon="arrow-right" />
          </Button>
        </div>
      </div>
      <div
        className={cn('w-full overflow-hidden rounded-l-sm', hasStationaryBlock ? 'md:w-2/3' : 'md:w-full')}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div ref={carouselRef} className="flex">
          {itemsToRender.map((item, idx) => {
            const realIndex = getOriginalSlideNumber(idx, slides.length);
            return (
              <div
                key={JSON.stringify(item)}
                className={className(
                  'min-h-32 w-[285px] flex-shrink-0 overflow-hidden md:w-[486px]',
                  'ml-[16px] md:ml-[32px]',
                )}
                ref={el => (cardRefs.current[idx] = el)}
              >
                <Blocks
                  parent={builderBlock?.id}
                  path={`slides.${realIndex}.slideBlocks`}
                  blocks={slides[realIndex]?.slideBlocks}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
