import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import FadeOutCarousel from '.';

export const FADE_OUT_CAROUSEL_COMPONENT: RegisteredComponent = {
  name: 'Fade Out Carousel',
  component: FadeOutCarousel,
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  childRequirements: {
    message: 'You can only put Text, or Multi Color Heading',
    query: {
      'component.name': { $in: ['Text', 'Multi Color Heading'] },
    },
  },
  inputs: [
    {
      name: 'showAllSlidesInEditor',
      type: 'boolean',
      helperText:
        'In order to be able to edit all the slides, you need to set this to true, otherwise they are not visible. This has no effect on the live page.',
      defaultValue: false,
    },
    {
      name: 'blocks',
      type: 'uiBlocks',
      hideFromUI: true,
      defaultValue: [],
    },
    {
      name: 'hasStationaryBlock',
      type: 'boolean',
      defaultValue: false,
      helperText: 'If true, the carousel will have space for a stationary block on the left side',
    },
    {
      name: 'slides',
      type: 'list',
      defaultValue: [
        {
          slideBlocks: [],
        },
      ],
      subFields: [
        {
          name: 'slideBlocks',
          type: 'uiBlocks',
          hideFromUI: true,
          defaultValue: [],
        },
      ],
    },
  ],
};
