import { RegisteredComponent } from '@builder.io/sdk-react/edge';
import ProductAddToCart from '.';

export const PRODUCT_ADD_TO_CART_COMPONENT: RegisteredComponent = {
  name: 'Product Add to Cart',
  component: ProductAddToCart,
  requiresParent: {
    message: 'Product Add to Cart must be inside a Product Data Provider',
    component: 'Product Data Provider',
  },
  canHaveChildren: true,
  inputs: [
    {
      type: 'boolean',
      name: 'hideDiscountMessages',
      defaultValue: false,
    },
  ],
};
