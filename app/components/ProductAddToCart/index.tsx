import { CartForm } from '@shopify/hydrogen';
import { useState, useEffect } from 'react';
import { useIsBusinessSite } from '@/hooks/use-is-business-site';
import { Fetcher } from 'react-router';
import { useCart } from '@/hooks/use-cart';
import { useProduct } from '@/components/ProductDataProvider/context';
import QuantitySelect from '@/components/QuantitySelect';
import { Button } from '@/components/ui/button';
import Icon from '@/components/Icon';
import { SYSTEM_FEATURES } from '@/business/core/constants/features';
import { SystemFeatureHandle } from '@/business/core/types';
import { usePromiseEffect } from '@/hooks/use-promise-effect';
import useAdvertisedDiscounts from '@/hooks/use-advertised-discounts';
import { useCartSheet } from '../Cart/CartSheet';

interface IProps {
  analytics?: unknown;
  hideDiscountMessages?: boolean;
  children?: React.ReactNode;
}

function AddToCartForm({
  analytics,
  fetcher,
  limit,
  quantity,
  discountMessage,
  setQuantity,
  children,
}: {
  analytics?: unknown;
  fetcher: Fetcher;
  limit: number;
  quantity: number;
  discountMessage?: string;
  setQuantity: (quantity: number) => void;
  children?: React.ReactNode;
}) {
  const [variant, setVariant] = useState<'primary' | 'outline'>('primary');
  const [buttonText, setButtonText] = useState('Add to cart');
  const [showSuccess, setShowSuccess] = useState(false);
  const [showLoading, setShowLoading] = useState(false);
  const cartSheet = useCartSheet();

  useEffect(() => {
    if (fetcher.state === 'idle' && showLoading) {
      setShowLoading(false);
      setShowSuccess(true);
      setButtonText('Added to cart');
      setVariant('outline');
      cartSheet.toggleOpen();

      setTimeout(() => {
        setShowSuccess(false);
        setButtonText('Add to cart');
        setVariant('primary');
      }, 1500);
    } else if (fetcher.state === 'submitting' || fetcher.state === 'loading') {
      setShowLoading(true);
      setVariant('primary');
      setShowSuccess(false);
    }
  }, [fetcher.state]);

  return (
    <div className="flex flex-col items-start gap-2 md:gap-3">
      <div className="flex w-full items-center gap-2 md:gap-3">
        <input name="analytics" type="hidden" value={JSON.stringify(analytics)} />
        <QuantitySelect limit={limit} value={quantity} onValueChange={setQuantity} />
        <div className="flex flex-1 items-center gap-2">
          <Button type="submit" size="full" variant={variant} loading={showLoading} className="flex-1">
            {showSuccess && <Icon icon="check" className="mr-2" />}
            {buttonText}
          </Button>
          {children}
        </div>
      </div>
      {discountMessage && <div className="text-sm font-bold text-emphasis">{discountMessage}</div>}
    </div>
  );
}

export default function ProductAddToCart({ analytics, hideDiscountMessages, children }: IProps) {
  const { product, discounts: discountsPromise, selectedVariant } = useProduct();
  const { value: discounts } = usePromiseEffect(discountsPromise);
  const { message: discountMessage } = useAdvertisedDiscounts(product?.id!, discounts);

  const variantId = selectedVariant?.id;

  const deviceHandle = product?.deviceHandle?.value;
  const { cart } = useCart();
  const quantityInCart = !deviceHandle
    ? 0
    : cart?.lines?.nodes?.reduce(
        (acc, line) => (line.merchandise.product.deviceHandle?.value === deviceHandle ? acc + line.quantity : acc),
        0,
      ) || 0;
  const limit = deviceHandle ? SYSTEM_FEATURES[deviceHandle as SystemFeatureHandle]?.limit || 100 : 100;

  const [quantity, setQuantity] = useState(1);
  const isBusiness = useIsBusinessSite();
  const cartRoute = isBusiness ? '/business/cart' : '/home/<USER>';

  return (
    <CartForm
      route={cartRoute}
      inputs={{ lines: [{ merchandiseId: variantId!, quantity, selectedVariant }] }}
      action={CartForm.ACTIONS.LinesAdd}
    >
      {fetcher => (
        <AddToCartForm
          analytics={analytics}
          fetcher={fetcher}
          limit={limit - quantityInCart}
          quantity={quantity}
          setQuantity={setQuantity}
          discountMessage={!hideDiscountMessages ? discountMessage : undefined}
        >
          {children}
        </AddToCartForm>
      )}
    </CartForm>
  );
}
