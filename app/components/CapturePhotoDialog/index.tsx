import { ReactEventHandler, ReactNode, useCallback, useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogTitle, DialogTrigger } from '../ui/dialog';
import CameraCapture from '../CameraCapture';

export type CapturePhotoDialogProps = {
  title?: ReactNode;
  description?: ReactNode;
  onCapture?: (blob: Blob | null, width: number, height: number) => void;
  videoReadyCallback?: ReactEventHandler<HTMLVideoElement>;
  preferredZoom?: number;
  children: ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
};

export default function CapturePhotoDialog({
  onCapture,
  children,
  title,
  description,
  preferredZoom = 1,
  videoReadyCallback,
  open = false,
  onOpenChange,
}: CapturePhotoDialogProps) {
  const [isOpen, setIsOpen] = useState(open);

  const changeOpen = useCallback(
    (open: boolean) => {
      setIsOpen(open);
      onOpenChange?.(open);
    },
    [onOpenChange],
  );

  const handleCapture = useCallback(
    (blob: Blob | null, w: number, h: number) => {
      onCapture?.(blob, w, h);
      changeOpen(false);
    },
    [onCapture],
  );

  return (
    <Dialog open={onOpenChange ? open : isOpen} onOpenChange={changeOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className="h-full max-h-screen w-full max-w-screen grid-rows-1 gap-0 border-none bg-muted-foreground p-0 [&_div.sticky]:absolute [&_svg.lucide-x]:text-background"
        style={{ borderRadius: '0px' }}
      >
        <DialogTitle className="hidden">{title}</DialogTitle>
        <DialogDescription className="hidden">{description}</DialogDescription>
        <CameraCapture
          className="mx-auto max-h-fit max-w-fit"
          onCapture={onCapture ? handleCapture : undefined}
          videoReadyCallback={videoReadyCallback}
          preferredZoom={preferredZoom}
        >
          <div className="absolute top-14 left-1/2 -translate-x-1/2 text-center text-background">
            {title && <h3 className="text-2xl font-bold">{title}</h3>}
            {description && <p className="text-muted opacity-60">{description}</p>}
          </div>
          <div className="absolute top-40 right-[10%] bottom-40 left-[10%] border-x-4 border-background"></div>
          <div className="absolute top-40 bottom-40 left-[10%] w-10 border-y-4 border-background"></div>
          <div className="absolute top-40 right-[10%] bottom-40 w-10 border-y-4 border-background"></div>
        </CameraCapture>
      </DialogContent>
    </Dialog>
  );
}
