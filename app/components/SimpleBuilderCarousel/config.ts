import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import SimpleBuilderCarousel from '.';

export const SIMPLE_BUILDER_CAROUSEL_COMPONENT: RegisteredComponent = {
  name: 'Carousel',
  component: SimpleBuilderCarousel,
  defaultStyles: {
    height: '50vh',
  },
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  inputs: [
    {
      name: 'badgeText',
      type: 'string',
      helperText: 'Leave blank to hide the badge',
    },
    {
      name: 'hasGradient',
      type: 'boolean',
      defaultValue: true,
      helperText: 'If enabled shows a gradient at the bottom for making text more readable',
    },
    {
      name: 'aspectRatio',
      type: 'number',
      defaultValue: 3 / 2,
      helperText: 'The aspect ratio of the images',
    },
    {
      name: 'sizes',
      type: 'string',
      defaultValue: '(max-width: 1199px) 90vw, 50vw',
    },
    {
      name: 'items',
      type: 'list',
      defaultValue: [
        {
          imageUrl:
            'https://cdn.shopify.com/s/files/1/0708/3078/4833/files/different-way-long-driveway-mailbox.jpg?v=1701285880',
          alt: 'Placeholder',
        },
      ],
      subFields: [
        { name: 'imageUrl', type: 'file', allowedFileTypes: ['jpeg', 'jpg', 'png', 'gif'], required: true },
        { name: 'alt', type: 'string', required: true },
      ],
    },
  ],
};
