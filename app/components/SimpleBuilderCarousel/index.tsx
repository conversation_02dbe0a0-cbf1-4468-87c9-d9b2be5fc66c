import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselProvider,
  CarouselPagination,
} from '../ui/carousel';
import { cn, generateImageSrcSet } from '@/lib/utils';
import { Blocks, type BuilderBlock } from '@builder.io/sdk-react/edge';

export type SimpleBuilderCarouselItem = {
  imageUrl: string;
  alt: string;
  blocks: BuilderBlock[];
};

interface IProps {
  items: SimpleBuilderCarouselItem[];
  badgeText?: string;
  insideCarousel?: boolean;
  builderBlock: BuilderBlock;
  slideBlocks: BuilderBlock[][];
  className?: string;
  hasGradient?: boolean;
  aspectRatio?: number;
  sizes: string;
}

export default function SimpleBuilderCarousel({
  items = [],
  badgeText,
  insideCarousel = true,
  builderBlock,
  slideBlocks,
  className,
  hasGradient = true,
  aspectRatio = 3 / 2,
  sizes,
}: IProps) {
  return (
    <div className={cn(className)}>
      <CarouselProvider>
        <Carousel className="relative">
          {badgeText && (
            <div className="absolute top-5 right-5 z-10 rounded-full bg-background-branded px-6 py-2 text-body-xxs font-bold text-background sm:top-10 sm:right-10 sm:text-body-xs">
              {badgeText}
            </div>
          )}
          <CarouselContent>
            {items.map((item, idx) => (
              <CarouselItem key={item.imageUrl} className="overflow-hidden rounded-sm">
                <div className="relative overflow-hidden" style={{ aspectRatio }}>
                  <img
                    alt={item.alt}
                    src={item.imageUrl}
                    srcSet={generateImageSrcSet(item.imageUrl)}
                    className="size-full object-cover"
                    sizes={sizes}
                  />
                  {hasGradient && (
                    <div className="absolute bottom-0 left-0 h-[40%] w-full bg-linear-to-t from-black/80 to-transparent" />
                  )}
                  {slideBlocks && (
                    <div className="absolute right-0 bottom-0 left-0 flex flex-col p-4 text-white">
                      <Blocks parent={builderBlock.id} path={`items.${idx}.mobileBlocks`} blocks={slideBlocks[idx]} />
                    </div>
                  )}
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="-left-5" />
          <CarouselNext className="-right-5" />

          <CarouselPagination
            itemCount={items.length}
            className={insideCarousel ? 'absolute right-0 bottom-6 left-0' : 'my-4'}
          />
        </Carousel>
      </CarouselProvider>
    </div>
  );
}
