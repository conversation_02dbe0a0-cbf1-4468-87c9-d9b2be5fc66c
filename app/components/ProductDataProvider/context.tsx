import { DiscountsPromise, ProductData, SubscriptionPromise } from '@/routes/($theme).$handle.product/loader';
import {
  getAdjacentAndFirstAvailableVariants,
  getProductOptions,
  useOptimisticVariant,
  useSelectedOptionInUrlParam,
} from '@shopify/hydrogen';
import { createContext, useContext } from 'react';
import { ProductVariantFragment } from 'storefront.generated';

type ProductContextType = {
  product: ProductData | undefined | null;
  selectedVariant: (ProductVariantFragment & { isOptimistic?: boolean }) | undefined | null;
  discounts: DiscountsPromise | undefined | null;
  subscription: SubscriptionPromise | undefined | null;
  setFetcherSearchQuery?: (urlSearchQuery: string) => void;
};

export const ProductContext = createContext<ProductContextType | undefined>(undefined);

export function useProduct() {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error('useProduct must be used within a ProductProvider');
  }
  return context;
}
