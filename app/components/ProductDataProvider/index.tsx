import useTheme from '@/hooks/use-theme';
import {
  DiscountsPromise,
  ProductData,
  ProductLoaderData,
  SubscriptionPromise,
} from '@/routes/($theme).$handle.product/loader';
import { UIMatch, useFetcher, useMatches, useNavigation } from 'react-router';
import { getAdjacentAndFirstAvailableVariants, useSelectedOptionInUrlParam } from '@shopify/hydrogen';
import { useEffect, useRef, useState } from 'react';
import { Skeleton } from '../ui/skeleton';
import { ProductContext } from './context';

type IProps = {
  children: React.ReactNode;
  handle?: string;
  product?: ProductData;
  discounts?: DiscountsPromise;
  subscription?: SubscriptionPromise;
  descriptionHtmlOverwrite?: string;
};

export default function ProductDataProvider({ children, descriptionHtmlOverwrite, ...data }: IProps) {
  // const [showSkeleton, setShowSkeleton] = useState(false);
  const fetcher = useFetcher<ProductLoaderData>();

  const matches = useMatches();
  const match = matches.find(match => match.id?.endsWith('$handle.product')) as UIMatch<ProductLoaderData>;

  const usingFetcher = Boolean(data?.handle);
  const [urlSearchQuery, setUrlSearchQuery] = useState<string | undefined>();
  //Load data from handle > from prop > from loader in that order of priority
  const product = usingFetcher ? fetcher.data?.product : data?.product || match?.data?.product;
  const discounts = usingFetcher ? fetcher.data?.discounts : data?.discounts || match?.data?.discounts;
  const subscription = usingFetcher ? fetcher.data?.subscription : data?.subscription || match?.data?.subscription;

  const theme = useTheme();
  const requestRef = useRef('');

  useEffect(() => {
    if (!usingFetcher) return;

    const url = `/${data.handle}/product?${urlSearchQuery || ''}`;

    if (requestRef.current === url) return;
    requestRef.current = url;

    fetcher.load(url);
  }, [fetcher, theme, urlSearchQuery]);

  const variants = product ? getAdjacentAndFirstAvailableVariants(product) : [];
  let selectedVariant = product?.selectedOrFirstAvailableVariant;

  const navigation = useNavigation();
  if ((usingFetcher && navigation.state == 'loading') || (!usingFetcher && navigation.state == 'loading')) {
    const searchParams = new URLSearchParams(usingFetcher ? '?' + urlSearchQuery || '' : navigation.location.search);

    const foundVariant = variants.find(variant => {
      return variant.selectedOptions.every(option => {
        return searchParams.get(option.name) === option.value;
      });
    });

    if (foundVariant) {
      selectedVariant = foundVariant as typeof selectedVariant;
      (selectedVariant as any).isOptimistic = true;
    }
  }

  useSelectedOptionInUrlParam(usingFetcher ? [] : selectedVariant?.selectedOptions || []);

  if (!children) {
    return <Skeleton className="h-52 w-full" />;
  }

  if (descriptionHtmlOverwrite && product) product.descriptionHtml = descriptionHtmlOverwrite;

  return (
    <ProductContext.Provider
      value={{
        // product: showSkeleton ? undefined : product ? product : fetcher.state == 'loading' ? undefined : nu
        product: product ? product : fetcher.state == 'loading' ? undefined : null,
        selectedVariant: selectedVariant ? selectedVariant : fetcher.state == 'loading' ? undefined : null,
        subscription: subscription ? subscription : fetcher.state == 'loading' ? undefined : null,
        discounts: discounts ? discounts : fetcher.state == 'loading' ? undefined : null,
        setFetcherSearchQuery: usingFetcher ? setUrlSearchQuery : undefined,
      }}
    >
      {/* <Checkbox checked={showSkeleton} onCheckedChange={checked => setShowSkeleton(checked == true)}></Checkbox> */}
      {children}
    </ProductContext.Provider>
  );
}
