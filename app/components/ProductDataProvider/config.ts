import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import ProductDataProvider from '.';

export const PRODUCT_DATA_PROVIDER_COMPONENT: RegisteredComponent = {
  name: 'Product Data Provider',
  component: ProductDataProvider,
  canHaveChildren: true,
  inputs: [
    {
      name: 'handle',
      type: 'string',
      required: true,
      helperText:
        'Make sure to first go to Connect Data > Add Data > Shopify > Product > Filter data by Entry > Choose the product. Then, back here, you need to click the database icon right next to the label for this input > Choose data > Select "Product > Handle"',
    },
    {
      name: 'descriptionHtmlOverwrite',
      type: 'richText',
      helperText: 'If provided, this will overwrite the product description',
    },
  ],
};
