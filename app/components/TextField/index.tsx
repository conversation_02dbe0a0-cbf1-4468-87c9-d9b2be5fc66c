import { Input, InputProps } from '@/components/ui/input';
import { useRemixFormContext } from 'remix-hook-form';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';

interface TextFieldProps extends Omit<InputProps, 'form'> {
  name: string;
  label?: string;
  placeholder?: string;
  description?: string;
  disabled?: boolean;
  className?: string;
}

export default function TextField({
  name,
  label,
  placeholder,
  description,
  disabled,
  className,
  ...rest
}: TextFieldProps) {
  const form = useRemixFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Input
              autoComplete="off"
              disabled={disabled}
              placeholder={placeholder}
              className={className}
              {...field}
              {...rest}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
