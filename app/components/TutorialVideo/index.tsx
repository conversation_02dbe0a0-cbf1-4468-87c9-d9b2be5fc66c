import { useRef, useState } from 'react';
import PlayButton from '../PlayButton';

interface IProps {
  videoUrl: string;
  posterImageUrl: string;
  trackSrc: string;
  playing: boolean;
  muted: boolean;
  loop: boolean;
  controls: boolean;
  preload: 'metadata' | 'auto' | 'none';
}

export default function TutorialVideo({ videoUrl, posterImageUrl, trackSrc, playing, muted, loop, preload }: IProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasClickedPlay, setHasClickedPlay] = useState(false);

  function handleClickPlay() {
    const currentVideo = videoRef.current;

    if (!isPlaying) {
      currentVideo?.play();
    } else {
      currentVideo?.pause();
    }

    if (!hasClickedPlay) {
      setHasClickedPlay(true);
    }

    setIsPlaying(!isPlaying);
  }

  return (
    <div className="relative h-full w-full">
      <video
        ref={videoRef}
        src={videoUrl}
        poster={posterImageUrl}
        preload={preload}
        autoPlay={playing}
        muted={muted}
        loop={loop}
        controls={hasClickedPlay}
        style={{
          objectFit: 'cover',
          objectPosition: 'center',
        }}
        className="aspect-video w-full rounded-sm md:rounded-md"
        crossOrigin="anonymous"
      >
        <track kind="captions" src={trackSrc} label="Captions" srcLang="en" />
      </video>
      {!hasClickedPlay && <PlayButton onClick={handleClickPlay} />}
    </div>
  );
}
