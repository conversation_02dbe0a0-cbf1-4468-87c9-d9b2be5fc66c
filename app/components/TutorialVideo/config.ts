import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import TutorialVideo from '.';

export const TUTORIAL_VIDEO_COMPONENT: RegisteredComponent = {
  name: 'Tutorial Video',
  component: TutorialVideo,
  inputs: [
    {
      name: 'videoUrl',
      required: true,
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/videos/c/o/v/48107705a5a94ccab37340062b8e56cc.mp4',
    },
    {
      name: 'posterImageUrl',
      required: true,
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/video-img-thumb-desktop.jpg?v=1742489530',
    },
    {
      name: 'trackSrc',
      type: 'url',
      required: true,
      helperText: 'The URL of the track file. Must be a .vtt file.',
    },
    {
      name: 'playing',
      type: 'boolean',
      defaultValue: false,
    },
    {
      name: 'muted',
      type: 'boolean',
      defaultValue: false,
    },
    {
      name: 'loop',
      type: 'boolean',
      defaultValue: false,
    },
    {
      name: 'preload',
      type: 'enum',
      defaultValue: 'metadata',
      enum: [
        { label: 'Metadata', value: 'metadata' },
        { label: 'Auto', value: 'auto' },
        { label: 'None', value: 'none' },
      ],
    },
  ],
};
