import { Slot } from '@radix-ui/react-slot';
import { ChangeEvent, InputHTMLAttributes, ReactNode, useCallback, useRef, useState } from 'react';
import Compressor from 'compressorjs';
import { useToast } from '@/hooks/use-toast';
import { VIN_IDENTIFIER_IMAGE_SIZE } from '@/business/core/constants/vin';

interface ImageUploaderProps
  extends Omit<
    InputHTMLAttributes<HTMLInputElement>,
    'style' | 'className' | 'ref' | 'type' | 'onChange' | 'multiple' | 'accept'
  > {
  children: ReactNode;
  onUpload?: (file: File | Blob | null) => void;
}

export default function ImageUploader({ children, onUpload, ...inputProps }: ImageUploaderProps) {
  const hiddenFileInput = useRef<HTMLInputElement | null>(null);
  const { toast } = useToast();
  const handleClick = useCallback(() => {
    hiddenFileInput?.current?.click();
  }, []);

  const handleChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const file = event.target?.files?.[0] || null;

      if (!file) {
        return;
      }

      event.target.value = '';

      new Compressor(file, {
        quality: 0.8,
        convertTypes: 'image/*',
        convertSize: 500000, //500kb
        maxWidth: VIN_IDENTIFIER_IMAGE_SIZE,
        maxHeight: VIN_IDENTIFIER_IMAGE_SIZE,
        mimeType: 'image/jpeg',
        success: onUpload,
        error(error) {
          console.error('IMAGE_UPLOAD_ERROR', error);
          toast({
            variant: 'destructive',
            title: 'Upload failed',
            description: 'Could not process image, please try a different image.',
          });
        },
      });
    },
    [onUpload],
  );
  return (
    <>
      <Slot onClick={handleClick}>{children}</Slot>
      <input
        type="file"
        ref={hiddenFileInput}
        onChange={handleChange}
        style={{ display: 'none' }}
        accept="image/*"
        multiple={false}
        {...inputProps}
      />
    </>
  );
}
