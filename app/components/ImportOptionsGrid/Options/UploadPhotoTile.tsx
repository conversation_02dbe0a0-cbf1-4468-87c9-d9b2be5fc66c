import { VINIdentificationState } from '@/business/core/types/vin';
import ImageUploader from '@/components/ImageUploader';
import { useToast } from '@/hooks/use-toast';
import { useVINIdentifier } from '@/hooks/use-vin-identifier';
import { Loader2, Upload } from 'lucide-react';

export default function UploadPhotoTile({
  onVINsImported,
}: {
  onVINsImported: (VINs: VINIdentificationState[]) => void;
}) {
  const { toast } = useToast();
  const { identify, loading } = useVINIdentifier({
    onSuccess: response => {
      if (response.error) {
        toast({
          title: 'Error',
          variant: 'destructive',
          description: response.error,
        });
      }

      if (response.results.length && onVINsImported) {
        onVINsImported(response.results);
      }
    },
  });

  return (
    <ImageUploader onUpload={identify}>
      <button
        className="flex aspect-square w-full flex-col items-center justify-center gap-2 rounded-sm bg-card shadow-lg"
        disabled={loading}
      >
        {loading ? (
          <Loader2 className="size-10 animate-spin text-primary" />
        ) : (
          <>
            <Upload className="size-10 text-primary" />
            <p className="text-xs font-bold text-muted-foreground">Upload Photo</p>
          </>
        )}
      </button>
    </ImageUploader>
  );
}
