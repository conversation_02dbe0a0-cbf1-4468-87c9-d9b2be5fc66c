import { CopyLinkField } from '@/components/CopyLinkField';
import {
  Drawer,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { useToast } from '@/hooks/use-toast';
import { CheckSquare, Co<PERSON>, Link } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

export default function ShareLinkTile() {
  const [open, setOpen] = useState(false);

  const close = useCallback(() => {
    setOpen(false);
  }, []);

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <button className="flex aspect-square w-full flex-col items-center justify-center gap-2 rounded-sm bg-card shadow-lg">
          <Link className="size-10 text-primary" />
          <p className="text-xs font-bold text-muted-foreground">Share Link</p>
        </button>
      </DrawerTrigger>
      <DrawerContent>
        <div className="mx-auto w-full max-w-sm">
          <DrawerHeader>
            <DrawerTitle>Share Link</DrawerTitle>
            <DrawerDescription>
              Need your team to add vehicles? Share the link below for them to add additional vehicles.
            </DrawerDescription>
          </DrawerHeader>
          <DrawerFooter className="mb-8">
            <CopyLinkField
              onCopied={close}
              template={`Below is a link to add your vehicle(s) to the All Aware system. Open the link and snap a photo of the license plate or VIN on each vehicle. Set a vehicle nickname and submit. {link}`}
            />
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
