import { VINIdentificationState } from '@/business/core/types/vin';
import CapturePhotoDialog from '@/components/CapturePhotoDialog';
import { useToast } from '@/hooks/use-toast';
import { useVINIdentifier } from '@/hooks/use-vin-identifier';
import { Camera, Loader2 } from 'lucide-react';

export default function CapturePhotoTile({
  onVINsImported,
}: {
  onVINsImported: (VINs: VINIdentificationState[]) => void;
}) {
  const { toast } = useToast();
  const { identify, loading } = useVINIdentifier({
    onSuccess: response => {
      if (response.error) {
        toast({
          title: 'Error',
          variant: 'destructive',
          description: response.error,
        });
      }

      if (response.results.length && onVINsImported) {
        onVINsImported(response.results);
      }
    },
  });

  return (
    <CapturePhotoDialog
      title="Take a Photo"
      description="Take a photo of the vehicle’s VIN or license plate to import it into your vehicle list."
      onCapture={identify}
    >
      <button
        className="flex aspect-square w-full flex-col items-center justify-center gap-2 rounded-sm bg-card shadow-lg"
        disabled={loading}
      >
        {loading ? (
          <Loader2 className="size-10 animate-spin text-primary" />
        ) : (
          <>
            <Camera className="size-10 text-primary" />
            <p className="text-xs font-bold text-muted-foreground">Take a Photo</p>
          </>
        )}
      </button>
    </CapturePhotoDialog>
  );
}
