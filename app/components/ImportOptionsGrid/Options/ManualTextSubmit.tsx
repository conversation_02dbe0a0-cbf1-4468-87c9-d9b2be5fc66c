import { VINIdentificationState } from '@/business/core/types/vin';
import { Button } from '@/components/ui/button';
import VINTextArea from '@/components/VINTextArea';
import { useCallback, useEffect, useRef, useState } from 'react';

export default function ManualTextSubmit({
  onVINsImported,
}: {
  onVINsImported: (VINs: VINIdentificationState[]) => void;
}) {
  const [text, setText] = useState<string>('');
  const VINsRef = useRef<string[]>([]);
  const setVINs = useCallback((VINs: string[]) => {
    VINsRef.current = VINs;
  }, []);

  const submit = useCallback(() => {
    setText('');
    onVINsImported?.(
      VINsRef.current.map(v => ({
        licensePlate: null,
        VIN: v,
        MMY: undefined, //Indicates that the VIN is not yet decoded, but may still be valid
      })),
    );
  }, [onVINsImported]);

  return (
    <div className="space-y-6">
      <VINTextArea
        placeholder="Or enter VINs manually e.g. 1G1BL6ED8SF215493, JTDKN3FU5RT789102"
        onVINsChanged={setVINs}
        text={text}
        setText={setText}
      />
      <Button variant="primary" size="full" onClick={submit}>
        Add Vehicles
      </Button>
    </div>
  );
}
