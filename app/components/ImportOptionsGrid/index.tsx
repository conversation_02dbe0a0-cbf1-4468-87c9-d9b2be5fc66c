import { VINIdentificationState } from '@/business/core/types/vin';
import CapturePhotoTile from './Options/CapturePhotoTile';
import ManualTextSubmit from './Options/ManualTextSubmit';
import UploadPhotoTile from './Options/UploadPhotoTile';
import ShareLinkTile from './Options/ShareLinkTile';
import { cn } from '@/lib/utils';

export function ImportOptionsGrid({
  onVINsImported,
  hideShare,
  hideCapture,
  hideUpload,
  hideManual,
  className,
  hideDisclaimer,
}: {
  onVINsImported: (VINs: VINIdentificationState[]) => void;
  hideShare?: boolean;
  hideCapture?: boolean;
  hideUpload?: boolean;
  hideManual?: boolean;
  hideDisclaimer?: boolean;
  className?: string;
}) {
  return (
    <div className={cn('w-full space-y-5', className)}>
      <div className="flex flex-nowrap gap-2">
        {!hideCapture && <CapturePhotoTile onVINsImported={onVINsImported} />}
        {!hideUpload && <UploadPhotoTile onVINsImported={onVINsImported} />}
        {!hideShare && <ShareLinkTile />}
      </div>
      {!hideManual && <ManualTextSubmit onVINsImported={onVINsImported} />}
      {!hideDisclaimer && (
        <p className="text-xs text-muted-foreground italic">
          Please note, we do not store photos or documents once your vehicles are added.
        </p>
      )}
    </div>
  );
}
