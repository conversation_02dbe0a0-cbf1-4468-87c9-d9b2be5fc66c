import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
  FC,
} from 'react';

//===============================================
// Base Step State Definition
//===============================================
// Base interface that specific step states should extend
export type BaseStepState = {
  name: string;
  skip?: boolean;
  complete?: boolean;
};

//===============================================
// Factory for Wizard Form Context/Provider/Hook
//===============================================

interface WizardFormProviderProps<F extends Record<string, any> = Record<string, any>> {
  children: ReactNode;
  initialData?: F;
  onSubmit?: (finalData: F) => void;
}

// (No changes from the previous factory version based on your WizardCore.tsx)
export function createWizardFormContext<F extends Record<string, any> = Record<string, any>>() {
  interface WizardFormContextType {
    formData: F;
    commit: (newData: Partial<F>) => void;
    reset: (initialData?: F) => void;
    submit: (resetData?: F, reset?: boolean) => void;
  }

  // Create context with undefined initial value
  const WizardFormContext = createContext<WizardFormContextType | undefined>(undefined);

  const WizardFormProvider: FC<WizardFormProviderProps<F>> = ({ children, initialData = {} as F, onSubmit }) => {
    const [formData, setFormData] = useState<F>(initialData);

    const commit = useCallback((newData: Partial<F>) => {
      setFormData(prevData => ({
        ...prevData,
        ...newData,
      }));
    }, []);

    const reset = useCallback(
      (data: F = initialData) => {
        setFormData(data);
      },
      [initialData],
    );

    const submit = useCallback(
      (data?: F, reset?: boolean) => {
        setFormData(finalData => {
          const newData = data || finalData;
          onSubmit?.(newData as F);
          return reset ? newData : finalData;
        });
      },
      [onSubmit],
    );

    const value = useMemo(
      () => ({
        formData,
        commit,
        reset,
        submit,
      }),
      [formData, commit, reset, submit],
    );

    return <WizardFormContext.Provider value={value}>{children}</WizardFormContext.Provider>;
  };

  const useWizardForm = (): WizardFormContextType => {
    const context = useContext(WizardFormContext);
    if (context === undefined) {
      throw new Error('useWizardForm must be used within a WizardFormProvider');
    }
    return context as WizardFormContextType;
  };

  return {
    WizardFormContext, // Expose the actual context object
    WizardFormProvider,
    useWizardForm,
  };
}

//=====================================================
// Factory for Wizard Steps Context/Provider/Hook/Step
//=====================================================

interface WizardStepsProviderProps<S extends BaseStepState = BaseStepState> {
  children: ReactNode;
  steps: (S | string)[];
  onStepChanged?: (step: string) => void;
  onComplete?: () => void;
  initialStep?: string;
}

export function createWizardStepsContext<S extends BaseStepState = BaseStepState>() {
  interface WizardStepsContextType {
    steps: S[]; // Use generic S
    totalSteps: number;
    completedSteps: number;
    stepIndex: number;
    step: string;
    nextStep: () => void;
    prevStep: () => void;
    goToStep: (stepName: string) => void;
  }

  const WizardStepsContext = createContext<WizardStepsContextType | undefined>(undefined);

  const WizardStepsProvider: FC<WizardStepsProviderProps<S>> = ({
    children,
    steps: _steps,
    onStepChanged,
    onComplete,
    initialStep,
  }) => {
    // Adhere to original implementation: throw if steps is empty/null
    if (!_steps?.length) {
      throw new Error("WizardStepsProvider requires a non-empty 'steps' prop array.");
    }

    const steps = _steps.map(s => (typeof s == 'string' ? ({ name: s } as S) : s));

    const [_, setStepDirty] = useState(0);
    const makeStepDirty = useCallback(() => setStepDirty(prev => prev + 1), []);
    const navigationIntent = useRef<'next' | 'prev' | null>(null);
    const stepRef = useRef<string | null>(initialStep || null);

    function nextAvailableStep(): string | null {
      let nextStepName: string | null = null;
      let foundCurrent = false;
      for (const step of steps) {
        if (step.name === stepRef.current) {
          foundCurrent = true;
          continue;
        } else if (foundCurrent && !step.skip) {
          nextStepName = step.name;
          break;
        }
      }
      // Use 'complete' as the terminal step name convention
      return foundCurrent ? nextStepName || 'complete' : null;
    }

    function prevAvailableStep(): string | null {
      let prevStepName: string | null = null;
      for (const step of steps) {
        if (step.name === stepRef.current) break;
        if (!step.skip) prevStepName = step.name;
      }
      // Return previous valid step, or the current step if no valid previous step found
      return prevStepName ?? stepRef.current;
    }

    // Logic replicated from user's WizardCore.tsx
    if (navigationIntent.current === 'next') stepRef.current = nextAvailableStep();
    else if (navigationIntent.current === 'prev') stepRef.current = prevAvailableStep();
    navigationIntent.current = null;

    if (!stepRef.current) {
      stepRef.current =
        steps.find(step => !step.skip && !step.complete)?.name || steps.find(step => !step.skip)?.name || 'complete';
    }

    const goToStep = useCallback(
      (step: string) => {
        if (stepRef.current === step) return;

        // Check if target step exists and is not skipped before navigating
        const targetStepExists = steps.some(s => s.name === step && !s.skip);
        if (!targetStepExists) {
          console.warn(`WizardStepsProvider: Attempted to goToStep an invalid or skipped step "${step}"`);
          return;
        }

        stepRef.current = step;
        navigationIntent.current = null;
        makeStepDirty();
      },
      [makeStepDirty, steps], // steps dependency needed for check inside
    );

    const nextStep = useCallback(() => {
      if (navigationIntent.current === 'next') return;
      navigationIntent.current = 'next';
      makeStepDirty();
    }, [makeStepDirty]);

    const prevStep = useCallback(() => {
      if (navigationIntent.current === 'prev') return;
      navigationIntent.current = 'prev';
      makeStepDirty();
    }, [makeStepDirty]);

    const previousStepRef = useRef<string | null>(null);
    useEffect(() => {
      const currentStepVal = stepRef.current;
      if (previousStepRef.current !== currentStepVal && currentStepVal !== null) {
        onStepChanged?.(currentStepVal);
        if (currentStepVal === 'complete') onComplete?.();
        previousStepRef.current = currentStepVal;
      }
    }, [stepRef.current, onStepChanged, onComplete]);

    const value = useMemo(() => {
      const relevantSteps = steps.filter(step => !step.skip);
      const totalSteps = relevantSteps.length;
      const completedSteps = relevantSteps.reduce((acc, step) => acc + (step.complete ? 1 : 0), 0);
      const currentStepName = stepRef.current ?? 'complete';
      const stepIndex = relevantSteps.findIndex(step => step.name === currentStepName);

      return {
        steps: relevantSteps,
        totalSteps,
        completedSteps,
        stepIndex,
        step: currentStepName,
        nextStep,
        prevStep,
        goToStep,
      };
    }, [JSON.stringify(steps), stepRef.current, nextStep, prevStep, goToStep]);

    return <WizardStepsContext.Provider value={value}>{children}</WizardStepsContext.Provider>;
  };

  const useWizardSteps = (): WizardStepsContextType => {
    const context = useContext(WizardStepsContext);
    if (context === undefined) {
      throw new Error('useWizardSteps must be used within a WizardStepsProvider');
    }
    return context;
  };

  interface WizardStepProps {
    name: string;
    children: ReactNode;
  }

  const WizardStep: FC<WizardStepProps> = ({ name, children }) => {
    const { step } = useWizardSteps();

    if (step === name) {
      return <>{children}</>;
    }
    return null;
  };
  WizardStep.displayName = 'WizardStep';

  return {
    WizardStepsContext,
    WizardStepsProvider,
    useWizardSteps,
    WizardStep,
  };
}

export const { WizardStep, WizardStepsContext, WizardStepsProvider, useWizardSteps } = createWizardStepsContext();
