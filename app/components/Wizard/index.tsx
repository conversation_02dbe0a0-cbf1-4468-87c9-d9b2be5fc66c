import {
  Context,
  createContext,
  Dispatch,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

export type WizardStepDictionary = { [step: string]: number };
export type WizardStateBase = Record<string, unknown>;
export type WizardContext<T extends WizardStateBase> = {
  step: string;
  stepCount: number;
  stepIndex: number;
  state: T;
  steps: WizardStepDictionary;
  commit: (stepState: Partial<T>) => void;
  next: () => void;
  previous: () => void;
  getNextAvailableStep: () => string;
  getPreviousAvailableStep: () => string;
  goToStep: (step: string) => void;
  registerStep: (step: string, included: boolean, version: number) => void;
  refreshSteps: (stepVersion: number) => void;
  version: number;
  prospectiveStep: string;
  advanceProspectiveStep: () => void;
};

export type WizardStepState = {
  step: string;
  included: boolean;
  version: number;
};

const wizardContext = createContext<WizardContext<WizardStateBase>>({} as any);
export const useWizard = <T extends WizardStateBase>() => {
  const context = useContext(wizardContext) as WizardContext<T>;

  const filteredContext = useMemo(
    () => ({
      step: context.step,
      stepCount: context.stepCount,
      stepIndex: context.stepIndex,
      steps: context.steps,
      state: context.state,
      commit: context.commit,
      next: context.next,
      previous: context.previous,
      getNextAvailableStep: context.getNextAvailableStep,
      getPreviousAvailableStep: context.getPreviousAvailableStep,
      goToStep: context.goToStep,
    }),
    [
      context.step,
      context.stepCount,
      context.stepIndex,
      context.steps,
      context.state,
      context.commit,
      context.next,
      context.previous,
      context.getNextAvailableStep,
      context.getPreviousAvailableStep,
      context.goToStep,
    ],
  );

  return filteredContext;
};

export function useWizardStep({ step, ignoreStep = false }: { step: string; ignoreStep?: boolean }) {
  const {
    version,
    goToStep,
    refreshSteps,
    registerStep,
    step: currentStep,
    prospectiveStep,
    advanceProspectiveStep,
  } = useContext(wizardContext);

  const included = !ignoreStep;

  const renderVersion = useRef<number | undefined>(undefined);
  useEffect(() => {
    if (renderVersion.current != version) {
      renderVersion.current = version;
      registerStep(step, included, version);
    }
  }, [version]);

  const refreshVersion = useRef<number>(-2);
  useEffect(() => {
    if (refreshVersion.current != version) {
      refreshVersion.current = version;
      refreshSteps(version);
    }
  }, [included, step]);

  useEffect(() => {
    if (step != prospectiveStep) return;

    if (!included) advanceProspectiveStep();
    else if (currentStep != prospectiveStep) goToStep(prospectiveStep);
  }, [!included, step, currentStep, prospectiveStep]);

  return step == currentStep; // && (!step && included && ));
}

export default function Wizard<T extends WizardStateBase>({
  children,
  state,
  setState,
  onComplete,
}: {
  children: React.ReactNode;
  state?: T;
  setState?: Dispatch<SetStateAction<T>>;
  onComplete?: (data: T) => void;
}) {
  const [prospectiveStep, setProspectiveStep] = useState<string>('');
  const [step, setStep] = useState<string>('');
  const [signal, signalStepsUpdate] = useState(0);
  const [version, setVersion] = useState(-1);
  const backbuffer = useRef<WizardStepState[]>([]);
  const steps = useRef<WizardStepState[]>([]);

  if (backbuffer.current.length) {
    steps.current = backbuffer.current;
    backbuffer.current = [];
  }

  const [stepIndex, stepCount] = useMemo(() => {
    let stepCount = 0;
    let stepIndex = 0;

    for (const s of steps.current) {
      if (s.step == step) stepIndex = stepCount;
      if (s.included) stepCount++;
    }

    return [stepIndex, stepCount];
  }, [step, signal]);

  const getPreviousAvailableStep = useCallback(() => {
    let previousStep = '';

    for (const s of steps.current) {
      if (s.step == step) break;
      if (!s.included) continue;
      previousStep = s.step;
    }

    return previousStep;
  }, [step]);

  const getNextAvailableStep = useCallback(() => {
    if (step == 'complete') return step;
    let foundCurrentStep = step == '';

    for (const s of steps.current) {
      if (s.step == step) {
        foundCurrentStep = true;
        continue;
      }

      if (foundCurrentStep && s.included) {
        return s.step;
      }
    }

    return foundCurrentStep ? 'complete' : '';
  }, [step]);

  function getNextStep(step: string) {
    const currentStepIndex = steps.current.findIndex(s => s.step == step);
    if (currentStepIndex == -1) return steps.current[0]?.step || 'complete';
    return steps.current[currentStepIndex + 1]?.step || 'complete';
  }

  const advanceProspectiveStep = useCallback(() => {
    setProspectiveStep(step => {
      const nextStep = getNextStep(step);
      if (nextStep == 'complete') setStep(step);
      return nextStep;
    });
  }, []);

  const goToStep = useCallback((step: string) => {
    setStep(step);
    setProspectiveStep('');
  }, []);

  const nextStep = useCallback(() => {
    const nextStep = getNextStep(step);
    if (nextStep == 'complete') goToStep(nextStep);
    else setProspectiveStep(nextStep);
  }, [step, goToStep]);

  const previousStep = useCallback(() => {
    const previousStep = getPreviousAvailableStep();
    if (previousStep) goToStep(previousStep);
  }, [getPreviousAvailableStep, goToStep]);

  const refreshSteps = useCallback((stepVersion: number) => {
    setVersion(version => Math.max(stepVersion + 1, version));
  }, []);

  const registerStep = useCallback((step: string, included: boolean, version: number) => {
    backbuffer.current = backbuffer.current.filter(s => s.version == version);
    backbuffer.current.push({ step, included, version });
    signalStepsUpdate(prev => prev + 1);
  }, []);

  const completeRef = useRef<Partial<T> | undefined>(undefined);
  useEffect(() => {
    if (steps.current.length > 0 && step != 'complete' && !steps.current.find(s => s.step == step)) {
      setStep(getNextAvailableStep());
    } else if (step == 'complete') {
      if (completeRef.current != state) onComplete?.(state as T);
      completeRef.current = state;
    }
  }, [signal, step]);

  const onCommit = useCallback((data: Partial<T>) => {
    setState?.(prev => ({ ...prev, ...data }));
  }, []);

  const contextValue = useMemo(
    () => ({
      step,
      stepIndex,
      stepCount,
      steps: Object.fromEntries(steps.current.filter(s => s.included).map((s, index) => [s.step, index])),
      commit: onCommit,
      previous: previousStep,
      next: nextStep,
      getNextAvailableStep,
      getPreviousAvailableStep,
      goToStep,
      state: state || ({} as T),
      registerStep,
      refreshSteps,
      version,
      prospectiveStep,
      advanceProspectiveStep,
    }),
    [
      step,
      stepCount,
      stepIndex,
      onCommit,
      previousStep,
      nextStep,
      getNextAvailableStep,
      getPreviousAvailableStep,
      goToStep,
      state,
      registerStep,
      refreshSteps,
      version,
      prospectiveStep,
      advanceProspectiveStep,
    ],
  );

  const TypedContext = wizardContext as Context<WizardContext<T>>;
  return <TypedContext.Provider value={contextValue}>{children}</TypedContext.Provider>;
}
