// WizardCore.tsx
import React, { createContext, ReactNode, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';

//===============================================
// 1. WizardFormProvider
//===============================================

interface WizardFormContextType<F extends Record<string, any> = Record<string, any>> {
  formData: Partial<F>;
  commit: (newData: Partial<F>) => void;
  reset: (initialData?: Partial<F>) => void;
  submit: () => void;
}

interface WizardFormProviderProps<F extends Record<string, any> = Record<string, any>> {
  children: ReactNode;
  initialData?: Partial<F>;
  onSubmit?: (finalData: F) => void;
}

const WizardFormContext = createContext<WizardFormContextType | undefined>(undefined);

export const WizardFormProvider = <F extends Record<string, any> = Record<string, any>>({
  children,
  initialData = {},
  onSubmit,
}: WizardFormProviderProps<F>) => {
  const [formData, setFormData] = useState<Partial<F>>(initialData);

  const commit = useCallback((newData: Partial<F>) => {
    setFormData(prevData => ({
      ...prevData,
      ...newData,
    }));
  }, []);

  const reset = useCallback(
    (data: Partial<F> = initialData) => {
      setFormData(data);
    },
    [initialData],
  );

  const submit = useCallback(() => {
    setFormData(finalData => {
      onSubmit?.(finalData as F);
      return finalData;
    });
  }, [onSubmit]);

  const value = useMemo(
    () => ({
      formData,
      commit,
      reset,
      submit,
    }),
    [formData, commit, reset, submit],
  );

  const TypedWizardFormContext = WizardFormContext as unknown as React.Context<WizardFormContextType<F>>;
  return <TypedWizardFormContext.Provider value={value}>{children}</TypedWizardFormContext.Provider>;
};

export const useWizardForm = <F extends Record<string, any> = Record<string, any>>(): WizardFormContextType<F> => {
  const context = useContext(WizardFormContext);
  if (context === undefined) {
    throw new Error('useWizardForm must be used within a WizardFormProvider');
  }
  return context as WizardFormContextType<F>;
};

//===============================================
// 2. WizardStepsProvider
//===============================================

export interface StepState {
  name: string;
  skip?: boolean;
  complete?: boolean;
}

interface WizardStepsContextType {
  steps: StepState[];
  totalSteps: number;
  completedSteps: number;
  stepIndex: number;
  step: string;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (stepName: string) => void;
}

interface WizardStepsProviderProps {
  children: ReactNode;
  steps: StepState[];
  onStepChanged?: (step: string) => void;
  onComplete?: () => void;
}

const WizardStepsContext = createContext<WizardStepsContextType | undefined>(undefined);

export const WizardStepsProvider = ({ children, steps, onStepChanged, onComplete }: WizardStepsProviderProps) => {
  if (!steps?.length) {
    console.warn("WizardStepsProvider received empty or undefined 'steps' prop.");
  }

  const [_, setStepDirty] = useState(0);
  const makeStepDirty = useCallback(() => setStepDirty(prev => prev + 1), []);
  const navigationIntent = useRef<'next' | 'prev' | null>(null);
  const stepRef = useRef<string | null>(null);

  function nextAvailableStep() {
    let nextStepName: string | null = null;
    let foundCurrent = false;
    for (const step of steps) {
      if (step.name == stepRef.current) {
        foundCurrent = true;
        continue;
      } else if (foundCurrent && !step.skip) {
        nextStepName = step.name;
        break;
      }
    }

    return foundCurrent ? nextStepName || 'complete' : null;
  }

  function prevAvailableStep() {
    let prevStepName: string | null = null;
    for (const step of steps) {
      if (step.name == stepRef.current) break;
      if (!step.skip) prevStepName = step.name;
    }
    return prevStepName || stepRef.current;
  }

  if (navigationIntent.current == 'next') stepRef.current = nextAvailableStep();
  else if (navigationIntent.current == 'prev') stepRef.current = prevAvailableStep();
  navigationIntent.current = null;

  if (!stepRef.current) stepRef.current = steps.find(step => !step.skip && !step.complete)?.name || 'complete';

  const goToStep = useCallback(
    (step: string) => {
      if (stepRef.current == step) return;

      stepRef.current = step;
      navigationIntent.current = null;
      makeStepDirty();
    },
    [makeStepDirty],
  );

  const nextStep = useCallback(() => {
    if (navigationIntent.current == 'next') return;

    navigationIntent.current = 'next';
    makeStepDirty();
  }, [makeStepDirty]);

  const prevStep = useCallback(() => {
    if (navigationIntent.current == 'prev') return;

    navigationIntent.current = 'prev';
    makeStepDirty();
  }, [makeStepDirty]);

  const formCtx = useContext(WizardFormContext);
  const previousStepRef = useRef<string | null>(null);
  useEffect(() => {
    if (previousStepRef.current != stepRef.current) {
      onStepChanged?.(stepRef.current!);
      if (stepRef.current == 'complete') {
        formCtx?.submit?.();
        onComplete?.();
      }
      previousStepRef.current = stepRef.current;
    }
  });

  const value = useMemo(() => {
    const relevantSteps = steps.filter(step => !step.skip);
    const totalSteps = relevantSteps.length;
    const completedSteps = relevantSteps.reduce((acc, step) => acc + (step.complete ? 1 : 0), 0);
    const stepIndex = relevantSteps.findIndex(step => step.name == stepRef.current);

    return {
      steps: relevantSteps,
      totalSteps,
      completedSteps,
      stepIndex,
      step: stepRef.current!,
      nextStep,
      prevStep,
      goToStep,
    };
  }, [JSON.stringify(steps), stepRef.current, nextStep, prevStep, goToStep]);

  return <WizardStepsContext.Provider value={value}>{children}</WizardStepsContext.Provider>;
};

export const useWizardSteps = (): WizardStepsContextType => {
  const context = useContext(WizardStepsContext);
  if (context === undefined) {
    throw new Error('useWizardSteps must be used within a WizardStepsProvider');
  }
  return context;
};

//===============================================
// 3. WizardStep Component
//===============================================
interface WizardStepProps {
  name: string;
  children: ReactNode;
}

export const WizardStep = ({ name, children }: WizardStepProps) => {
  const { step } = useWizardSteps();

  if (step === name) {
    return <>{children}</>;
  }

  return null;
};
