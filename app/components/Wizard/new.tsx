import {
  createContext,
  Dispatch,
  MutableRefObject,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';

export default function createWizard<
  ProgressiveState = unknown,
  InitialState = unknown,
  StateStep extends { skip?: boolean | undefined; completed?: boolean | undefined } = {
    skip?: boolean | undefined;
    completed?: boolean | undefined;
  },
>() {
  const wizardContext = createContext<{
    state: InitialState & Partial<ProgressiveState>;
    commit: (data: Partial<ProgressiveState>) => void;
    next: () => void;
    previous: () => void;
    goToStep: (step: string) => void;
    _registerStep: (name: string, stepState?: StateStep) => void;
    _step: MutableRefObject<string | undefined>;
    _advance: () => void;
    _prospectiveStep: MutableRefObject<string | undefined>;
    _stepsDirty: () => void;
  } | null>(null);

  const useWizard = () => {
    const ctx = useContext(wizardContext);

    if (!ctx) throw new Error('Wizard hook can only be used under Wizard component.');

    return ctx;
  };

  const useWizardStep = (name: string, stepState?: StateStep) => {
    const ctx = useContext(wizardContext);

    if (!ctx) throw new Error('Wizard step hook can only be used under Wizard component.');

    const { _registerStep, _stepsDirty, _advance, _prospectiveStep, _step } = ctx;

    const stateHashRef = useRef<string>('');
    const currentStateHash = stepState ? JSON.stringify(stepState) : 'undefined';

    if (stateHashRef.current !== currentStateHash) {
      stateHashRef.current = currentStateHash;
      _registerStep(name, stepState);
    }

    if (_prospectiveStep.current === name && stepState?.skip) _advance();

    useEffect(() => {
      _stepsDirty();
    }, [currentStateHash]);

    return _step.current === name;
  };

  const Wizard = ({
    children,
    setState,
    state,
    onComplete,
  }: {
    children: React.ReactNode;
    state: InitialState & Partial<ProgressiveState>;
    setState: Dispatch<SetStateAction<InitialState & Partial<ProgressiveState>>>;
    onComplete: (finalState: InitialState & ProgressiveState) => void;
  }) => {
    const [stepDirtySignal, setStepDirty] = useState(0);
    const [stepsDirtySignal, setStepsDirty] = useState(0);
    const prospectiveStep = useRef<string | undefined>(undefined);
    const step = useRef<string | undefined>(undefined);
    const steps = useRef<({ name: string } & StateStep)[]>([]);

    const stepsDirty = useCallback(() => setStepsDirty(s => s + 1), []);
    const stepDirty = useCallback(() => setStepDirty(s => s + 1), []);
    const _registerStep = useCallback((name: string, stepState?: StateStep) => {
      let stepIndex = steps.current.findIndex(({ name: other }) => other === name);

      if (stepIndex === -1) {
        stepIndex = steps.current.length;
        steps.current.push({ name, ...(stepState || {}) } as { name: string } & StateStep);
      } else {
        steps.current[stepIndex] = { name, ...(stepState || {}) } as { name: string } & StateStep;
      }

      // If the step is not skipped and the step is not set, set the step to the current step
      // This is to ensure that the step is set to the first available step upon first render
      if (!stepState?.skip && !step.current) {
        step.current = name;
      }

      // triggerUpdate();

      return stepIndex;
    }, []);

    const goToStep = useCallback(
      (name: string) => {
        console.log('goToStep', name);
        prospectiveStep.current = undefined;
        step.current = name;
        stepDirty();
        if (name === 'complete') onComplete?.(state as InitialState & ProgressiveState);
      },
      [onComplete],
    );

    const advance = useCallback(() => {
      const nextStepIndex = steps.current.findIndex(({ name: other }) => other === prospectiveStep.current) + 1;

      if (nextStepIndex > 0 && nextStepIndex < steps.current.length) {
        prospectiveStep.current = steps.current[nextStepIndex].name;
      } else {
        prospectiveStep.current = 'complete';
      }
    }, []);

    const next = useCallback(() => {
      prospectiveStep.current = step.current;
      advance();
      stepDirty();
    }, []);

    const previous = useCallback(() => {
      let previousStep = '';

      for (const s of steps.current) {
        if (s.name == step.current) break;
        if (s.skip) continue;
        previousStep = s.name;
      }
      if (previousStep) goToStep(previousStep);
    }, [goToStep]);

    const commit = useCallback(
      (data: Partial<ProgressiveState>) => {
        setState(prev => ({ ...prev, ...data }));
      },
      [setState],
    );

    useEffect(() => {
      if (prospectiveStep.current) goToStep(prospectiveStep.current);
    });

    return (
      <wizardContext.Provider
        value={{
          state,
          commit,
          next,
          previous,
          goToStep,
          _registerStep,
          _step: step,
          _prospectiveStep: prospectiveStep,
          _advance: advance,
          _stepsDirty: stepsDirty,
        }}
      >
        {children}
      </wizardContext.Provider>
    );
  };

  return [Wizard, useWizard, useWizardStep] as const;
}
