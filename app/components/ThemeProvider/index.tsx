import { ThemeContext } from './context';

export default function ThemeProvider({
  children,
  theme,
}: {
  children: ((themeClassName: string) => React.ReactNode) | React.ReactNode;
  theme: SystemType;
}) {
  return (
    <ThemeContext.Provider value={theme}>
      {typeof children === 'function' ? children(theme === 'business' ? 'business-theme' : 'personal-theme') : children}
    </ThemeContext.Provider>
  );
}
