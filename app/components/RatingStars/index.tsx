import type { ReactNode } from 'react';
import { IconName } from '../Icon/props';
import Icon from '../Icon';

interface RatingStarsProps {
  rating: number;
  children?: ReactNode;
}

const starIndexes = [0, 1, 2, 3, 4];
const fractionalStars: IconName[] = ['star-empty', 'star-half', 'star-full'];

export default function RatingStars({ rating, children }: RatingStarsProps) {
  const full = Math.floor(rating);
  const fractionIndex = Math.round((rating - full) * 2);

  const stars = starIndexes.map(index => {
    let icon: IconName;

    if (index < full) icon = 'star-full';
    else if (index == full) icon = fractionalStars[fractionIndex];
    else icon = 'star-empty';

    return <Icon key={index} icon={icon} className="w-[18px]" />;
  });

  return (
    <div className="flex items-center justify-start gap-[16px]">
      <div className="text-gray-dark flex items-center justify-between gap-[2px]">{stars}</div>
      <p className="text-gray-dark">{rating.toFixed(1)}</p>
      {children}
    </div>
  );
}
