import { useIsBusinessSite } from '@/hooks/use-is-business-site';
import { cn } from '@/lib/utils';

export default function AnimatedBlob({
  delay,
  left,
  top,
  zIndex,
  opacity,
  width,
  height,
  blur,
}: {
  delay?: string;
  zIndex?: number;
  left?: string;
  top?: string;
  opacity?: number;
  width?: string;
  height?: string;
  blur?: string;
}) {
  const isBusiness = useIsBusinessSite();

  return (
    <div
      className="pointer-events-none absolute inset-0 top-0 left-0 h-full w-full overflow-clip blur-[100px]"
      role="presentation"
      style={{ zIndex, filter: blur ? `blur(${blur})` : undefined }}
    >
      <div className="absolute top-1/3 left-1/4 animate-blob-rotate" style={{ animationDelay: delay, left, top }}>
        <div className="animate-blob-move" style={{ animationDelay: delay }}>
          <div
            className={cn(
              'h-[100px] w-[400px] animate-blob rounded-full opacity-50 sm:h-[350px] sm:w-[1000px]',
              isBusiness ? 'bg-[#008d86]' : 'bg-[#1df9df]',
            )}
            style={{ animationDelay: delay, opacity, width, height }}
          ></div>
        </div>
      </div>
    </div>
  );
}
