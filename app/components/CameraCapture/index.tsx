import { VIN_IDENTIFIER_IMAGE_SIZE } from '@/business/core/constants/vin';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { ArrowLeftRight, Circle } from 'lucide-react';
import { ReactEventHandler, useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '../ui/button';

export interface CameraCaptureProps {
  className?: string;
  videoReadyCallback?: ReactEventHandler<HTMLVideoElement>;
  contain?: boolean;
  preferredFacingMode?: 'user' | 'environment';
  preferredZoom?: number;
  onCapture?: (blob: Blob | null, width: number, height: number) => void;
  children?: React.ReactNode;
}

export default function CameraCapture({
  className,
  videoReadyCallback,
  onCapture,
  contain,
  preferredFacingMode = 'environment',
  preferredZoom = 1,
  children,
}: CameraCaptureProps) {
  const { toast } = useToast();
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);

  const streamRef = useRef<MediaStream | null>(null);
  const playerRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [facingMode, setFacingMode] = useState(preferredFacingMode);

  const stopStream = useCallback(() => {
    if (playerRef.current) playerRef.current.srcObject = null;
    if (!streamRef.current) return;
    streamRef.current?.getTracks()?.forEach(track => track?.stop());
    streamRef.current = null;
  }, []);

  useEffect(() => {
    const initStream = async () => {
      if (!navigator.mediaDevices?.getUserMedia) {
        toast({
          description: 'No camera device accessible. Please connect your camera or try a different browser.',
          variant: 'destructive',
        });
        return;
      }

      await navigator.mediaDevices
        .getUserMedia({
          audio: false,
          video: {
            facingMode: { ideal: facingMode },
            zoom: { ideal: preferredZoom },
          } as any,
        })
        .then(stream => {
          stopStream();
          streamRef.current = stream;
          if (playerRef.current) playerRef.current.srcObject = stream;
          if (!devices?.length) {
            navigator.mediaDevices
              ?.enumerateDevices()
              ?.then(devices => devices.filter(device => device.kind == 'videoinput'))
              .then(setDevices);
          }
        })
        .catch(error => {
          if (error.name === 'PermissionDeniedError')
            toast({
              description: 'Permission denied. Please refresh and give camera permission.',
              variant: 'destructive',
            });
          else
            toast({
              description: error.message,
              variant: 'destructive',
            });

          stopStream();
          return undefined;
        });
    };

    initStream();
  }, [facingMode]);

  useEffect(() => stopStream, []);

  const takePhoto = useCallback((): string | undefined => {
    if (
      !playerRef.current ||
      !canvasRef.current ||
      !playerRef.current?.videoWidth ||
      !playerRef.current?.videoHeight ||
      !canvasRef.current?.getContext('2d') ||
      !containerRef.current?.offsetWidth ||
      !containerRef.current?.offsetHeight
    )
      return;

    const playerWidth = playerRef.current.videoWidth ?? VIN_IDENTIFIER_IMAGE_SIZE;
    const playerHeight = playerRef.current.videoHeight ?? VIN_IDENTIFIER_IMAGE_SIZE;
    const playerAR = playerWidth / playerHeight;

    const canvasWidth = containerRef?.current?.offsetWidth ?? VIN_IDENTIFIER_IMAGE_SIZE;
    const canvasHeight = containerRef?.current?.offsetHeight ?? VIN_IDENTIFIER_IMAGE_SIZE;
    const canvasAR = canvasWidth / canvasHeight;

    let sX, sY, sW, sH;

    if (playerAR > canvasAR) {
      sH = playerHeight;
      sW = playerHeight * canvasAR;
      sX = (playerWidth - sW) / 2;
      sY = 0;
    } else {
      sW = playerWidth;
      sH = playerWidth / canvasAR;
      sX = 0;
      sY = (playerHeight - sH) / 2;
    }

    canvasRef.current.width = sW;
    canvasRef.current.height = sH;

    const context = canvasRef.current.getContext('2d');
    if (context && playerRef?.current) {
      context.drawImage(playerRef.current, sX, sY, sW, sH, 0, 0, sW, sH);
    }

    canvasRef.current.toBlob(blob => onCapture?.(blob, sW, sH), 'image/jpeg', 0.8);
  }, [onCapture]);

  const switchCamera = useCallback(() => {
    setFacingMode(value => (value == 'environment' ? 'user' : 'environment'));
  }, []);

  return (
    <div ref={containerRef} className={cn('relative bg-muted', className)}>
      <video
        className={cn(
          'z-0 mx-auto h-full',
          contain ? 'object-contain' : 'w-[-webkit-fill-available] w-full object-cover',
        )}
        ref={playerRef}
        id="video"
        muted={true}
        autoPlay={true}
        playsInline={true}
        onLoadedData={videoReadyCallback}
      />
      <canvas className="hidden" ref={canvasRef} />
      {devices?.length >= 2 && (
        <Button size="icon" variant="outline" className="absolute top-4 left-4 opacity-60" onClick={switchCamera}>
          <ArrowLeftRight />
        </Button>
      )}
      {Boolean(onCapture) && (
        <button
          className="absolute bottom-8 left-1/2 size-10 origin-bottom -translate-x-1/2 rounded-full border-none bg-emphasis text-emphasis opacity-90 outline-hidden transition-colors hover:bg-primary-foreground hover:text-primary-foreground"
          onClick={takePhoto}
        >
          <Circle className="absolute -top-3 -left-3 size-16" />
        </button>
      )}
      {children}
    </div>
  );
}
