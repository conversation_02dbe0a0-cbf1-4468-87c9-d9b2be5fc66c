import { useState } from 'react';
import { ChevronLeft, X } from 'lucide-react';
import { Button } from '../ui/button';
import { Blocks, BuilderBlock } from '@builder.io/sdk-react/edge';
import { cn } from '@/lib/utils';
import { IMenuItem } from '.';
import { SidebarContent, SidebarGroup, SidebarGroupContent, SidebarMenu, useSidebar } from '../ui/sidebar';
import HeaderMenuMobileLink from './HeaderMenuMobileLink';
import { Sheet, SheetContent } from '../ui/sheet';

interface IHeaderMenuMobileProps {
  menuItems: IMenuItem[];
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isGlobal?: boolean;
  builderBlock?: BuilderBlock;
  mobileFooterBlocks?: BuilderBlock[];
}

export default function HeaderMenuMobile({
  menuItems,
  isGlobal,
  builderBlock,
  mobileFooterBlocks,
  onOpenChange,
  open,
}: IHeaderMenuMobileProps) {
  const [selectedSubmenu, setSelectedSubmenu] = useState<string | null>(null);

  function handleClickClose() {
    setSelectedSubmenu(null);
    onOpenChange?.(false);
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        data-sidebar="sidebar"
        data-mobile="true"
        className="w-full bg-background p-0 text-sidebar-foreground sm:max-w-full [&>button]:hidden"
        side="left"
      >
        <div className="flex h-full w-full flex-col">
          <div className={cn('flex h-8 items-center px-6 py-6', selectedSubmenu ? 'justify-between' : 'justify-end')}>
            {!!selectedSubmenu && (
              <>
                <div className="absolute inset-x-0 top-3 flex-1 text-center text-body-xs font-bold">
                  {selectedSubmenu}
                </div>
                <Button
                  variant={'link'}
                  size="fit"
                  className="text-body-normal"
                  onClick={() => setSelectedSubmenu(null)}
                >
                  <ChevronLeft /> Back
                </Button>
              </>
            )}
            <Button variant={'ghost'} size={'icon'} className="font-normal text-body-normal" onClick={handleClickClose}>
              <X />
            </Button>
          </div>

          <SidebarContent
            className="w-[200vw] flex-row overflow-x-hidden overflow-y-auto"
            onClick={event => {
              if ((event.target as HTMLElement).closest('a')) onOpenChange?.(false);
            }}
          >
            <SidebarGroup
              className={cn('w-screen transition-all duration-200', !!selectedSubmenu && '-translate-x-full')}
            >
              <SidebarGroupContent className="flex max-h-[1000px] flex-1 flex-col justify-between px-5">
                <SidebarMenu className="gap-8">
                  {menuItems.map(item => (
                    <HeaderMenuMobileLink
                      key={item.label}
                      item={item}
                      selectedSubmenu={selectedSubmenu}
                      setSelectedSubmenu={setSelectedSubmenu}
                    />
                  ))}
                </SidebarMenu>
                <Blocks parent={builderBlock?.id} path="mobileFooterBlocks" blocks={mobileFooterBlocks} />
              </SidebarGroupContent>
            </SidebarGroup>
            {!isGlobal && (
              <SidebarGroup
                className={cn(
                  'w-screen overflow-y-auto p-6 transition-all duration-200',
                  !!selectedSubmenu && '-translate-x-full',
                )}
              >
                <SidebarGroupContent>
                  {menuItems.map(item => (
                    <div
                      key={item.label}
                      className={cn('h-full', selectedSubmenu === item.label ? 'visible' : 'hidden')}
                    >
                      <Blocks parent={builderBlock?.id} blocks={item.blocks} />
                    </div>
                  ))}
                </SidebarGroupContent>
              </SidebarGroup>
            )}
          </SidebarContent>
        </div>
      </SheetContent>
    </Sheet>
  );
}
