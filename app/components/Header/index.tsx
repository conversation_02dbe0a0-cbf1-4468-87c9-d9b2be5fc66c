import { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router';
import { ChevronDown, Menu } from 'lucide-react';
import { Blocks, BuilderBlock, isEditing } from '@builder.io/sdk-react/edge';
import { cn } from '@/lib/utils';
import { useIsBusinessSite } from '@/hooks/use-is-business-site';
import HeaderMenuMobile from './HeaderMenuMobile';
import HeaderDesktopBar from './HeaderDesktopBar';
import Icon from '../Icon';
import { useIsMounted } from '@/hooks/use-mounted';
import { Button } from '../ui/button';
import { HOME_ROOT_URL } from './config';
import { BUSINESS_ROOT_URL } from './config';

export type IMenuItem = { label: string; url: string; hasSubMenu: boolean; blocks: BuilderBlock[] };

interface IProps {
  businessLogo: string;
  personalLogo: string;
  menuItems: IMenuItem[];
  builderBlock: BuilderBlock;
  mobileFooterBlocks: BuilderBlock[];
  desktopHeaderBlocks: BuilderBlock[];
  showEditableDropdowns: boolean;
}

const DesktopMenuItem = ({
  item,
  openMenu,
  onOpenMenuChange,
  isMounted,
}: {
  item: IMenuItem;
  openMenu: string | null;
  onOpenMenuChange: (label: string | null) => void;
  isMounted: boolean;
}) => {
  const isOpen = openMenu === item.label;
  const Comp = item.hasSubMenu ? 'button' : Link;

  const handleMouseEnter = () => {
    onOpenMenuChange(item.label);
  };

  const handleClickMenuItem = () => {
    if (item.hasSubMenu) {
      onOpenMenuChange(openMenu === item.label ? null : item.label);
    }
  };

  return (
    <li key={item.label} className="relative flex items-center px-4" onMouseEnter={handleMouseEnter}>
      <Comp
        to={item.url}
        prefetch="intent"
        onClick={handleClickMenuItem}
        className={cn(
          'relative inline-flex h-24 w-max items-center justify-center py-2 text-body-sm font-medium',
          'disabled:pointer-events-none disabled:opacity-50',
        )}
        aria-expanded={isOpen}
      >
        {item.label} {item.hasSubMenu && <ChevronDown className="h-4 text-body-normal" />}
        {isMounted && (
          <div
            className={cn(
              'absolute right-0 bottom-6 left-0 h-[2px] origin-left rounded-full bg-emphasis transition-all duration-180 ease-in',
              isOpen ? 'scale-x-100' : 'scale-x-0',
            )}
          />
        )}
      </Comp>
    </li>
  );
};

const SubMenuItems = ({
  menuItems,
  openMenu,
  isEdit,
  builderBlock,
}: {
  menuItems: IMenuItem[];
  openMenu: string | null;
  isEdit: boolean;
  builderBlock: BuilderBlock;
}) => {
  return menuItems.map((item, idx) => {
    const isOpen = (openMenu === item.label || isEdit) && item.hasSubMenu;

    return (
      <div
        key={item.label}
        className={cn(
          'top-24 left-0 flex min-h-96 w-screen overflow-hidden bg-background',
          'transition-all duration-300 ease-in-out',
          isOpen ? 'visible opacity-100' : 'invisible opacity-0',
          isEdit ? 'static' : 'fixed',
          isEdit ? 'z-50' : '-z-50',
        )}
      >
        {isEdit && <h1 className="text-title-2 font-medium">Editing: {item.label}</h1>}
        <div
          className={cn(
            'flex min-h-96 w-screen items-center justify-center',
            'transition-all duration-300 ease-in-out',
            isOpen ? 'scale-100' : 'scale-0.95',
          )}
        >
          <Blocks
            parent={builderBlock?.id}
            path={`menuItems.${idx}.blocks`}
            blocks={item.blocks}
            className="flex flex-1"
          />
        </div>
      </div>
    );
  });
};

export default function Header({
  businessLogo,
  personalLogo,
  menuItems,
  builderBlock,
  mobileFooterBlocks,
  desktopHeaderBlocks,
  showEditableDropdowns,
}: IProps) {
  const isBusiness = useIsBusinessSite();
  const urlRoot = isBusiness ? BUSINESS_ROOT_URL : HOME_ROOT_URL;
  const logo = isBusiness ? businessLogo : personalLogo;
  const [mobileOpen, setMobileOpen] = useState(false);
  const [openMenu, setOpenMenu] = useState<string | null>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const openMenuItem = menuItems.find(item => item.label === openMenu);
  const isMounted = useIsMounted();
  const isEdit = isEditing() && showEditableDropdowns;

  useEffect(() => {
    let lastScrollTop = 0;

    const onScroll = () => {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;

      if (!headerRef.current) return;

      if (scrollTop > 500 && scrollTop > lastScrollTop) {
        headerRef.current.classList.add('aa-hide-header');
      }

      if (scrollTop > 500 && scrollTop <= lastScrollTop) {
        headerRef.current.classList.remove('aa-hide-header');
      }

      if (scrollTop <= 500) {
        headerRef.current.classList.remove('aa-hide-header');
      }

      lastScrollTop = scrollTop;
    };

    window.addEventListener('scroll', onScroll);
    return () => window.removeEventListener('scroll', onScroll);
  }, []);

  const handleMouseLeave = () => {
    setOpenMenu(null);
  };

  if (isEdit) {
    return <SubMenuItems menuItems={menuItems} openMenu={openMenu} isEdit={isEdit} builderBlock={builderBlock} />;
  }

  return (
    <>
      <header
        ref={headerRef}
        className="aa-header fixed top-0 left-0 z-50 flex h-14 w-full flex-col items-center justify-center bg-background shadow-md transition-transform duration-300 ease-in-out md:h-36"
      >
        <HeaderDesktopBar builderBlock={builderBlock} desktopHeaderBlocks={desktopHeaderBlocks} />
        <ul className="relative grid h-24 w-full grid-cols-6 px-6 md:px-[5%]">
          <li className="relative col-span-2 flex w-full items-center md:col-span-1 md:hidden">
            <Button variant="ghost" size="icon" onClick={() => setMobileOpen(true)}>
              <Menu />
              <span className="sr-only">Open Mobile Menu</span>
            </Button>
          </li>
          <li className="relative col-span-2 flex w-full items-center md:col-span-1">
            <Link to={urlRoot} prefetch="intent">
              <img src={logo} alt="logo" className="h-8 md:h-12 md:w-fit" />
            </Link>
          </li>

          <div onMouseLeave={handleMouseLeave} className="col-span-4 hidden justify-center md:flex">
            <div className="flex">
              {menuItems.map(item => (
                <DesktopMenuItem
                  key={item.label}
                  item={item}
                  openMenu={openMenu}
                  onOpenMenuChange={setOpenMenu}
                  isMounted={isMounted}
                />
              ))}
            </div>
            {isMounted && (
              <SubMenuItems menuItems={menuItems} openMenu={openMenu} isEdit={isEdit} builderBlock={builderBlock} />
            )}
          </div>

          <li className="relative col-span-2 flex items-center justify-end md:col-span-1">
            <Link to={isBusiness ? '/business/cart' : '/home/<USER>'} prefetch="intent" className="p-3">
              <Icon icon="shopping-cart" className="h-6 w-6" />
            </Link>
          </li>
        </ul>
      </header>

      {openMenu && openMenuItem?.hasSubMenu && (
        <div className="bg-opacity-65 fixed inset-0 z-40 bg-black backdrop-blur-[50px]"></div>
      )}

      {isMounted && (
        <HeaderMenuMobile
          open={mobileOpen}
          onOpenChange={setMobileOpen}
          menuItems={menuItems}
          builderBlock={builderBlock}
          mobileFooterBlocks={mobileFooterBlocks}
        />
      )}
      <div className="h-14 md:h-36" />
    </>
  );
}
