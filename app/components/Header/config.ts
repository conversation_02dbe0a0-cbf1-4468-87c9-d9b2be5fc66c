import { RegisteredComponent } from '@builder.io/sdk-react/edge';
import Header from '.';

export const BUSINESS_ROOT_URL = '/business/fleet-aware';
export const HOME_ROOT_URL = '/';

export const HEADER_COMPONENT: RegisteredComponent = {
  name: 'Header',
  component: Header,
  description:
    "The header menu should be used only in a symbol that is then used across the site. The reason for editing it in a symbol on it's own is to prevent any weird behavior when editing the submenus. To edit the submenus, you must toggle 'show editable dropdowns'. This will give you a view of all the submenu contents in a way that you can edit them. The tallest submenu content will determine the height of the dropdown for all submenus.",
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  inputs: [
    {
      name: 'businessLogo',
      type: 'url',
      defaultValue: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/h2-business-logo.svg?v=1742407003',
      helperText: 'The logo to display in the header. Get the URL from Shopify.',
    },
    {
      name: 'personal<PERSON>ogo',
      type: 'url',
      defaultValue: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/h2-personal-logo.svg?v=1742407003',
      helperText: 'The logo to display in the header. Get the URL from Shopify.',
    },
    {
      name: 'showEditableDropdowns',
      type: 'boolean',
      helperText: 'If true, the dropdown content will be editable in the editor. This has no effect on the live site.',
      defaultValue: false,
    },
    {
      name: 'menuItems',
      type: 'list',
      defaultValue: [
        {
          label: 'Example',
          url: '/example',
          blocks: [],
        },
      ],
      subFields: [
        {
          name: 'label',
          type: 'string',
          defaultValue: '',
        },
        {
          name: 'url',
          type: 'url',
          defaultValue: '',
        },
        {
          name: 'hasSubMenu',
          type: 'boolean',
          defaultValue: false,
        },
        {
          name: 'blocks',
          type: 'uiBlocks',
          hideFromUI: true,
          defaultValue: [],
        },
      ],
    },
    {
      name: 'mobileFooterBlocks',
      type: 'uiBlocks',
      hideFromUI: true,
      defaultValue: [],
    },
    {
      name: 'desktopHeaderBlocks',
      type: 'uiBlocks',
      hideFromUI: true,
      defaultValue: [],
    },
  ],
};
