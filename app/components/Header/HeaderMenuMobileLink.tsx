import { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router';
import { ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SidebarMenuItem } from '../ui/sidebar';
import { IMenuItem } from '.';

interface IHeaderMenuMobileLinkProps {
  item: IMenuItem;
  selectedSubmenu: string | null;
  setSelectedSubmenu: (submenu: string | null) => void;
}

export default function HeaderMenuMobileLink({
  item,
  selectedSubmenu,
  setSelectedSubmenu,
}: IHeaderMenuMobileLinkProps) {
  const [isPressed, setIsPressed] = useState(false);
  const Comp = item.hasSubMenu ? 'button' : Link;
  const buttonRef = useRef<HTMLButtonElement & HTMLAnchorElement>(null);

  function handleClick() {
    setSelectedSubmenu(item.label);
  }

  const handleTouchMove = (e: TouchEvent) => {
    if (!buttonRef.current) return;

    const touch = e.touches[0];
    const element = buttonRef.current;
    const rect = element.getBoundingClientRect();

    if (
      touch.clientX < rect.left ||
      touch.clientX > rect.right ||
      touch.clientY < rect.top ||
      touch.clientY > rect.bottom
    ) {
      setIsPressed(false);
    }
  };

  useEffect(() => {
    const element = buttonRef.current;
    if (!element) return;

    element.addEventListener('touchmove', handleTouchMove);
    return () => {
      element.removeEventListener('touchmove', handleTouchMove);
    };
  }, []);

  useEffect(() => {
    if (isPressed) {
      setIsPressed(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedSubmenu]);

  return (
    <SidebarMenuItem>
      <Comp
        ref={buttonRef}
        to={item.url}
        prefetch="intent"
        onClick={item.hasSubMenu ? handleClick : undefined}
        onTouchStart={() => setIsPressed(true)}
        className="flex w-full items-center"
      >
        <span className="text-body-sm font-bold">{item.label}</span>
        {item.hasSubMenu && <ChevronRight className="ml-auto" size={24} />}
      </Comp>
      <div
        className={cn(
          'mt-2 h-[2px] origin-left rounded-full bg-emphasis transition-all duration-180 ease-in',
          isPressed ? 'scale-x-100' : 'scale-x-0',
        )}
      />
    </SidebarMenuItem>
  );
}
