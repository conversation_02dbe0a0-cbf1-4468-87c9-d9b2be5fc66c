import { cn } from '@/lib/utils';
import { useIsBusinessSite } from '@/hooks/use-is-business-site';
import { Link } from 'react-router';
import { Blocks, BuilderBlock } from '@builder.io/sdk-react/edge';
import { useState } from 'react';
import { BUSINESS_ROOT_URL, HOME_ROOT_URL } from './config';

interface IProps {
  isGlobal?: boolean;
  builderBlock?: BuilderBlock;
  desktopHeaderBlocks?: BuilderBlock[];
}

export default function HeaderDesktopBar({ isGlobal, builderBlock, desktopHeaderBlocks }: IProps) {
  const isBusiness = useIsBusinessSite();
  const [isHovering, setIsHovering] = useState<'business' | 'personal' | null>(null);

  return (
    <div className="hidden h-12 w-full items-center justify-between gap-7 border-b border-border px-[5%] md:flex">
      <div className="flex gap-7">
        <div
          className="group relative flex flex-col items-center"
          onMouseEnter={() => setIsHovering('business')}
          onMouseLeave={() => setIsHovering(null)}
        >
          <Link to={BUSINESS_ROOT_URL} prefetch="intent" className="flex w-full items-center">
            <span className="text-body-xxs">Business</span>
          </Link>
          <div
            className={cn(
              'absolute -bottom-4 mt-2 h-[2px] w-full origin-left rounded-full bg-emphasis transition-all duration-180 ease-in',
              isHovering === 'business'
                ? 'scale-x-100'
                : isHovering === 'personal'
                  ? 'scale-x-0'
                  : isBusiness && !isGlobal
                    ? 'scale-x-100'
                    : 'scale-x-0 group-hover:scale-x-100',
            )}
          />
        </div>
        <div
          className="group relative flex flex-col items-center"
          onMouseEnter={() => setIsHovering('personal')}
          onMouseLeave={() => setIsHovering(null)}
        >
          <Link to={HOME_ROOT_URL} prefetch="intent" className="flex w-full items-center">
            <span className="text-body-xxs">Personal</span>
          </Link>
          <div
            className={cn(
              'absolute -bottom-4 mt-2 h-[2px] w-full origin-left rounded-full bg-emphasis transition-all duration-180 ease-in',
              isHovering === 'personal'
                ? 'scale-x-100'
                : isHovering === 'business'
                  ? 'scale-x-0'
                  : !isBusiness && !isGlobal
                    ? 'scale-x-100'
                    : 'scale-x-0 group-hover:scale-x-100',
            )}
          />
        </div>
      </div>
      {!isGlobal && (
        <div className="z-50 flex flex-1 items-center justify-end">
          <Blocks
            parent={builderBlock?.id}
            path="desktopHeaderBlocks"
            blocks={desktopHeaderBlocks}
            className="flex-1"
          />
        </div>
      )}
    </div>
  );
}
