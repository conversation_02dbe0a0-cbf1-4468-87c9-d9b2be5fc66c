import { Button } from '../ui/button';
import { useNavigate, Link } from 'react-router';

export function CartEmpty() {
  const navigate = useNavigate();

  return (
    <div className="flex h-[calc(100vh-64px)] w-full flex-col items-center justify-center md:h-[calc(100vh-144px)]">
      <h1 className="pb-4 text-title-4 font-bold text-heading-two md:text-title-3">Your cart is empty</h1>
      <p className="text-md pb-8 text-center text-body-normal">Sign in to view your account or continue shopping.</p>
      <div className="flex w-[90%] flex-col gap-4 md:w-[50%] md:flex-row">
        <Link to="/account/login" className="w-full">
          <Button variant="outline" size="full">
            Sign in
          </Button>
        </Link>
        <Button variant="primary" size="full" onClick={() => navigate(-1)}>
          Continue Shopping
        </Button>
      </div>
    </div>
  );
}
