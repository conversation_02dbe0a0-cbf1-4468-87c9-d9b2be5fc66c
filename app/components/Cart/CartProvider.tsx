import { AugmentedCart } from '@/business/core/types/cart';
import { CartSystemView } from '@/business/handlers/types/cart';
import { CartContext } from '@/hooks/use-cart';
import { useIsBusinessSite } from '@/hooks/use-is-business-site';
import { usePromiseEffect } from '@/hooks/use-promise-effect';
import { useRootData } from '@/root';
import { Analytics, OptimisticCart, useOptimisticCart } from '@shopify/hydrogen';
import { useEffect, useRef, useState } from 'react';
import { UIMatch, useFetchers, useMatches } from 'react-router';
import { CartApiQueryFragment } from 'storefront.generated';

export function CartProvider({
  children,
  overwriteCart,
}: {
  children: React.ReactNode;
  overwriteCart?: CartApiQueryFragment;
}) {
  const { shop, consent } = useRootData();
  const fetchers = useFetchers();
  const systemType: SystemType = useIsBusinessSite() ? 'business' : 'personal';
  const matches = useMatches();
  const loaderData = matches.find(match => match.id?.endsWith('.cart')) as UIMatch<{
    accessToken: string | undefined | null;
    cart: AugmentedCart;
    systems: Promise<CartSystemView[]>;
    fallbackLocationId: Promise<string | null>;
    orderingCustomerId: Promise<string | null>;
  }>;

  overwriteCart ||= loaderData?.data?.cart || null;

  const [cartPromise, setCartPromise] = useState<Promise<AugmentedCart | null> | null>(null);
  const systemsPromise = loaderData?.data?.systems || null;
  const orderingCustomerIdPromise = loaderData?.data?.orderingCustomerId || null;
  const fallbackLocationIdPromise = loaderData?.data?.fallbackLocationId || null;

  const sendingRef = useRef(false);
  useEffect(() => {
    if (
      overwriteCart ||
      sendingRef.current ||
      (cartPromise && !fetchers.some(fetcher => fetcher.state === 'loading' && fetcher.formAction?.includes('/cart')))
    ) {
      return;
    }
    sendingRef.current = true;

    const payloadPromise = fetch(`/cart?type=${systemType}`)
      .then(res => res.json<AugmentedCart>())
      .catch(error => {
        console.warn('Error while fetching cart', error);
        return null;
      })
      .finally(() => {
        sendingRef.current = false;
      });

    setCartPromise(payloadPromise);
  }, [fetchers]);

  const { value: cart, isLoading: isLoadingCart } = usePromiseEffect(cartPromise as Promise<CartApiQueryFragment>);
  const { value: systems } = usePromiseEffect(systemsPromise);
  const { value: orderingCustomerId } = usePromiseEffect(orderingCustomerIdPromise);
  const { value: fallbackLocationId } = usePromiseEffect(fallbackLocationIdPromise);

  const optimisticCart = overwriteCart ? useOptimisticCart(overwriteCart) : cart;

  return (
    <>
      <CartContext.Provider
        value={{
          systemType,
          accessToken: loaderData?.data?.accessToken,
          cart: optimisticCart as OptimisticCart<AugmentedCart>,
          isLoadingCart: overwriteCart ? false : isLoadingCart,
          systems,
          fallbackLocationId,
          orderingCustomerId,
        }}
      >
        {overwriteCart ? (
          children
        ) : (
          <Analytics.Provider
            cart={(optimisticCart as any) ? (cartPromise as any) : null}
            shop={shop}
            consent={consent}
          >
            {children}
          </Analytics.Provider>
        )}
      </CartContext.Provider>
    </>
  );
}
