import { Button } from '@/components/ui/button';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { useCallback, useState } from 'react';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';
import { getCookie, setCookie } from '@/lib/utils';
import { CART_LOGIN_MODAL_ANSWERED_COOKIE_NAME } from '@/business/core/constants/cart';
import TextField from '../TextField';
import { OTPField } from '../OTPField';
import { useOTP } from '@/hooks/use-otp';
import { createWizardFormContext, createWizardStepsContext } from '../Wizard/ctx-factory';
import { Loader2 } from 'lucide-react';
import { useRootData } from '@/root';
import ThemeProvider from '../ThemeProvider';
import { useCart } from '@/hooks/use-cart';

const { WizardFormProvider: LoginFormProvider, useWizardForm: useLoginForm } = createWizardFormContext<{
  newCustomer: boolean;
  email?: string;
}>();

const {
  WizardStep: LoginStep,
  WizardStepsProvider: LoginStepsProvider,
  useWizardSteps: useLoginSteps,
} = createWizardStepsContext();

function NewOrReturningStep() {
  const {
    commit,
    formData: { newCustomer },
  } = useLoginForm();
  const { nextStep } = useLoginSteps();

  return (
    <>
      <DialogHeader className="flex flex-col gap-4">
        <DialogTitle>Are you a new or returning customer?</DialogTitle>
        <DialogDescription>
          Let us know if you are a new or returning customer so we can apply any relevant discounts to your
          subscription.
        </DialogDescription>
      </DialogHeader>
      <div className="flex flex-col gap-4 pt-4">
        <RadioGroup
          defaultValue={newCustomer ? 'new' : 'returning'}
          className="flex flex-col gap-4"
          onValueChange={value => commit({ newCustomer: value === 'new' })}
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="new" />
            <Label htmlFor="new">New Customer</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="returning" />
            <Label htmlFor="returning">Returning Customer</Label>
          </div>
        </RadioGroup>
        <DialogFooter className="pt-4">
          <Button type="button" onClick={nextStep} className="min-w-32 self-start">
            Continue
          </Button>
        </DialogFooter>
      </div>
    </>
  );
}

export function EmailStep() {
  const { commit } = useLoginForm();
  const { nextStep, prevStep } = useLoginSteps();

  const { form } = useOTP({
    email: '',
    onSuccess: () => {
      commit({ email: form.getValues('email') });
      nextStep();
    },
  });

  return (
    <>
      <DialogHeader className="flex flex-col gap-4">
        <DialogTitle>Enter your email below</DialogTitle>
        <DialogDescription>
          We will send you a 6-digit code to your email so you can login. Please use your email address associated with
          your All Aware login
        </DialogDescription>
      </DialogHeader>
      <Form form={form} className="flex flex-col gap-4 pt-4">
        <TextField name="email" label="Email" placeholder="Account email" />
        <DialogFooter className="justify-start gap-4 pt-4">
          <Button type="submit" loading={form.formState.isSubmitting} className="min-w-32 self-start">
            Send Login Code
          </Button>
          <Button type="button" variant="outline" className="min-w-32 self-start" onClick={prevStep}>
            Back
          </Button>
        </DialogFooter>
      </Form>
    </>
  );
}

export function CodeStep() {
  const {
    formData: { email },
  } = useLoginForm();
  const { nextStep, prevStep } = useLoginSteps();
  const { form } = useOTP({
    onSuccess: nextStep,
  });

  const submitting = form.formState.isSubmitting || form.formState.isLoading;
  return (
    <>
      <DialogHeader className="flex flex-col gap-4">
        <DialogTitle>Enter the 6-digit code to continue</DialogTitle>
        <DialogDescription>
          Please enter the 6 digit code sent to the email <b>{email}</b>. This code may take a moment to arrive and will
          expire in 15 minutes.
        </DialogDescription>
      </DialogHeader>
      <Form form={form} className="flex flex-col justify-start gap-4 pt-4">
        <OTPField name="OTP" label="Enter Code" resendEmail={email} disabled={submitting} />
        <DialogFooter className="pt-4">
          <Button type="submit" loading={submitting} className="min-w-32 self-start">
            Login with Code
          </Button>
          <Button type="button" variant="outline" className="min-w-32 self-start" onClick={prevStep}>
            Back
          </Button>
        </DialogFooter>
      </Form>
    </>
  );
}

export function CartLoginModal() {
  const { accessToken } = useCart();
  const hasAnswered = Boolean(accessToken) || getCookie(CART_LOGIN_MODAL_ANSWERED_COOKIE_NAME);
  const [isOpen, setIsOpen] = useState(!hasAnswered);

  const answered = useCallback(({ newCustomer }: { newCustomer?: boolean }) => {
    setCookie(CART_LOGIN_MODAL_ANSWERED_COOKIE_NAME, 'true', 30);
    if (newCustomer) {
      setIsOpen(false);
    } else {
      window.location.href = `/account/login?${new URLSearchParams({
        return_to: location.pathname + location.search + location.hash,
      })}`;
    }
  }, []);

  return (
    <Dialog open={isOpen}>
      <ThemeProvider theme="personal">
        {themeClassName => (
          <DialogContent hideClose className={themeClassName} onPointerDownOutside={e => e.preventDefault()}>
            <LoginFormProvider initialData={{ newCustomer: true }} onSubmit={answered}>
              <CartLoginSteps />
            </LoginFormProvider>
          </DialogContent>
        )}
      </ThemeProvider>
    </Dialog>
  );
}

function CartLoginSteps() {
  const {
    submit,
    formData: { newCustomer },
  } = useLoginForm();

  return (
    <LoginStepsProvider
      steps={[
        // Skip to the end if the user is a new customer
        'initial',
        { name: 'email', skip: newCustomer },
        { name: 'code', skip: newCustomer },
        'complete',
      ]}
      onComplete={submit}
    >
      <LoginStep name="initial">
        <NewOrReturningStep />
      </LoginStep>
      <LoginStep name="email">
        <EmailStep />
      </LoginStep>
      <LoginStep name="code">
        <CodeStep />
      </LoginStep>
      <LoginStep name="complete">
        <div className="relative flex h-40 w-full flex-col items-center justify-center gap-1">
          {!newCustomer && <p className="font-semibold text-muted-foreground">Logging in...</p>}
          <Loader2 className="size-16 animate-spin text-primary" />
        </div>
      </LoginStep>
    </LoginStepsProvider>
  );
}
