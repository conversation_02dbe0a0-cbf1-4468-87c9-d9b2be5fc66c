import { useCart } from '@/hooks/use-cart';
import { CartSummary } from './CartSummary';
import CartSystemSelector from './CartSystemSelector';
import { CartSubscriptionCard } from './CartSubscriptionCard';
import { <PERSON>tHeader } from './CartHeader';
import { CartLines } from './CartLines';
import { CartEmpty } from './CartEmpty';
import Icon from '../Icon';
import { CartLoginModal } from './CartLoginModal';
/**
 * The main cart component that displays the cart items and summary.
 * It is used by both the /cart route and the cart aside dialog.
 */
export default function CartMain({ isSidebar = false }: { isSidebar?: boolean }) {
  const { cart, isLoadingCart } = useCart();

  const cartHasItems = cart?.totalQuantity! > 0;
  const isEmpty = cart?.lines?.nodes?.length === 0;

  if (isLoadingCart) {
    return (
      <div className="flex h-[calc(100vh-64px)] w-full flex-col items-center justify-center md:h-[calc(100vh-144px)]">
        <Icon icon="sync" className="h-10 w-10 animate-spin text-icon-color" />
      </div>
    );
  }

  if (isEmpty) {
    return <CartEmpty />;
  }

  return (
    <div className="mx-6 pt-4 pb-20 md:mx-[5%]">
      {!isSidebar && <CartHeader />}
      <div className="grid gap-8 [@media(min-width:850px)]:grid-cols-6">
        <div className="flex min-h-full flex-col [@media(min-width:850px)]:col-span-4">
          <CartSystemSelector />
          <CartLines />
          <div className="hidden md:block">{!isSidebar && <CartSubscriptionCard />}</div>
        </div>
        <div className="[@media(min-width:850px)]:col-span-2">{!isSidebar && cartHasItems && <CartSummary />}</div>
        <div className="block md:hidden">{!isSidebar && <CartSubscriptionCard />}</div>
      </div>
      <CartLoginModal />
    </div>
  );
}
