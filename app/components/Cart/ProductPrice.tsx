import { Money } from '@shopify/hydrogen';
import type { MoneyV2 } from '@shopify/hydrogen/storefront-api-types';

export function ProductPrice({ price, compareAtPrice }: { price?: MoneyV2; compareAtPrice?: MoneyV2 | null }) {
  return (
    <div className="">
      {compareAtPrice ? (
        <div className="">
          {price ? <Money data={price} /> : null}
          <s>
            <Money data={compareAtPrice} />
          </s>
        </div>
      ) : price ? (
        <Money data={price} />
      ) : (
        <span>&nbsp;</span>
      )}
    </div>
  );
}
