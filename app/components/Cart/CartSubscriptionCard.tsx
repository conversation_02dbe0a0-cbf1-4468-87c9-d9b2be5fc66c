import { useCart } from '@/hooks/use-cart';
import Icon from '../Icon';
import { Card } from '../ui/card';
import { formatMoney } from '@/lib/utils';
import { Link } from 'react-router';

function FeatureItem({ icon, text }: { icon: 'no-contract' | 'free-returns' | 'thumb-up'; text: string }) {
  return (
    <div className="flex w-1/3 flex-col items-center gap-6 text-center">
      <Icon icon={icon} className="size-12 shrink-0 text-border" />
      <span className="text-body-xs text-[#636363]">{text}</span>
    </div>
  );
}

export function CartSubscriptionCard() {
  const { cart } = useCart();

  const subscriptionTotal = cart?.lines?.nodes.reduce((acc, line) => {
    return acc + (parseFloat(line?.subscription?.amount) || 0) * line.quantity;
  }, 0);

  if (!subscriptionTotal) return null;

  return (
    <Card className="flex w-full flex-col gap-8 rounded-sm border-none bg-[#F7F7F7] p-8 shadow-none">
      <div className="flex w-full flex-col gap-8 md:flex-row">
        <div className="w-full md:w-3/5">
          <h4 className="mb-2 text-body-md font-bold">Please Note: Monthly Subscription Required</h4>
          <p>
            This order requires a subscription. All Aware subscriptions are set up after FexEx delivers this order to
            you.
            <br />
            <br />
            You will be guided to create a monthly subscription for <strong>{formatMoney(subscriptionTotal)}</strong> to
            activate cellular service for this order. Your devices will immediately be ready to setup in the All Aware
            app once the subscription is created.
          </p>
        </div>
        <div className="flex w-full items-start justify-center gap-1 text-body-sm font-bold md:w-2/5 md:gap-4">
          <FeatureItem icon="no-contract" text="No contracts Cancel anytime" />
          <FeatureItem icon="free-returns" text="Free 30-day returns" />
          <FeatureItem icon="thumb-up" text="No hidden fees" />
        </div>
      </div>
      <p className="w-full">
        You acknowledge that you will be required to read, understand, and agree to be bound by All Aware&apos;s United
        States User Notice for Connected Car/Fleet before setting up the subscription. You may read a copy of the notice{' '}
        <Link to="/business/policies/fleet-user-notice" className="underline">
          here
        </Link>
        .
      </p>
    </Card>
  );
}
