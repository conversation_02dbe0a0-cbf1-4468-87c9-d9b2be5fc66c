import { useCart } from '@/hooks/use-cart';
import { CartForm, Money } from '@shopify/hydrogen';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Separator } from '../ui/separator';
import { Checkbox } from '../ui/checkbox';
import { useState } from 'react';
import { SeparatorWithText } from '../SeparatorWithText';
import { Form, useFetcher } from 'react-router';
import Icon from '../Icon';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { AugmentedCart } from '@/business/core/types/cart';
import { useCartSubmit } from '@/hooks/use-cart-fetcher';
import { AttributeInput } from 'storefront.types';
import { SYSTEM_KEY_ATTRIBUTE_KEY } from '@/business/core/constants/cart';

export function CartSummary() {
  const { cart } = useCart();
  const [checked, setChecked] = useState(false);
  const fetcher = useFetcher({ key: `cart-summary` });
  const { submit } = useCartSubmit(fetcher);
  const { subTotal, totalBeforeSavings, totalSavings } = (cart?.cost || {}) as AugmentedCart['cost'];

  const hasErrors = cart?.lines?.nodes?.some(line => line.error);

  const handleCheckboxChange = (checked: boolean) => {
    setChecked(checked);
    submit([
      {
        action: 'AttributesUpdateInput',
        inputs: {
          attributes: [
            ...((cart?.attributes || []) as AttributeInput[]),
            {
              key: 'accepted-terms-and-conditions',
              value: checked ? new Date().toISOString() : '',
            },
          ],
        },
      },
    ]);
  };

  if (!cart) return null;

  const systemKey = cart.attributes?.find(attr => attr.key === SYSTEM_KEY_ATTRIBUTE_KEY)?.value;

  return (
    <Card className="w-full rounded-sm border-none p-8 shadow-lg">
      <h4 className="text-title-4 font-bold">Order Summary</h4>
      <div>
        <dl className="my-6 flex justify-between">
          <dt className="text-body-xs text-body-normal">Before Savings</dt>
          <dd className="text-body-xs text-body-highlight">
            {totalBeforeSavings?.amount ? <Money data={totalBeforeSavings} /> : '-'}
          </dd>
        </dl>
        <dl className="my-6 flex justify-between">
          <dt className="text-body-xs text-body-normal">Savings</dt>
          <dd className="text-body-xs text-body-highlight">
            {totalSavings?.amount ? <Money data={totalSavings} /> : '-'}
          </dd>
        </dl>
        <dl className="my-6 flex justify-between">
          <dt className="text-body-xs text-body-normal">Taxes & Shipping</dt>
          <dd className="text-right text-body-xs text-body-highlight">Calculated at checkout</dd>
        </dl>
        <Separator className="my-4" />
        <dl className="my-6 flex justify-between font-bold">
          <dt className="text-body-sm text-body-normal">Subtotal</dt>
          <dd className="text-body-sm text-body-highlight">{subTotal?.amount ? <Money data={subTotal} /> : '-'}</dd>
        </dl>
      </div>
      <form method="POST" className="flex flex-col gap-6">
        <Button
          type="submit"
          variant="primary"
          name="checkout"
          value="true"
          size="full"
          disabled={Boolean(hasErrors) || !systemKey}
        >
          Checkout
        </Button>
        <div className="flex items-start gap-2 text-body-xxs text-body-normal">
          <Checkbox id="terms" className="mt-1" required checked={checked} onCheckedChange={handleCheckboxChange} />
          <label htmlFor="terms">
            I acknowledge that I have read, understand, and agree to be bound by All Aware&apos;s{' '}
            <a href="/terms-of-service" className="underline">
              Terms of Service
            </a>
            . I acknowledge that I have read and understand All Aware&apos;s{' '}
            <a href="/privacy-policy" className="underline">
              Privacy Policy
            </a>
            .
          </label>
        </div>
      </form>
      <SeparatorWithText text="Guaranteed Secure Checkout With:" />
      <div className="my-6 flex flex-wrap items-center justify-center gap-2">
        <img src="/images/Visa.svg" alt="Visa" className="size-12" />
        <img src="/images/Mastercard.svg" alt="Mastercard" className="size-12" />
        <img src="/images/Amex.svg" alt="American Express" className="size-12" />
        <img src="/images/Discover.svg" alt="Discover" className="size-12" />
        <img src="/images/ShopPay.svg" alt="Shop Pay" className="size-12" />
        <img src="/images/PayPal.svg" alt="PayPal" className="size-12" />
        <img src="/images/GooglePay.svg" alt="Google Pay" className="size-12" />
      </div>
      <p className="flex items-center justify-center gap-2 text-center">
        <span className="text-normal text-body-xs font-bold">0% Financing Available</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Icon icon="info" className="h-5 w-5" />
            </TooltipTrigger>
            <TooltipContent>
              <p>
                Choose ShopPay Installments at checkout for 0% financing. Minimum order amount of $50.00 is required.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </p>
    </Card>
  );
}
