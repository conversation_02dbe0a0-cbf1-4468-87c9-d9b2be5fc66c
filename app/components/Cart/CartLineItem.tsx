import { BUSINESS_ONLY_ERROR_CODE, MAX_DEVICE_COUNT_ERROR_CODE } from '@/business/core/constants/cart';
import { AugmentedCart, CartLineError } from '@/business/core/types/cart';
import useAdvertisedDiscounts from '@/hooks/use-advertised-discounts';
import { useCartSubmit } from '@/hooks/use-cart-fetcher';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { useVariantUrl } from '@/lib/variant';
import { Link, useFetcher } from 'react-router';
import { CartForm, Image, Money, type OptimisticCartLine } from '@shopify/hydrogen';
import { MoneyV2 } from 'storefront.types';
import Icon from '../Icon';
import { Button } from '../ui/button';
import { NumberInput } from '../ui/number-input';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';

type CartLine = OptimisticCartLine<AugmentedCart['lines']['nodes'][number]>;

function QuantitySelector({
  line,
  onRemove,
  onChange,
  isLoading,
  disabled,
  disableIncrement,
}: {
  line: CartLine;
  onRemove: () => void;
  onChange: (qty: number) => void;
  isLoading?: boolean;
  disabled?: boolean;
  disableIncrement?: boolean;
}) {
  return (
    <div className="flex h-7 flex-row items-start gap-4">
      <NumberInput
        value={line.quantity}
        onChange={onChange}
        isLoading={isLoading}
        disabled={disabled}
        disableIncrement={disableIncrement}
      />
      <Button
        loading={!!line.isOptimistic}
        type="button"
        variant="link"
        size="fit"
        onClick={onRemove}
        className="font-normal text-body-normal underline"
        disabled={isLoading}
      >
        Remove
      </Button>
    </div>
  );
}

function DiscountBadge({ percentage }: { percentage?: number }) {
  if (!percentage) return null;

  return (
    <div className="rounded-xs bg-green-one px-2 py-1 text-body-xxs font-bold whitespace-nowrap text-foreground md:text-body-xs">
      {Math.round(percentage)}% OFF
    </div>
  );
}

function DiscountMessage({ discountMessage }: { discountMessage?: string }) {
  if (!discountMessage) return null;

  return <div className="text-sm font-bold text-emphasis">{discountMessage}</div>;
}

function AmountPerQuantity({ amountPerQuantity }: { amountPerQuantity: MoneyV2 }) {
  return (
    <span className="text-md h-7 font-bold text-body-highlight">
      {amountPerQuantity && <Money data={amountPerQuantity} />}
    </span>
  );
}

function CompareAtAmountPerQuantity({ compareAtAmountPerQuantity }: { compareAtAmountPerQuantity?: MoneyV2 | null }) {
  if (!compareAtAmountPerQuantity) return null;

  return (
    <span className="md:text-md text-sm text-body-normal line-through">
      <Money data={compareAtAmountPerQuantity} />
    </span>
  );
}

function ErrorMessage({ error }: { error?: CartLineError }) {
  if (!error) return null;

  return (
    <Tooltip>
      <p className="text-xs text-red-500">
        {error.message}{' '}
        <TooltipTrigger>
          <span className="inline-block">
            <Icon icon="info" className="-mb-[2px] h-[14px] w-[14px]" />
          </span>
        </TooltipTrigger>
      </p>
      <TooltipContent>
        {error.description}{' '}
        <Button variant="link" size="fit" className="text-white underline">
          <Link to={error.link}>Learn More</Link>
        </Button>
      </TooltipContent>
    </Tooltip>
  );
}

/**
 * A single line item in the cart. It displays the product image, title, price.
 * It also provides controls to update the quantity or remove the line item.
 */
export function CartLineItem({ line, isSidebar = false }: { line: CartLine; isSidebar?: boolean }) {
  const { id, merchandise } = line;
  const { product, title, image, selectedOptions } = merchandise;
  const lineItemUrl = useVariantUrl(product.handle, selectedOptions);
  const fetcher = useFetcher({ key: `quantity-${line.id}` });
  const { submit } = useCartSubmit(fetcher);

  const amountPerQuantity = line?.cost?.amountPerQuantity;
  const compareAtAmountPerQuantity = line?.cost?.compareAtAmountPerQuantity;

  const subtotalAmount = line?.cost?.subtotalAmount?.amount * 1.0;
  const totalAmount = line?.cost?.totalAmount?.amount * 1.0;
  const discountPercentage = ((subtotalAmount - totalAmount) / subtotalAmount) * 100;

  const { message: discountMessage } = useAdvertisedDiscounts(line.merchandise.product.id, line.discounts);
  const isMobile = useIsMobileBreakpoint();
  const isBusinessOnly = line.error?.errorCode === BUSINESS_ONLY_ERROR_CODE;
  const isMaxDeviceCount = line.error?.errorCode === MAX_DEVICE_COUNT_ERROR_CODE;

  const updateQuantity = (qty: number) => {
    submit({ action: CartForm.ACTIONS.LinesUpdate, inputs: { lines: [{ id, quantity: qty }] } });
  };

  const handleRemove = () => {
    submit({ action: CartForm.ACTIONS.LinesRemove, inputs: { lineIds: [id] } });
  };

  if (!line || typeof line?.quantity === 'undefined') return null;

  if (isSidebar) {
    return (
      <li className="flex items-center justify-between">
        <div className="flex flex-row items-center gap-4">
          {image && <Image alt={title} aspectRatio="1/1" data={image} loading="lazy" width={100} />}
          <Link prefetch="intent" to={lineItemUrl} className="flex flex-col justify-between">
            <span className="text-body-md font-bold text-body-highlight">{product.title}</span>
            <span className="text-body-xs text-body-normal">Quantity: {line.quantity}</span>
          </Link>
        </div>
        <div className="flex flex-col items-end justify-between">
          <span className="text-md font-bold text-body-highlight">
            {amountPerQuantity && <Money data={amountPerQuantity} />}
          </span>
        </div>
      </li>
    );
  }

  if (isMobile) {
    return (
      <li className="flex items-start gap-2">
        {image && <Image alt={title} aspectRatio="1/1" data={image} loading="lazy" width={100} />}
        <div className="flex flex-col gap-4">
          <Link prefetch="intent" to={lineItemUrl} className="flex h-14 flex-col items-start justify-between">
            <span className="text-body-md font-bold text-body-highlight">{product.title}</span>
            {line.subscription.amount && (
              <span className="text-body-xs text-body-normal">${line.subscription.amount}/mo/device subscription</span>
            )}
          </Link>
          <div className="flex flex-col items-start justify-between gap-4">
            <QuantitySelector
              line={line}
              onRemove={handleRemove}
              onChange={updateQuantity}
              // isLoading={isLoading}
              disabled={isBusinessOnly}
              disableIncrement={isMaxDeviceCount}
            />
            <DiscountMessage discountMessage={discountMessage} />
            <ErrorMessage error={line.error} />
          </div>
          <div className="flex flex-row items-center gap-2">
            <DiscountBadge percentage={discountPercentage} />
            <CompareAtAmountPerQuantity compareAtAmountPerQuantity={compareAtAmountPerQuantity} />
            <AmountPerQuantity amountPerQuantity={amountPerQuantity} />
          </div>
        </div>
      </li>
    );
  }

  // grid layout is needed for this to be properly responsive and to align all the lines which have text that don't fit nicely like in the design
  return (
    <div className="grid grid-cols-[120px_1fr_1fr_1fr] grid-rows-4 gap-2">
      <div className="col-span-1 row-span-4 flex items-center justify-center">
        {image && <Image alt={title} aspectRatio="1/1" data={image} loading="lazy" width={100} />}
      </div>

      <div />
      <div />
      <div />

      <div>
        <span className="text-body-md font-bold text-body-highlight">{product.title}</span>
      </div>

      <div className="flex justify-end">
        <QuantitySelector
          line={line}
          onRemove={handleRemove}
          onChange={updateQuantity}
          disabled={isBusinessOnly}
          disableIncrement={isMaxDeviceCount}
        />
      </div>

      <div className="flex justify-end text-right">
        <AmountPerQuantity amountPerQuantity={amountPerQuantity} />
      </div>

      <div>
        <span className="text-body-xs text-body-normal">${line.subscription.amount}/mo/device subscription</span>
      </div>

      <div className="flex justify-end text-right">
        <DiscountMessage discountMessage={discountMessage} />
      </div>

      <div className="flex flex-row items-start justify-end gap-2 text-right">
        <DiscountBadge percentage={discountPercentage} />
        <CompareAtAmountPerQuantity compareAtAmountPerQuantity={compareAtAmountPerQuantity} />
      </div>

      <div />

      <div className="flex justify-end text-right">
        <ErrorMessage error={line.error} />
      </div>

      <div />
    </div>
  );
}
