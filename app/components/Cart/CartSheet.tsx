import { useCart } from '@/hooks/use-cart';
import { useIsBusinessSite } from '@/hooks/use-is-business-site';
import { cn } from '@/lib/utils';
import { Money } from '@shopify/hydrogen';
import { createContext, ReactNode, useCallback, useContext, useState } from 'react';
import { Link } from 'react-router';
import Icon from '../Icon';
import ThemeProvider from '../ThemeProvider';
import { Button } from '../ui/button';
import { Separator } from '../ui/separator';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '../ui/sheet';
import { SidebarContent, SidebarFooter, SidebarHeader } from '../ui/sidebar';
import { CartLines } from './CartLines';
import React from 'react';

interface CartSheetContextType {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  toggleOpen: () => void;
}

export const CartSheetContext = createContext<CartSheetContextType | null>(null);

export function useCartSheet() {
  const context = useContext(CartSheetContext);
  if (!context) {
    throw new Error('useCartSheet must be used within a CartSheetProvider');
  }
  return context;
}

const CartSheetProvider = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(
  ({ children, ...props }: { children?: ReactNode }, ref) => {
    const [open, setOpen] = useState(false);
    const toggle = useCallback(() => setOpen(open => !open), []);

    return (
      <CartSheetContext.Provider value={{ isOpen: open, setIsOpen: setOpen, toggleOpen: toggle }}>
        <Sheet open={open} onOpenChange={setOpen} {...props}>
          <ThemeProvider theme="personal">
            {themeClassName => (
              <SheetContent
                data-sidebar="sidebar"
                data-mobile="true"
                className={cn(themeClassName, 'w-full bg-background p-0 [&>button]:hidden')}
                side="right"
              >
                <SheetHeader hidden className="hidden">
                  <SheetTitle>Cart</SheetTitle>
                </SheetHeader>
                <div className="flex h-full w-full flex-col">
                  <CartSheetContent />
                </div>
              </SheetContent>
            )}
          </ThemeProvider>
        </Sheet>
        {children}
      </CartSheetContext.Provider>
    );
  },
);

function CartSheetContent() {
  const { toggleOpen } = useCartSheet();
  const { cart } = useCart();
  const { subTotal } = cart?.cost || {};

  const isBusiness = useIsBusinessSite();
  const cartRoute = isBusiness ? '/business/cart' : '/home/<USER>';

  return (
    <>
      <SidebarHeader className="px-6 sm:px-8">
        <div className="flex items-center justify-between py-4">
          <h2 className="text-lg font-semibold text-body-highlight">Ready for checkout</h2>
          <Button onClick={toggleOpen} variant="ghost" size="icon" className="-mr-2 text-muted-foreground">
            <Icon icon="close" className="h-8 w-8" />
          </Button>
        </div>
        <Separator />
      </SidebarHeader>
      <SidebarContent className="px-6 sm:px-8">
        <CartLines isSidebar />
      </SidebarContent>
      <SidebarFooter className="px-6 pb-12 sm:px-8 sm:pb-16">
        <Separator className="my-4" />
        <dl className="flex justify-between text-body-sm font-bold text-body-highlight">
          <dt>Subtotal</dt>
          <dd>{subTotal?.amount && <Money data={subTotal} />}</dd>
        </dl>
        <p className="mb-6 text-body-xxs text-body-normal">Taxes & shipping calculated at checkout</p>
        <Button variant="primary" size="full" onClick={toggleOpen} asChild>
          <Link to={cartRoute} prefetch="viewport">
            View Cart & Checkout
          </Link>
        </Button>
        <Button variant="outline" size="full" onClick={toggleOpen}>
          Continue shopping
        </Button>
      </SidebarFooter>
    </>
  );
}

export default CartSheetProvider;
