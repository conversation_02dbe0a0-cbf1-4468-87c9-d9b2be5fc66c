import {
  COMPANY_LOCATION_ID_ATTRIBUTE_KEY,
  NEW_SYSTEM_KEY_ATTRIBUTE_VALUE,
  ORDERING_CUSTOMER_ID_ATTRIBUTE_KEY,
  SYSTEM_KEY_ATTRIBUTE_KEY,
  SYSTEM_NAME_ATTRIBUTE_KEY,
  SYSTEM_TYPE_ATTRIBUTE_KEY,
} from '@/business/core/constants/cart';
import { useCart } from '@/hooks/use-cart';
import { useCartSubmit } from '@/hooks/use-cart-fetcher';
import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useFetcher } from 'react-router';
import { AttributeInput } from 'storefront.types';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { CartNewSystemModal } from './CartNewSystemModal';

export default function CartSystemSelector() {
  const { cart, systems, systemType, fallbackLocationId, orderingCustomerId } = useCart();

  const { accessToken } = useCart();
  const fetcher = useFetcher({ key: `cart-system-selector` });
  const { submit } = useCartSubmit(fetcher);
  const [systemNameModalOpen, setSystemNameModalOpen] = useState(false);

  const selectSystem = useCallback(
    (systemId: string, systemName?: string) => {
      const existingAttributes = (cart?.attributes?.filter(
        attribute =>
          attribute.key !== SYSTEM_KEY_ATTRIBUTE_KEY &&
          attribute.key !== SYSTEM_TYPE_ATTRIBUTE_KEY &&
          attribute.key !== SYSTEM_NAME_ATTRIBUTE_KEY &&
          attribute.key !== ORDERING_CUSTOMER_ID_ATTRIBUTE_KEY &&
          attribute.key !== COMPANY_LOCATION_ID_ATTRIBUTE_KEY,
      ) || []) as AttributeInput[];

      systemName ||=
        cart?.attributes?.find(attribute => attribute.key === SYSTEM_NAME_ATTRIBUTE_KEY)?.value || undefined;

      const newAttributes: AttributeInput[] = [
        {
          key: SYSTEM_KEY_ATTRIBUTE_KEY,
          value: systemId,
        },
        {
          key: SYSTEM_TYPE_ATTRIBUTE_KEY,
          value: systemType!,
        },
        systemName && systemId == NEW_SYSTEM_KEY_ATTRIBUTE_VALUE
          ? {
              key: SYSTEM_NAME_ATTRIBUTE_KEY,
              value: systemName,
            }
          : null,
        orderingCustomerId
          ? {
              key: ORDERING_CUSTOMER_ID_ATTRIBUTE_KEY,
              value: orderingCustomerId,
            }
          : null,
        fallbackLocationId
          ? {
              key: COMPANY_LOCATION_ID_ATTRIBUTE_KEY,
              value: fallbackLocationId,
            }
          : null,
      ].filter(Boolean);

      submit([
        {
          action: 'AttributesUpdateInput',
          inputs: {
            attributes: [...existingAttributes, ...newAttributes],
          },
        },
        {
          action: 'BuyerIdentityUpdate',
          inputs: {
            buyerIdentity: {
              companyLocationId: fallbackLocationId || null,
              customerAccessToken: accessToken || null,
            },
          },
        },
      ]);
    },
    [cart, fallbackLocationId, orderingCustomerId, accessToken, systemType, submit],
  );

  const handleValueChange = useCallback(
    (selected: string) => {
      if (selected === NEW_SYSTEM_KEY_ATTRIBUTE_VALUE) {
        setSystemNameModalOpen(true);
      } else {
        selectSystem(selected);
      }
    },
    [selectSystem, setSystemNameModalOpen],
  );

  const customerIdAtt = cart?.attributes?.find(
    attribute => attribute.key === ORDERING_CUSTOMER_ID_ATTRIBUTE_KEY,
  )?.value;
  const systemKeyAtt = cart?.attributes?.find(attribute => attribute.key === SYSTEM_KEY_ATTRIBUTE_KEY)?.value;
  const systemTypeAtt = cart?.attributes?.find(attribute => attribute.key === SYSTEM_TYPE_ATTRIBUTE_KEY)?.value;
  const systemNameAtt = cart?.attributes?.find(attribute => attribute.key === SYSTEM_NAME_ATTRIBUTE_KEY)?.value;
  const value =
    customerIdAtt == orderingCustomerId &&
    systemTypeAtt == systemType &&
    cart?.buyerIdentity?.purchasingCompany?.location?.id == fallbackLocationId
      ? systemKeyAtt || undefined
      : null;
  useEffect(() => {
    // console.log(systems, fallbackLocationId, orderingCustomerId, value);
    if (
      systems === undefined ||
      (systemType == 'business' && fallbackLocationId === undefined) ||
      orderingCustomerId === undefined
    )
      return;

    if (!value || (!systems?.some(system => system.systemKey == value) && value !== NEW_SYSTEM_KEY_ATTRIBUTE_VALUE))
      selectSystem(systems?.at(0)?.systemKey || NEW_SYSTEM_KEY_ATTRIBUTE_VALUE);
  }, [value, systems, fallbackLocationId, orderingCustomerId]);

  if (!systems?.length) return null;

  return (
    <>
      <Select value={value || 'loading'} onValueChange={handleValueChange}>
        <SelectTrigger className="max-w-[200px]">
          {!value || fetcher.state != 'idle' ? <Loader2 className="size-4 animate-spin" /> : <SelectValue />}
        </SelectTrigger>
        <SelectContent>
          {systems?.map(path => (
            <SelectItem key={path.systemKey} value={path.systemKey}>
              {path.name}
            </SelectItem>
          ))}
          <SelectItem value={NEW_SYSTEM_KEY_ATTRIBUTE_VALUE}>
            {value == NEW_SYSTEM_KEY_ATTRIBUTE_VALUE && systemNameAtt ? `+ ${systemNameAtt}` : '+ New system'}
          </SelectItem>
        </SelectContent>
      </Select>
      <CartNewSystemModal open={systemNameModalOpen} setOpen={setSystemNameModalOpen} onSubmit={selectSystem} />
    </>
  );
}
