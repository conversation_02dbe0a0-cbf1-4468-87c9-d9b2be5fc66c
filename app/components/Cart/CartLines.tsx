import { useCart } from '@/hooks/use-cart';
import { Fragment } from 'react';
import { Separator } from '../ui/separator';
import { CartLineItem } from './CartLineItem';

export function CartLines({ isSidebar = false }: { isSidebar?: boolean }) {
  const { cart } = useCart();

  return (
    <ul className="mb-16">
      {(cart?.lines?.nodes ?? []).map((line, i) => (
        <Fragment key={line.id}>
          <CartLineItem line={line as any} isSidebar={isSidebar} />
          {cart?.lines?.nodes &&
            cart.lines.nodes.length > 1 &&
            i < cart.lines.nodes.length - 1 &&
            (isSidebar ? <div className="my-4" /> : <Separator className="my-6" />)}
        </Fragment>
      ))}
    </ul>
  );
}
