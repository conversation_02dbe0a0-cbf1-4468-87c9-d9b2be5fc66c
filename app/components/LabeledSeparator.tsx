import { cn } from '@/lib/utils';
import { Separator } from './ui/separator';

export function LabeledSeparator({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <div className={cn('flex items-center gap-4 py-2', className)}>
      <Separator className="flex-1" />
      <span className="text-sm font-bold text-muted-foreground">{children}</span>
      <Separator className="flex-1" />
    </div>
  );
}
