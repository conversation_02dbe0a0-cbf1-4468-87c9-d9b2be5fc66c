import { ReactNode } from 'react';
import HorizontalScrollJack from '.';
import { Blocks, BuilderBlock, RegisteredComponent } from '@builder.io/sdk-react/edge';

export const HORIZONTAL_SCROLL_JACK_COMPONENT: RegisteredComponent = {
  name: 'Horizontal Scroll Jack',
  component: ({
    align = 'bottom',
    builderBlock,
    headerBlocks,
    children,
    footerBlocks,
  }: {
    align?: 'bottom' | 'top';
    builderBlock: BuilderBlock;
    headerBlocks: BuilderBlock[];
    footerBlocks: BuilderBlock[];
    children?: ReactNode;
  }) => (
    <HorizontalScrollJack
      align={align}
      header={<Blocks parent={builderBlock.id} blocks={headerBlocks} path="headerBlocks" />}
      footer={<Blocks parent={builderBlock.id} blocks={footerBlocks} path="footerBlocks" />}
    >
      {children}
    </HorizontalScrollJack>
  ),
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  canHaveChildren: true,
  defaultStyles: {},
  inputs: [
    {
      name: 'align',
      type: 'enum',
      defaultValue: 'bottom',
      enum: ['bottom', 'top'],
    },
    {
      name: 'headerBlocks',
      type: 'uiBlocks',
      // hideFromUI: true,
      defaultValue: [],
    },
    {
      name: 'footerBlocks',
      type: 'uiBlocks',
      // hideFromUI: true,
      defaultValue: [],
    },
  ],
};
