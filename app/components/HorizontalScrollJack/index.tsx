import useAnimateOnView from '@/hooks/use-animate-on-view';
import { useIsomorphicLayoutEffect } from '@/hooks/use-isomorphic-effect';
import { cn } from '@/lib/utils';
import { isEditing } from '@builder.io/sdk-react/edge';
import { useRef } from 'react';

interface HorizontalScrollJackProps {
  align?: 'top' | 'bottom';
  header?: React.ReactNode;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
}

export default function HorizontalScrollJack({
  align = 'bottom',
  header,
  children,
  footer,
  className,
}: HorizontalScrollJackProps) {
  const stickyContainerRef = useRef<HTMLDivElement>(null);
  // const scrollContainerRef = useRef<HTMLDivElement>(null);
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const spacerRef = useRef<HTMLDivElement>(null);

  const { ref: verticalContainerRef } = useAnimateOnView(verticalContainer => {
    if (typeof window == 'undefined' || isEditing()) return;

    const stickyContainer = stickyContainerRef.current!;
    const contentContainer = contentContainerRef.current!;
    const spacer = spacerRef.current!;

    if (!verticalContainer || !contentContainer || !spacer || !stickyContainer) return;

    const alignOffset = align == 'bottom' ? window.innerHeight - stickyContainer.scrollHeight : 0;
    const contentSize = contentContainer.scrollWidth - stickyContainer.clientWidth;
    const scroll = verticalContainer.getBoundingClientRect().top - alignOffset;

    spacer.style.height = `${contentSize}px`;
    const scrollLeft = Math.min(Math.max(0, -scroll), contentSize);
    contentContainer.style.transform = `translateX(${-scrollLeft}px)`;

    const headerAndFooterHeight = [...stickyContainer.childNodes].reduce(
      (acc, child) => acc + (child != contentContainer ? (child as HTMLElement).clientHeight : 0),
      0,
    );
    contentContainer.style.height = stickyContainer.clientHeight - headerAndFooterHeight + 'px';
  });

  if (isEditing()) {
    return (
      <>
        {header}
        {children}
        {footer}
      </>
    );
  }

  return (
    // Vertical container
    <div ref={verticalContainerRef} className={className}>
      {align === 'bottom' && <div ref={spacerRef}></div>}
      {/* Sticky container */}
      <div
        ref={stickyContainerRef}
        //  h-svh grid grid-rows-[min-content_auto_min-content]
        className={cn('sticky h-svh', {
          'top-0': align === 'top',
          'bottom-0': align === 'bottom',
        })}
      >
        <div>{header}</div>
        {/* Content container the size of the children */}
        <div
          ref={contentContainerRef}
          className={cn('flex h-full flex-nowrap items-center *:shrink-0')}
          // style={{
          //   containerName: 'horizontal-scroll-jack',
          //   containerType: 'inline-size',
          // }}
        >
          {children}
        </div>
        <div>{footer}</div>
      </div>
      {align === 'top' && <div ref={spacerRef}></div>}
    </div>
  );
}
