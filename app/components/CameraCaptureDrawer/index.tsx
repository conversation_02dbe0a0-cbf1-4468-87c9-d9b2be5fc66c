import { X } from 'lucide-react';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import CameraCapture from '../CameraCapture';
import { Button } from '../ui/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  Drawer<PERSON>itle,
  DrawerTrigger,
} from '../ui/drawer';
import { cn } from '@/lib/utils';

export interface CameraCaptureDrawerProps {
  children: ReactNode;
  title?: string;
  description?: string;
  onCapture?: (blob: Blob) => void;
}

export default function CameraCaptureDrawer({ children, title, description, onCapture }: CameraCaptureDrawerProps) {
  const [blob, setBlob] = useState<Blob | null>(null);
  const [dataUrl, setDataUrl] = useState<string | null>(null);

  const clear = useCallback(() => setBlob(null), []);
  const submit = useCallback(() => {
    if (onCapture && blob) onCapture(blob);
  }, [onCapture, blob]);
  useEffect(() => {
    if (!blob) {
      setDataUrl(null);
      return;
    }

    const url = URL.createObjectURL(blob);

    setDataUrl(url);

    return () => URL.revokeObjectURL(url);
  }, [blob]);

  return (
    <Drawer onClose={clear}>
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent>
        <div className="mx-auto w-full max-w-sm">
          <DrawerHeader>
            <DrawerTitle>{title}</DrawerTitle>
            <DrawerDescription>{description}</DrawerDescription>
          </DrawerHeader>
          {dataUrl && (
            <div className="relative h-[50vh] w-full bg-muted">
              <Button size="icon" onClick={clear} className="absolute top-4 right-4 bg-destructive">
                <X />
              </Button>
              <img src={dataUrl} className="mx-auto max-h-full animate-flash" />
            </div>
          )}
          <CameraCapture className={cn('h-[50vh] w-full bg-muted', dataUrl && 'hidden')} onCapture={setBlob} />
          <DrawerFooter>
            <DrawerClose asChild>
              {blob ? <Button onClick={submit}>Submit</Button> : <Button variant="outline">Cancel</Button>}
            </DrawerClose>
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
