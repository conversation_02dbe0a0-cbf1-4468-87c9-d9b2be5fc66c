import { cn } from '@/lib/utils';
import { useProduct } from '../ProductDataProvider/context';
import { Skeleton } from '../ui/skeleton';

export default function ProductTitle({ attributes }: { attributes?: any }) {
  const { product } = useProduct();
  if (!product) return <Skeleton {...attributes} className={cn('h-14 w-3/4 xl:h-18', attributes?.className)} />;
  return (
    <h1
      {...attributes}
      className={cn(`text-5xl leading-normal font-semibold tracking-wide xl:text-6xl`, attributes?.className)}
    >
      {product?.title}
    </h1>
  );
}
