import { cn } from '@/lib/utils';
import { useNavigation } from 'react-router';
import { useEffect, useRef } from 'react';

export default function PageInfiniteLoader({
  navigating,
  halfLifeMS = 1000,
}: {
  halfLifeMS?: number;
  navigating?: boolean;
}) {
  const nav = useNavigation();
  const barRef = useRef<HTMLDivElement>(null);
  const isNavigating = nav.state !== 'idle' || navigating;

  useEffect(() => {
    if (!isNavigating) return;

    let play = true;
    const tI = Date.now();
    function animate() {
      if (!play) return;

      const t = (Date.now() - tI) / halfLifeMS;
      const p = 1 - 1 / (t * t + 1);

      if (barRef.current) barRef.current.style.width = `${p * 100}%`;

      requestAnimationFrame(animate);
    }

    requestAnimationFrame(animate);
    return () => {
      play = false;
    };
  }, [isNavigating, nav.formAction, nav.location]);

  return (
    <div className={cn('fixed top-0 right-0 left-0 z-100 h-1 overflow-clip bg-muted', { hidden: !isNavigating })}>
      <div ref={barRef} className="h-full rounded-tr-sm rounded-br-sm bg-primary" />
    </div>
  );
}
