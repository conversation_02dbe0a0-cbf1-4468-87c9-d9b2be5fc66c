import { ReactEventHandler, useCallback, useEffect, useRef, useState } from 'react';
import CapturePhotoDialog, { CapturePhotoDialogProps } from '../CapturePhotoDialog';
import QrScanner from 'qr-scanner';

export type ScanQRDialogProps = Omit<CapturePhotoDialogProps, 'videoReadyCallback' | 'onCapture'> & {
  onScan: (data: string) => void;
  fps?: number;
};

export default function ScanQRDialog({ open: _open, onOpenChange, onScan, fps = 10, ...props }: ScanQRDialogProps) {
  const [isOpen, setIsOpen] = useState(_open);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const open = onOpenChange ? _open : isOpen;

  const changeOpen = useCallback(
    (open: boolean) => {
      setIsOpen(open);
      onOpenChange?.(open);
    },
    [onOpenChange],
  );

  const videoReadyCallback = useCallback<ReactEventHandler<HTMLVideoElement>>(event => {
    videoRef.current = event.target as HTMLVideoElement;
  }, []);

  useEffect(() => {
    if (!open) return;

    const qrEngine = QrScanner.createQrEngine();
    let scan = true;
    function scanQR() {
      if (!videoRef.current || !scan) return;

      QrScanner.scanImage(videoRef.current, { returnDetailedScanResult: true, qrEngine, canvas: canvasRef.current })
        .then(result => {
          if (!scan) return;
          scan = false;
          changeOpen(false);
          onScan(result.data);
        })
        .catch(error => {});
    }
    const id = setInterval(scanQR, 1000 / fps);
    return () => clearInterval(id);
  }, [open]);

  return (
    <>
      <canvas ref={canvasRef} className="hidden" />
      <CapturePhotoDialog
        videoReadyCallback={videoReadyCallback}
        preferredZoom={1.5}
        open={open}
        onOpenChange={changeOpen}
        {...props}
      />
    </>
  );
}
