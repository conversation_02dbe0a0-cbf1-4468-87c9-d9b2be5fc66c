import { RegisteredComponent } from '@builder.io/sdk-react/edge';
import Icon from '.';
import { ICON_KEYS, IconName } from '@/components/Icon/props';

interface BuilderProps {
  icon: IconName;
  size: number;
  color: string;
}

export const ICON_COMPONENT: RegisteredComponent = {
  component: ({ color, icon, size }: BuilderProps) => <Icon icon={icon} style={{ color, width: size, height: size }} />,
  name: 'Icon',
  inputs: [
    {
      name: 'size',
      type: 'number',
      defaultValue: 24,
    },
    {
      name: 'icon',
      type: 'string',
      enum: ICON_KEYS,
    },
    {
      name: 'color',
      type: 'color',
      defaultValue: '#000',
    },
  ],
};
