import Icon from '.';
import { icons } from './icons';

const fixtures: any = {};

for (const icon of Object.keys(icons)) {
  fixtures[`Dark Gray (${icon})`] = <Icon className="text-gray-dark h-20 w-20" icon={icon as any} />;
}

for (const icon of Object.keys(icons)) {
  fixtures[`White (${icon})`] = (
    <div className="bg-gray-dark">
      <Icon className="h-20 w-20 text-white" icon={icon as any} />
    </div>
  );
}

export default fixtures;
