import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgAdcDiamond = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" data-name="Layer 1" viewBox="0 0 144 144" ref={ref} {...props}><path d="M72.18 135.87a6 6 0 0 1-4.52-2.11l-46.5-54.88a5.94 5.94 0 0 1-.74-6.58l12.2-23.57a7.86 7.86 0 0 1 7-4.27h64.93a7.92 7.92 0 0 1 6.91 4.06l13.16 23.64a6 6 0 0 1-.69 6.78L76.69 133.8a6 6 0 0 1-4.51 2.07M28.15 74.76l44 52L117 74.8l-12.51-22.39-64.85.05ZM27.53 76" className="cls-1" /><path d="M121.81 78.3H23.37a3 3 0 0 1 0-6h98.44a3 3 0 0 1 0 6" className="cls-1" /><path d="M72.16 135.92a3 3 0 0 1-2.89-2.2L53.59 76.6 34.82 50.2a3 3 0 1 1 4.89-3.48L59.1 74l.14.51 15.82 57.63a3 3 0 0 1-2.1 3.68 2.8 2.8 0 0 1-.8.1" className="cls-1" /><path d="M72 135.15a3 3 0 0 1-2.88-3.84L85.77 74l18.62-27.24a3 3 0 1 1 5 3.39L91.27 76.6 74.83 133a3 3 0 0 1-2.83 2.15" className="cls-1" /><path d="M88.52 78.3a3 3 0 0 1-2.59-1.49L72.78 54.29 58.9 76.87a3 3 0 0 1-5.11-3.14L69 49a4.43 4.43 0 0 1 3.8-2.12 4.4 4.4 0 0 1 3.8 2.21l14.51 24.7a3 3 0 0 1-1.11 4.1 2.93 2.93 0 0 1-1.48.41M72 33.25a3 3 0 0 1-3-3V13.07a3 3 0 0 1 6 0v17.18a3 3 0 0 1-3 3M104.38 39.76a3 3 0 0 1-2.57-4.55l8.84-14.73a3 3 0 0 1 5.15 3.08L107 38.3a3 3 0 0 1-2.62 1.46M123.4 57.89a3 3 0 0 1-1.4-5.65l15.16-8.09a3 3 0 1 1 2.84 5.29l-15.15 8.1a3 3 0 0 1-1.45.35M39.33 39.76a3 3 0 0 1-2.57-1.46l-8.84-14.74a3 3 0 0 1 5.15-3.08l8.83 14.73a3 3 0 0 1-1 4.12 3 3 0 0 1-1.57.43M20.32 57.89a3 3 0 0 1-1.41-.35l-15.16-8.1a3 3 0 1 1 2.83-5.29l15.16 8.09a3 3 0 0 1-1.42 5.65" className="cls-1" /></svg>;
const ForwardRef = forwardRef(SvgAdcDiamond);
export default ForwardRef;