import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgAdcAccessControlBadgeHand = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" data-name="Layer 1" viewBox="0 0 144 144" ref={ref} {...props}><path d="M94.43 77.55a8 8 0 0 0 10.5-4.08l20.6-46.7a8 8 0 0 0-4.08-10.5l-26-11.47A8 8 0 0 0 85 8.87L72.13 38M85.63 73.67l8.8 3.88" className="cls-1" /><path d="M36.19 90.51s21.66-21.18 27.4-33.67c5.27-11.48 18.34-5.21 13.22 5.52S67.42 82.62 72.07 86s32.47-4.21 44.37-19.32c11.64-14.78 29.15-9 18.61 2.31-15.88 17-30.41 37.48-38 41.2s-41 12.9-43.82 14.07" className="cls-2" /><path d="m95.36 17.02 16.02 7.04" className="cls-1" /><rect width={16.07} height={48.44} x={17.17} y={91.56} className="cls-1" rx={2} transform="rotate(-26.32 25.21 115.777)" /></svg>;
const ForwardRef = forwardRef(SvgAdcAccessControlBadgeHand);
export default ForwardRef;