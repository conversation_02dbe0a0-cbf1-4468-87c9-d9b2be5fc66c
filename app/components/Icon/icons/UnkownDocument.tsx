import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgUnkownDocument = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M200-800v640zv200zm80 400h147q11-23 25.5-43t32.5-37H280zm0 160h123q-3-20-3-40t3-40H280zM200-80q-33 0-56.5-23.5T120-160v-640q0-33 23.5-56.5T200-880h320l240 240v92q-19-6-39-9t-41-3v-40H480v-200H200v640h227q11 23 25.5 43T485-80zm480-400q83 0 141.5 58.5T880-280t-58.5 141.5T680-80t-141.5-58.5T480-280t58.5-141.5T680-480m0 320q11 0 18.5-7.5T706-186t-7.5-18.5T680-212t-18.5 7.5T654-186t7.5 18.5T680-160m-18-76h36v-10q0-11 6-19.5t14-16.5q14-12 22-23t8-31q0-29-19-46.5T680-400q-23 0-41.5 13.5T612-350l32 14q3-12 12.5-21t23.5-9q15 0 23.5 7.5T712-336q0 11-6 18.5T692-302q-6 6-12.5 12T668-276q-3 6-4.5 12t-1.5 14z" /></svg>;
const ForwardRef = forwardRef(SvgUnkownDocument);
export default ForwardRef;