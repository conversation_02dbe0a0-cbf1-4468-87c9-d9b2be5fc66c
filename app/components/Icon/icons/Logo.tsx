import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgLogo = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={40} height={40} fill="none" viewBox="0 0 40 40" ref={ref} {...props}><g fill="currentColor" clipPath="url(#a)"><path d="M20.094 40a20.12 20.12 0 0 1-16.6-8.734 2.24 2.24 0 0 1 .588-3.124 2.26 2.26 0 0 1 3.137.589 15.6 15.6 0 0 0 12.875 6.774c6.179 0 11.777-3.639 14.267-9.271a2.26 2.26 0 0 1 2.974-1.151 2.243 2.243 0 0 1 1.155 2.962c-3.209 7.264-10.432 11.957-18.399 11.957zM2.331 23.899a2.255 2.255 0 0 1-2.245-2.041A20 20 0 0 1 0 19.986C0 9.336 8.359.558 19.03.005a2.25 2.25 0 0 1 2.372 2.126 2.25 2.25 0 0 1-2.134 2.362c-8.273.43-14.752 7.237-14.752 15.495 0 .485.022.975.067 1.454a2.25 2.25 0 0 1-2.25 2.454zM37.743 20.556A2.25 2.25 0 0 0 40 18.31a2.25 2.25 0 0 0-2.257-2.248 2.25 2.25 0 0 0-2.256 2.248 2.25 2.25 0 0 0 2.256 2.247M34.774 12.121a2.25 2.25 0 0 0 2.256-2.247 2.25 2.25 0 0 0-2.257-2.248 2.25 2.25 0 0 0-2.256 2.248 2.25 2.25 0 0 0 2.256 2.247M27.888 6.428a2.25 2.25 0 0 0 2.257-2.248 2.25 2.25 0 0 0-2.257-2.247 2.25 2.25 0 0 0-2.257 2.247 2.25 2.25 0 0 0 2.257 2.248" /></g><defs><clipPath id="a"><path fill="currentColor" d="M0 0h40v40H0z" /></clipPath></defs></svg>;
const ForwardRef = forwardRef(SvgLogo);
export default ForwardRef;