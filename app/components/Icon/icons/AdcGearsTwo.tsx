import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgAdcGearsTwo = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 133.9 141.83" ref={ref} {...props}><g data-name="Layer 2"><g data-name="Layer 1"><path d="M47.8 115.85a20.57 20.57 0 1 1 7.54-1.45 20.4 20.4 0 0 1-7.54 1.45m0-35A14.54 14.54 0 1 0 53.57 82a14.55 14.55 0 0 0-5.76-1.19Z" className="cls-1" /><path d="M36 141.83a8.3 8.3 0 0 1-3.26-.68l-9.31-4a8.22 8.22 0 0 1-4.93-8l.33-5.36a58 58 0 0 0-7.8-2.15 7.93 7.93 0 0 1-6.5-5l-3.7-9.36a8.23 8.23 0 0 1 1.67-8.62l4.29-4.59a.2.2 0 0 0 0-.27L2 88.34a8.25 8.25 0 0 1-1.33-8.66l3.93-9.07a8.24 8.24 0 0 1 7.24-4.93l6.28-.22a.19.19 0 0 0 .19-.18L18.8 58a8.22 8.22 0 0 1 5.2-7l9.19-3.64A8.26 8.26 0 0 1 41.77 49l4.59 4.28a.19.19 0 0 0 .26 0l5.46-4.77a8.25 8.25 0 0 1 8.66-1.35l9.07 3.94a8.22 8.22 0 0 1 4.93 7.23l.26 6.25a.19.19 0 0 0 .19.19l7.23.49a8.22 8.22 0 0 1 7.07 5.17l3.64 9.19a8.25 8.25 0 0 1-1.63 8.61l-4.29 4.59a.2.2 0 0 0 0 .27l4.77 5.46a8.24 8.24 0 0 1 1.35 8.65l-3.93 9.07a8.26 8.26 0 0 1-7.24 4.94l-6.28.21a.2.2 0 0 0-.19.19l-.49 7.23a8.25 8.25 0 0 1-5.2 7.08l-9.67 3.82a7.86 7.86 0 0 1-7.46-.89l-5.5-3.65a.18.18 0 0 0-.23 0l-5.95 4.8a8.2 8.2 0 0 1-5.19 1.83m-24-28.2.46.07c13.25 2.68 14.44 5.45 14.53 8.06v.24l-.46 7.63a.18.18 0 0 0 .12.19l9.31 4a.19.19 0 0 0 .2 0L42.1 129a8.17 8.17 0 0 1 9.69-.45l5.69 3.78 9.57-3.83a.21.21 0 0 0 .13-.17l.49-7.24a8.17 8.17 0 0 1 7.89-7.64l6.28-.22a.2.2 0 0 0 .18-.12L86 104a.19.19 0 0 0 0-.21l-4.77-5.46a8.19 8.19 0 0 1 .18-11l4.29-4.59a.2.2 0 0 0 0-.21L82 73.37a.21.21 0 0 0-.17-.13l-7.23-.48a8.19 8.19 0 0 1-7.6-7.9l-.21-6.28a.2.2 0 0 0-.12-.17l-9.07-3.94a.22.22 0 0 0-.21 0l-5.46 4.77a8.18 8.18 0 0 1-11-.18l-4.59-4.29a.23.23 0 0 0-.21 0l-9.19 3.64a.18.18 0 0 0-.13.17l-.49 7.23a8.18 8.18 0 0 1-7.89 7.65l-6.28.21a.22.22 0 0 0-.18.12L8 82.86a.2.2 0 0 0 0 .21l4.78 5.46a8.2 8.2 0 0 1-.18 11l-4.29 4.6a.18.18 0 0 0 0 .2Z" className="cls-1" data-name="Wellness Marketing" /><g data-name="SettingsGear"><path d="M102.34 47.33a14.39 14.39 0 1 1 14.39-14.39 14.4 14.4 0 0 1-14.39 14.39m0-22.78a8.39 8.39 0 1 0 8.39 8.39 8.39 8.39 0 0 0-8.39-8.39" className="cls-1" /><path d="M106.06 63.21h-6.77a5.52 5.52 0 0 1-4.63-2.5l-2.34-3.36-4.55 1.42A5.69 5.69 0 0 1 82 57.35l-4.67-4.67a5.74 5.74 0 0 1-1.13-6.48l1.35-2.89a37 37 0 0 0-3.92-2.92 5.57 5.57 0 0 1-2.94-4.9v-6.55A5.76 5.76 0 0 1 74 23.76l3.47-1.65-1.5-4.21a5.75 5.75 0 0 1 1.35-6l4.55-4.55A5.75 5.75 0 0 1 87.8 6l3.63 1.28 1.92-4A5.74 5.74 0 0 1 98.52 0H105a5.76 5.76 0 0 1 5.18 3.27l1.65 3.48 4.17-1.5a5.73 5.73 0 0 1 6 1.34l4.55 4.55a5.74 5.74 0 0 1 1.35 6l-1.29 3.63 4 1.92a5.76 5.76 0 0 1 3.28 5.18v6.43a5.75 5.75 0 0 1-3.28 5.17l-3.47 1.65 1.5 4.22a5.75 5.75 0 0 1-1.35 6l-4.55 4.54a5.74 5.74 0 0 1-6 1.35l-3.62-1.29-1.92 4a5.78 5.78 0 0 1-5.14 3.27m-6.52-6h6.35l2-4.11a5.71 5.71 0 0 1 7.1-2.94l3.69 1.31 4.31-4.31-1.52-4.28a5.71 5.71 0 0 1 2.94-7.1l3.54-1.68V28l-4.11-1.95a5.71 5.71 0 0 1-2.94-7.1l1.31-3.69-4.36-4.26-4.28 1.53a5.72 5.72 0 0 1-7.1-2.94L104.79 6h-6.1l-2 4.11a5.72 5.72 0 0 1-7.1 2.94L86 11.73 81.64 16l1.53 4.29a5.73 5.73 0 0 1-2.94 7.1l-3.54 1.68v6.11l.08.05c6.87 4.46 7.78 6.68 7 8.82l-.1.26-2 4.23L86.13 53l4.59-1.44a5.73 5.73 0 0 1 6.42 2.2Zm22.68-42.13" className="cls-1" data-name="Wellness Marketing" /></g></g></g></svg>;
const ForwardRef = forwardRef(SvgAdcGearsTwo);
export default ForwardRef;