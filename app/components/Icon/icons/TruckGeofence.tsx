import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgTruckGeofence = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={42} height={38} fill="none" viewBox="0 0 42 38" ref={ref} {...props}><path stroke="currentColor" d="M20.638 8.713q1.37-1.022 2.05-2.041.678-1.02.679-1.95 0-1.413-.889-2.129-.889-.717-1.852-.716-.957 0-1.85.717-.893.716-.893 2.128 0 .924.684 1.913.684.99 2.071 2.078Zm-.013.787q-1.689-1.26-2.532-2.449t-.843-2.33q0-.857.31-1.505.308-.648.792-1.085a3.3 3.3 0 0 1 1.084-.66 3.4 3.4 0 0 1 1.19-.221q.59 0 1.192.222.6.222 1.086.66.484.436.79 1.085T24 4.723q0 1.137-.839 2.327-.84 1.19-2.536 2.45Z" /><path fill="currentColor" d="M12.21 34.31q-1.668 0-2.84-1.176t-1.172-2.853H6V14.82q0-.933.663-1.597a2.17 2.17 0 0 1 1.594-.664h19.986v5.626h3.562l4.945 6.62v5.475h-2.448q0 1.678-1.173 2.853t-2.85 1.176q-1.672 0-2.842-1.176t-1.17-2.853H16.233q0 1.68-1.177 2.855-1.177 1.174-2.845 1.174m.006-1.817q.92 0 1.562-.643.642-.642.642-1.565t-.642-1.564a2.13 2.13 0 0 0-1.562-.644q-.921 0-1.563.643t-.642 1.566.642 1.564 1.563.643m-4.403-4.028h.868a3.97 3.97 0 0 1 1.405-1.587 3.7 3.7 0 0 1 2.112-.631q1.142 0 2.105.632.963.631 1.423 1.586H26.43v-14.09H8.257a.42.42 0 0 0-.305.14.43.43 0 0 0-.139.306zm22.472 4.028q.92 0 1.562-.643.642-.642.642-1.565t-.642-1.564a2.13 2.13 0 0 0-1.562-.644q-.921 0-1.563.643t-.642 1.566.642 1.564q.643.644 1.563.643m-2.042-7.027h6.75l-4.123-5.464h-2.627z" /><path fill="currentColor" fillRule="evenodd" d="M6 25.251c-3.712 1.353-6 3.205-6 5.249C0 34.642 9.402 38 21 38s21-3.358 21-7.5c0-1.216-.81-2.365-2.25-3.381v2.09c.512.545.65.983.65 1.29 0 .383-.212.963-1.082 1.698-.863.728-2.207 1.456-4.007 2.1C31.725 35.576 26.665 36.4 21 36.4s-10.725-.823-14.311-2.104c-1.8-.642-3.144-1.37-4.006-2.1-.87-.734-1.083-1.314-1.083-1.696s.212-.962 1.083-1.697c.748-.632 1.86-1.265 3.317-1.84z" clipRule="evenodd" /></svg>;
const ForwardRef = forwardRef(SvgTruckGeofence);
export default ForwardRef;