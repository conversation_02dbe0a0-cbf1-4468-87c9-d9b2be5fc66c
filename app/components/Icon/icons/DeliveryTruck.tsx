import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgDeliveryTruck = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M280-160q-50 0-85-35t-35-85H60l18-80h113q17-19 40-29.5t49-10.5 49 10.5 40 29.5h167l84-360H182l4-17q6-28 27.5-45.5T264-800h456l-37 160h117l120 160-40 200h-80q0 50-35 85t-85 35-85-35-35-85H400q0 50-35 85t-85 35m357-280h193l4-21-74-99h-95zm-19-273 2-7-84 360 2-7 34-146zM20-427l20-80h220l-20 80zm80-146 20-80h260l-20 80zm180 333q17 0 28.5-11.5T320-280t-11.5-28.5T280-320t-28.5 11.5T240-280t11.5 28.5T280-240m400 0q17 0 28.5-11.5T720-280t-11.5-28.5T680-320t-28.5 11.5T640-280t11.5 28.5T680-240" /></svg>;
const ForwardRef = forwardRef(SvgDeliveryTruck);
export default ForwardRef;