import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgFindInPage = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="m590-160 80 80H240q-33 0-56.5-23.5T160-160v-640q0-33 23.5-56.5T240-880h360l200 240v480q0 20-8.5 36.5T768-96L560-302q-17 11-37 16.5t-43 5.5q-66 0-113-47t-47-113 47-113 113-47 113 47 47 113q0 23-5.5 43T618-360l102 104v-356L562-800H240v640zM480-360q33 0 56.5-23.5T560-440t-23.5-56.5T480-520t-56.5 23.5T400-440t23.5 56.5T480-360m0-80" /></svg>;
const ForwardRef = forwardRef(SvgFindInPage);
export default ForwardRef;