import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgAdcBarGraph = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" data-name="Layer 1" viewBox="0 0 144 144" ref={ref} {...props}><path d="M42.12 123a4 4 0 0 1-4-4V75.15H16.73V119a4 4 0 0 1-8 0V73.28c0-3.5 3.44-6.13 8-6.13h21.39c4.56 0 8 2.63 8 6.13V119a4 4 0 0 1-4 4M87.39 123a4 4 0 0 1-4-4V44.62H62V119a4 4 0 0 1-8 0V44.11a7.77 7.77 0 0 1 8-7.49h21.4a7.77 7.77 0 0 1 8 7.49V119a4 4 0 0 1-4.01 4M132.65 123a4 4 0 0 1-4-4V16.16a1.35 1.35 0 0 0-.22-.79h-21a1.35 1.35 0 0 0-.22.79V119a4 4 0 0 1-8 0V16.16c0-4.85 3.59-8.79 8-8.79h21.39c4.41 0 8 3.94 8 8.79V119a4 4 0 0 1-3.95 4M137.08 137.19H8.76a4 4 0 1 1 0-8h128.32a4 4 0 0 1 0 8" className="cls-1" /></svg>;
const ForwardRef = forwardRef(SvgAdcBarGraph);
export default ForwardRef;