import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgAttLogo = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={1000.265} height={411.08} viewBox="-0.082 -42.217 1000.265 411.08" ref={ref} {...props}><g fill="currentColor"><path d="M925.371 245.707c-2.676 0-4.519-1.855-4.519-4.523V108.655h-45.021c-2.677 0-4.523-1.847-4.523-4.518V85.856c0-2.677 1.847-4.529 4.523-4.529h119.838c2.674 0 4.515 1.854 4.515 4.529v18.28c0 2.669-1.841 4.519-4.515 4.519h-45.018v132.527c0 2.67-1.852 4.525-4.519 4.525zm-344.868-66.166-23.633-67.8-23.845 67.8zm52.416 60.811c1.033 2.678-.615 5.355-3.493 5.355h-21.373c-3.085 0-4.937-1.434-5.968-4.324l-12.122-34.918h-66.386l-12.143 34.918c-1.02 2.891-2.877 4.324-5.954 4.324h-20.137c-2.676 0-4.529-2.678-3.5-5.355l55.702-154.907c1.031-2.886 2.88-4.111 5.957-4.111h27.544c3.086 0 5.142 1.226 6.169 4.111zm159.664-16.84c13.149 0 21.992-6.359 29.188-17.254l-33.293-35.756c-12.75 7.197-20.976 14.379-20.976 28.766.002 14.18 11.507 24.244 25.081 24.244m9.248-120.406c-10.685 0-16.851 6.787-16.851 15.826 0 6.985 3.692 13.15 12.123 22.193 14.592-8.43 20.762-13.564 20.762-22.602 0-8.431-5.342-15.417-16.034-15.417m91.065 136.847c2.669 2.879 1.028 5.754-2.27 5.754h-26.104c-3.493 0-5.343-.826-7.603-3.5l-15.621-17.252c-10.481 13.973-25.087 24.449-49.336 24.449-30.008 0-53.653-18.08-53.653-49.1 0-23.842 12.751-36.584 32.075-47.266-9.463-10.889-13.771-22.396-13.771-32.459 0-25.485 17.881-42.947 44.803-42.947 27.544 0 44.402 16.238 44.402 40.273 0 20.547-14.796 32.043-30.423 40.679l23.025 24.87 12.947-22.61c1.643-2.667 3.494-3.698 6.778-3.698h19.934c3.294 0 5.144 2.266 3.095 5.757l-23.026 39.444zm-205.131 5.754c2.672 0 4.531-1.855 4.531-4.523V108.655h45.008c2.672 0 4.52-1.847 4.52-4.518V85.856c0-2.677-1.848-4.529-4.52-4.529H617.466c-2.676 0-4.522 1.854-4.522 4.529v18.28c0 2.669 1.847 4.519 4.522 4.519h45.008v132.527c0 2.67 1.856 4.525 4.525 4.525z" /><path fill="currentColor" d="M79.446 325.647c34.859 26.984 78.613 43.197 126.084 43.197 51.949 0 99.308-19.287 135.452-50.947.438-.387.222-.643-.21-.387-16.219 10.832-62.445 34.477-135.24 34.477-63.262 0-103.241-14.115-125.818-26.717-.432-.213-.592.111-.268.377m140.041 10.506c50.598 0 106.199-13.793 139.453-41.096 9.1-7.439 17.768-17.34 25.531-30.646 4.469-7.656 8.84-16.752 12.4-25.693.158-.436-.111-.648-.439-.158-30.924 45.508-120.473 73.893-212.937 73.893-65.357 0-135.68-20.9-163.212-60.807-.271-.369-.542-.211-.377.213 25.648 54.514 103.458 84.294 199.581 84.294m-55.283-90.436C58.97 245.717 9.35 196.705.349 163.258c-.111-.484-.43-.378-.43.057 0 11.26 1.127 25.791 3.066 35.436.925 4.695 4.746 12.063 10.348 17.936 25.482 26.561 89.012 63.779 199.036 63.779 149.903 0 184.178-49.934 191.177-66.355 5.005-11.744 7.598-32.967 7.598-50.795 0-4.314-.108-7.76-.271-11.143 0-.549-.318-.594-.428-.059-7.491 40.181-135.566 93.603-246.241 93.603M19.741 75.143C13.711 87.11 7.027 107.299 5.04 117.748c-.871 4.477-.5 6.627 1.07 9.968 12.613 26.761 76.412 69.579 225.23 69.579 90.79 0 161.318-22.305 172.744-63.008 2.104-7.493 2.217-15.404-.486-26.064-3.02-11.912-8.676-25.803-13.463-35.557-.158-.318-.437-.271-.38.105 1.778 53.386-147.099 87.793-222.216 87.793-81.365 0-149.246-32.418-149.246-73.352 0-3.933.814-7.867 1.83-11.961.102-.374-.218-.436-.382-.108M341.315 9.596c.864 1.354 1.295 2.799 1.295 4.744 0 22.836-69.891 63.234-181.148 63.234-81.748 0-97.053-30.326-97.053-49.612 0-6.894 2.644-13.948 8.467-21.112.318-.426.048-.59-.319-.273a206 206 0 0 0-28.938 30.059c-4.09 5.17-6.629 9.75-6.629 12.494 0 39.967 100.216 68.945 193.921 68.945 99.844 0 144.404-32.594 144.404-61.238 0-10.237-3.985-16.213-14.179-27.799-6.617-7.537-12.876-13.674-19.501-19.715-.32-.264-.543-.049-.32.273m-30.609-22.831c-30.814-18.47-66.597-28.978-105.174-28.978-38.846 0-75.707 10.875-106.632 29.834C89.624-6.67 84.403-2.095 84.403 3.786c0 17.336 40.515 35.976 112.394 35.976 71.133 0 126.305-20.417 126.305-40.07 0-4.691-4.1-7.973-12.396-12.927" /></g></svg>;
const ForwardRef = forwardRef(SvgAttLogo);
export default ForwardRef;