import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgFreeReturns = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={47} height={49} fill="none" viewBox="0 0 47 49" ref={ref} {...props}><g fill="currentColor"><path d="M7.231 31.668c0 .4.217.77.568.97l14.769 8.42c.027.015.057.021.085.034q.044.021.092.038c.127.046.258.078.39.078.133 0 .263-.032.39-.078q.047-.017.093-.038c.027-.013.057-.018.084-.034l14.771-8.42c.351-.2.568-.57.568-.97V17.492c0-.03-.01-.059-.012-.089a1 1 0 0 0-.014-.099 1.1 1.1 0 0 0-.125-.367q0-.002-.002-.005l-.002-.003a1.1 1.1 0 0 0-.341-.353c-.026-.017-.046-.04-.073-.055l-14.77-8.419c-.351-.2-.784-.2-1.135 0l-14.77 8.42c-.025.015-.045.036-.07.054a1 1 0 0 0-.083.063 1.1 1.1 0 0 0-.258.29l-.003.003q0 .002-.002.005a1.1 1.1 0 0 0-.125.368q-.01.048-.014.098c-.003.03-.012.059-.012.09zM9.5 19.432l12.502 7.127v11.588L9.499 31.02V19.43zm6.252-4.857 12.501 7.127-5.116 2.917-12.5-7.127zm8.519 23.573V26.559l6.808-3.88.004-.003 5.69-3.243v11.59L24.27 38.15zm-1.135-27.782 12.501 7.126-5.116 2.916-12.501-7.126z" /><path d="M1.092 12.849c.016.09.048.172.085.255.02.041.036.083.06.122a1.326 1.326 0 0 0 .275.3c.04.03.067.07.111.095.039.023.082.024.122.041.042.019.086.03.131.044.093.026.182.046.277.05.013 0 .025.006.038.006q.077 0 .155-.01l7.71-1.051a1.114 1.114 0 0 0 .964-1.255 1.12 1.12 0 0 0-1.273-.95l-5.252.715c6.63-8.93 19.18-11.702 29.107-6.05 10.638 6.058 14.295 19.525 8.153 30.02s-19.748 14.126-30.326 8.102a1.137 1.137 0 0 0-1.542.408 1.106 1.106 0 0 0 .414 1.52 24.44 24.44 0 0 0 12.128 3.227c2.146 0 4.307-.282 6.436-.853 6.3-1.69 11.572-5.699 14.845-11.291 6.764-11.558 2.736-26.39-8.98-33.062C23.905-2.934 10.26.01 2.907 9.612L2.246 4.89a1.12 1.12 0 0 0-1.272-.95A1.113 1.113 0 0 0 .01 5.196L1.076 12.8c.003.018.013.031.016.049" /></g></svg>;
const ForwardRef = forwardRef(SvgFreeReturns);
export default ForwardRef;