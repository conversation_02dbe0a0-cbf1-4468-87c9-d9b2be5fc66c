import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgWorkspacePremium = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="m387-412 35-114-92-74h114l36-112 36 112h114l-93 74 35 114-92-71zM240-40v-309q-38-42-59-96t-21-115q0-134 93-227t227-93 227 93 93 227q0 61-21 115t-59 96v309l-240-80zm240-280q100 0 170-70t70-170-70-170-170-70-170 70-70 170 70 170 170 70M320-159l160-41 160 41v-124q-35 20-75.5 31.5T480-240t-84.5-11.5T320-283zm160-62" /></svg>;
const ForwardRef = forwardRef(SvgWorkspacePremium);
export default ForwardRef;