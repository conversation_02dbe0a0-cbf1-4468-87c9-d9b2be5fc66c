import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgHeadsetMic = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M480-40v-80h280v-40H600v-320h160v-40q0-116-82-198t-198-82-198 82-82 198v40h160v320H200q-33 0-56.5-23.5T120-240v-280q0-74 28.5-139.5T226-774t114.5-77.5T480-880t139.5 28.5T734-774t77.5 114.5T840-520v400q0 33-23.5 56.5T760-40zM200-240h80v-160h-80zm480 0h80v-160h-80zM200-400h80zm480 0h80z" /></svg>;
const ForwardRef = forwardRef(SvgHeadsetMic);
export default ForwardRef;