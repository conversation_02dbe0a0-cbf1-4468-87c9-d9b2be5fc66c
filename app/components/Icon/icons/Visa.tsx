import * as React from 'react';
import type { SVGProps } from 'react';
import { Ref, forwardRef } from 'react';
const SvgVisa = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={48} height={32} fill="none" viewBox="0 0 48 32" ref={ref} {...props}>
    <rect width={47} height={31} x={0.5} y={0.5} fill="currentColor" stroke="currentColor" rx={3.5} />
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M14.572 21.677h-2.908l-2.18-8.087c-.104-.373-.323-.701-.647-.857a9.7 9.7 0 0 0-2.665-.855v-.312h4.684a1.21 1.21 0 0 1 1.212 1.011l1.132 5.834 2.906-6.845h2.827zm5.978 0h-2.747l2.262-10.11h2.746zm5.814-7.31c.08-.544.566-.856 1.131-.856.89-.078 1.858.079 2.666.467l.485-2.177a7.1 7.1 0 0 0-2.504-.468c-2.666 0-4.605 1.4-4.605 3.344 0 1.48 1.374 2.256 2.344 2.723 1.049.467 1.453.778 1.372 1.244 0 .7-.808 1.011-1.615 1.011-.97 0-1.94-.233-2.827-.622l-.485 2.178c.97.388 2.019.545 2.989.545 2.988.076 4.846-1.323 4.846-3.423 0-2.645-3.797-2.8-3.797-3.966m13.408 7.31-2.18-10.11h-2.343c-.485 0-.97.31-1.131.777l-4.038 9.333h2.827l.564-1.477h3.474l.323 1.477zm-4.119-7.388.807 3.81h-2.261z"
      clipRule="evenodd"
    />
  </svg>
);
const ForwardRef = forwardRef(SvgVisa);
export default ForwardRef;
