import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const Svg4G = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M120-120q-33 0-56.5-23.5T40-200v-560q0-33 23.5-56.5T120-840h720q33 0 56.5 23.5T920-760v560q0 33-23.5 56.5T840-120zm0-80h720v-560H120zm0 0v-560zm680-320H660v80h60v80H600v-240h200q0-33-23.5-56.5T720-680H600q-33 0-56.5 23.5T520-600v240q0 33 23.5 56.5T600-280h120q33 0 56.5-23.5T800-360zM320-280h80v-120h80v-80h-80v-200h-80v200h-80v-200h-80v280h160z" /></svg>;
const ForwardRef = forwardRef(Svg4G);
export default ForwardRef;