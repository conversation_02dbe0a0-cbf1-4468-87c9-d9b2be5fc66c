import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgArrowTopLeft = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" viewBox="0 0 24 24" ref={ref} {...props}><path fill="currentColor" d="M17.6 18 8 8.4V17H6V5h12v2H9.4l9.6 9.6z" /></svg>;
const ForwardRef = forwardRef(SvgArrowTopLeft);
export default ForwardRef;