import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgPdf = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M360-460h40v-80h40q17 0 28.5-11.5T480-580v-40q0-17-11.5-28.5T440-660h-80zm40-120v-40h40v40zm120 120h80q17 0 28.5-11.5T640-500v-120q0-17-11.5-28.5T600-660h-80zm40-40v-120h40v120zm120 40h40v-80h40v-40h-40v-40h40v-40h-80zM320-240q-33 0-56.5-23.5T240-320v-480q0-33 23.5-56.5T320-880h480q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240zm0-80h480v-480H320zM160-80q-33 0-56.5-23.5T80-160v-560h80v560h560v80zm160-720v480z" /></svg>;
const ForwardRef = forwardRef(SvgPdf);
export default ForwardRef;