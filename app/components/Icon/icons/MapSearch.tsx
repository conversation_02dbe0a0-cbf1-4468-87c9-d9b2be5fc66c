import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgMapSearch = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={43} height={43} fill="none" viewBox="0 0 43 43" ref={ref} {...props}><path fill="currentColor" d="M8.139 34.222q-.75.36-1.444-.09Q6 33.68 6 32.833V9.555q0-.54.313-.958.312-.417.826-.625L15.695 5l10.638 3.722 7.528-2.972q.75-.333 1.444.104.696.438.695 1.285v15.458a8.7 8.7 0 0 0-1.215-1.652 9.2 9.2 0 0 0-1.563-1.334V8.89l-5.61 2.139v7q-.737.056-1.432.187a9 9 0 0 0-1.347.368v-7.555l-7.666-2.611V30.68zm.639-3.139 5.61-2.138V8.417l-5.61 1.86zm19.277-.305q1.528 0 2.563-1t1.049-2.611q.014-1.529-1.035-2.57-1.05-1.041-2.577-1.042-1.527 0-2.569 1.042-1.041 1.041-1.041 2.57 0 1.527 1.041 2.57 1.042 1.04 2.57 1.04m0 2.777q-2.638 0-4.513-1.875t-1.875-4.513q0-2.667 1.875-4.528t4.513-1.861q2.667 0 4.528 1.86 1.862 1.862 1.862 4.529 0 .958-.258 1.826a6.2 6.2 0 0 1-.742 1.618l4.222 4.222-1.834 1.834-4.222-4.195a6 6 0 0 1-1.66.813q-.882.27-1.896.27" /></svg>;
const ForwardRef = forwardRef(SvgMapSearch);
export default ForwardRef;