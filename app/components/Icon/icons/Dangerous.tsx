import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgDangerous = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M330-120 120-330v-300l210-210h300l210 210v300L630-120zm36-190 114-114 114 114 56-56-114-114 114-114-56-56-114 114-114-114-56 56 114 114-114 114zm-2 110h232l164-164v-232L596-760H364L200-596v232zm116-280" /></svg>;
const ForwardRef = forwardRef(SvgDangerous);
export default ForwardRef;