import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgThunderstorm = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="m300-40 36-100h-76l50-140h100l-43 100h83L340-40zm270-40 28-80h-78l43-120h100l-35 80h82L610-80zM300-320q-91 0-155.5-64.5T80-540q0-83 55-145t136-73q32-57 87.5-89.5T480-880q90 0 156.5 57.5T717-679q69 6 116 57t47 122q0 75-52.5 127.5T700-320zm0-80h400q42 0 71-29t29-71-29-71-71-29h-60v-40q0-66-47-113t-113-47q-48 0-87.5 26T333-704l-10 24h-25q-57 2-97.5 42.5T160-540q0 58 41 99t99 41m180-200" /></svg>;
const ForwardRef = forwardRef(SvgThunderstorm);
export default ForwardRef;