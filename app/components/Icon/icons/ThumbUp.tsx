import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgThumbUp = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={45} height={42} fill="none" viewBox="0 0 45 42" ref={ref} {...props}><path fill="currentColor" d="M1.098 42H10.3c.607 0 1.099-.496 1.099-1.106v-1.797h3.32c2.22 0 3.35.599 4.662 1.292 1.427.755 3.045 1.61 6.04 1.61h14.547c2.195 0 3.98-1.797 3.98-4.007a4 4 0 0 0-.998-2.652 4.01 4.01 0 0 0 1.524-3.15 4 4 0 0 0-.998-2.652A4.01 4.01 0 0 0 45 26.388a4 4 0 0 0-1.524-3.15 4 4 0 0 0 .998-2.653c0-2.21-1.785-4.007-3.98-4.007h-8.51v-7.26C31.984 2.88 27.909 0 23.87 0c-.607 0-1.098.495-1.098 1.105v1.478c0 10.25-3.63 15.257-11.374 15.683v-1.754c0-.61-.492-1.105-1.099-1.105H1.098c-.606 0-1.098.495-1.098 1.105v24.383C0 41.505.492 42 1.098 42M24.97 2.582v-.28c2.962.516 4.818 3.153 4.818 7.012v8.366c0 .61.492 1.105 1.098 1.105h9.608c.984 0 1.785.806 1.785 1.797s-.8 1.796-1.785 1.796c-.607 0-1.098.494-1.098 1.105 0 .61.491 1.106 1.098 1.106h.526c.984 0 1.784.805 1.784 1.796 0 .99-.8 1.796-1.784 1.796h-.526c-.607 0-1.098.495-1.098 1.105 0 .611.491 1.106 1.098 1.106.984 0 1.785.806 1.785 1.796s-.8 1.796-1.785 1.796h-.525c-.607 0-1.099.495-1.099 1.105 0 .611.492 1.106 1.099 1.106.984 0 1.784.806 1.784 1.796s-.8 1.796-1.784 1.796H25.42c-2.453 0-3.7-.659-5.018-1.356-1.37-.724-2.923-1.545-5.684-1.545h-3.32V20.49c9.006-.444 13.57-6.463 13.57-17.908M2.197 17.617h7.005v22.171H2.197z" /></svg>;
const ForwardRef = forwardRef(SvgThumbUp);
export default ForwardRef;