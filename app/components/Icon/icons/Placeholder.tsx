import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgPlaceholder = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" viewBox="0 0 24 24" ref={ref} {...props}><path fill="currentColor" d="M15.2 7.2 12 4 8.8 7.2l3.2 3.2zM4 12l3.2 3.2 3.2-3.2-3.2-3.2zM20 12l-3.2-3.2-3.2 3.2 3.2 3.2zM12 20l-3.2-3.2 3.2-3.2 3.2 3.2z" /></svg>;
const ForwardRef = forwardRef(SvgPlaceholder);
export default ForwardRef;