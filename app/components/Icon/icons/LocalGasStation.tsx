import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgLocalGasStation = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M160-120v-640q0-33 23.5-56.5T240-840h240q33 0 56.5 23.5T560-760v280h40q33 0 56.5 23.5T680-400v180q0 17 11.5 28.5T720-180t28.5-11.5T760-220v-288q-9 5-19 6.5t-21 1.5q-42 0-71-29t-29-71q0-32 17.5-57.5T684-694l-84-84 42-42 148 144q15 15 22.5 35t7.5 41v380q0 42-29 71t-71 29-71-29-29-71v-200h-60v300zm80-440h240v-200H240zm480 0q17 0 28.5-11.5T760-600t-11.5-28.5T720-640t-28.5 11.5T680-600t11.5 28.5T720-560M240-200h240v-280H240zm240 0H240z" /></svg>;
const ForwardRef = forwardRef(SvgLocalGasStation);
export default ForwardRef;