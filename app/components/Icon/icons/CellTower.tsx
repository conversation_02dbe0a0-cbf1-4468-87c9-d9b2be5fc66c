import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgCellTower = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M196-276q-57-60-86.5-133T80-560t29.5-151T196-844l48 48q-48 48-72 110.5T148-560t24 125.5T244-324zm96-96q-39-39-59.5-88T212-560t20.5-100 59.5-88l48 48q-30 27-45 64t-15 76q0 36 15 73t45 67zM280-80l135-405q-16-14-25.5-33t-9.5-42q0-42 29-71t71-29 71 29 29 71q0 23-9.5 42T545-485L680-80h-80l-26-80H387l-27 80zm133-160h134l-67-200zm255-132-48-48q30-27 45-64t15-76q0-36-15-73t-45-67l48-48q39 39 58 88t22 100q0 51-20.5 100T668-372m96 96-48-48q48-48 72-110.5T812-560t-24-125.5T716-796l48-48q57 60 86.5 133T880-560t-28 151-88 133" /></svg>;
const ForwardRef = forwardRef(SvgCellTower);
export default ForwardRef;