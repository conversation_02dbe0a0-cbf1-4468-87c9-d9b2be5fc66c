import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgDescription = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M320-240h320v-80H320zm0-160h320v-80H320zM240-80q-33 0-56.5-23.5T160-160v-640q0-33 23.5-56.5T240-880h320l240 240v480q0 33-23.5 56.5T720-80zm280-520v-200H240v640h480v-440zM240-800v200zv640z" /></svg>;
const ForwardRef = forwardRef(SvgDescription);
export default ForwardRef;