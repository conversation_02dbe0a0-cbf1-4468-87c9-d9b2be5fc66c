import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgChatConversation = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={39} height={29} fill="none" viewBox="0 0 39 29" ref={ref} {...props}><g fill="currentColor" clipPath="url(#a)"><path d="M36.058 6.372h-8.144v9.605c0 2.02-1.698 3.662-3.785 3.662h-11.88v2.4c0 1.572 1.317 2.846 2.942 2.846H30.3l4.672 4.054c.134.13.364.038.364-.147v-3.907h.722c1.625 0 2.941-1.275 2.941-2.846V9.218c0-1.572-1.317-2.846-2.941-2.846" /><path d="M23.81 0H2.941C1.317 0 0 1.274 0 2.846v12.821c0 1.572 1.317 2.846 2.942 2.846h.721v3.908c0 .184.23.276.365.146l5.47-4.054h14.31c1.626 0 2.942-1.274 2.942-2.846V2.846C26.75 1.274 25.433 0 23.81 0M7.945 11.42c-.905 0-1.639-.71-1.639-1.585 0-.876.734-1.586 1.64-1.586.904 0 1.638.71 1.638 1.586 0 .875-.734 1.585-1.639 1.585m5.423 0c-.905 0-1.639-.71-1.639-1.585 0-.876.734-1.586 1.639-1.586s1.639.71 1.639 1.586c0 .875-.734 1.585-1.64 1.585m5.422 0c-.904 0-1.638-.71-1.638-1.585 0-.876.734-1.586 1.639-1.586.904 0 1.638.71 1.638 1.586 0 .875-.733 1.585-1.639 1.585" /></g><defs><clipPath id="a"><path fill="currentColor" d="M0 0h39v29H0z" /></clipPath></defs></svg>;
const ForwardRef = forwardRef(SvgChatConversation);
export default ForwardRef;