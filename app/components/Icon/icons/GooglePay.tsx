import * as React from 'react';
import type { SVGProps } from 'react';
import { Ref, forwardRef } from 'react';
const SvgGooglePay = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={35} height={14} fill="none" viewBox="0 0 35 14" ref={ref} {...props}>
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M16.199 11.01V6.984h2.094q1.287-.002 2.172-.848l.142-.143a2.885 2.885 0 0 0-.142-4.06 3 3 0 0 0-2.172-.864h-3.361v9.943zm0-5.248V2.289h2.126c.456 0 .89.174 1.212.492.685.666.7 1.776.04 2.466-.323.34-.78.53-1.252.515zm10.32-1.023q-.816-.751-2.22-.753c-1.205 0-2.11.444-2.708 1.324l1.117.706q.615-.904 1.677-.904c.449 0 .882.166 1.22.468.33.285.52.697.52 1.133v.294c-.488-.27-1.102-.412-1.858-.412q-1.325-.002-2.117.626c-.528.42-.795.975-.795 1.68a2.11 2.11 0 0 0 .747 1.666c.496.444 1.126.666 1.866.666.874 0 1.566-.389 2.094-1.166h.055v.944h1.212V6.817c0-.88-.268-1.578-.81-2.078m-3.44 5.02c-.26-.191-.418-.5-.418-.833 0-.373.174-.682.512-.928.347-.246.78-.373 1.291-.373q1.064-.01 1.653.468-.002.81-.63 1.403c-.377.381-.889.595-1.424.595-.355.008-.7-.11-.984-.333M30.052 14l4.235-9.792H32.91l-1.96 4.884h-.024l-2.007-4.884H27.54l2.779 6.367L28.746 14z"
      clipRule="evenodd"
    />
    <path
      fill="currentColor"
      d="M11.824 6.11a7 7 0 0 0-.095-1.157H6.385V7.15h3.062a2.65 2.65 0 0 1-1.134 1.737v1.427h1.826c1.07-.991 1.685-2.458 1.685-4.202"
    />
    <path
      fill="currentColor"
      d="M6.383 11.693c1.527 0 2.818-.508 3.755-1.38L8.312 8.886c-.512.349-1.165.547-1.929.547-1.48 0-2.73-1.007-3.18-2.355h-1.88v1.475a5.66 5.66 0 0 0 5.06 3.14"
    />
    <path fill="currentColor" d="M3.205 7.078a3.47 3.47 0 0 1 0-2.188V3.423H1.323a5.7 5.7 0 0 0 0 5.122z" />
    <path
      fill="currentColor"
      d="M6.383 2.535c.811-.016 1.59.293 2.173.856l1.621-1.634A5.45 5.45 0 0 0 6.383.275a5.66 5.66 0 0 0-5.06 3.148l1.88 1.474c.45-1.355 1.7-2.362 3.18-2.362"
    />
  </svg>
);
const ForwardRef = forwardRef(SvgGooglePay);
export default ForwardRef;
