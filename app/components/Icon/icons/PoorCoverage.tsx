import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgPoorCoverage = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="m80-80 800-800v240h-80v-47L273-160h447v80zm760 0q-17 0-28.5-11.5T800-120t11.5-28.5T840-160t28.5 11.5T880-120t-11.5 28.5T840-80m-40-160v-320h80v320z" /></svg>;
const ForwardRef = forwardRef(SvgPoorCoverage);
export default ForwardRef;