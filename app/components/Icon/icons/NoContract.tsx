import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgNoContract = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={46} height={52} fill="none" viewBox="0 0 46 52" ref={ref} {...props}><g stroke="currentColor" strokeWidth={0.2}><path d="M33.21 29.169c-5.53 0-10.028 4.499-10.028 10.028 0 5.528 4.498 10.027 10.027 10.027 5.53 0 10.028-4.499 10.028-10.027 0-5.53-4.499-10.028-10.028-10.028Zm4.587 13.358-1.257 1.257-3.33-3.33-3.332 3.33-1.256-1.257 3.33-3.33-3.33-3.331 1.256-1.257 3.318 3.317s.022.01.026 0l3.318-3.317 1.257 1.257-3.322 3.322s-.005.013 0 .017z" /><path fill="currentColor" d="M31.79 22.094a.89.89 0 0 0-.887-.888H7.81a.89.89 0 0 0-.888.888c0 .489.4.888.888.888h23.093a.89.89 0 0 0 .888-.888ZM25.707 27.907c.408-.267.346-.484-.142-.484H7.81a.89.89 0 0 0-.888.888c0 .488.4.888.888.888h15.33c.488 0 1.2-.253 1.576-.564l.995-.728zM6.922 34.529c0 .488.4.888.888.888h8.722a.89.89 0 0 0 .888-.888.89.89 0 0 0-.888-.888H7.81a.89.89 0 0 0-.888.888Z" /><path fill="currentColor" d="M3.665 49.224a.89.89 0 0 1-.889-.888V3.665c0-.489.4-.889.889-.889h23.092c.489 0 .888.4.888.889v6.505c0 .489.4.889.889.889h6.505c.489 0 .889.4.889.888v13.056c0 .488.4.915.888.942a.83.83 0 0 0 .888-.835V10.69c0-.489-.284-1.172-.626-1.514l-7.55-7.55C29.182 1.28 28.502 1 28.014 1H1.888A.89.89 0 0 0 1 1.888v48.224c0 .488.4.888.888.888h23.728c.488 0 .55-.218.142-.48l-1.013-.732c-.377-.307-1.088-.56-1.576-.56H3.665zm25.61-44.17c0-.488.284-.603.626-.261l3.997 3.996c.346.347.227.626-.262.626h-3.473a.89.89 0 0 1-.888-.888z" /><path fill="currentColor" d="M14.025 14.99a.89.89 0 0 0-.888.887c0 .489.4.889.888.889h10.658a.89.89 0 0 0 .888-.889.89.89 0 0 0-.888-.888zM33.204 27.392c-6.51 0-11.804 5.293-11.804 11.804 0 6.51 5.294 11.803 11.804 11.803s11.804-5.293 11.804-11.803-5.293-11.804-11.804-11.804Zm0 21.831c-5.529 0-10.027-4.499-10.027-10.027 0-5.53 4.498-10.028 10.027-10.028 5.53 0 10.028 4.499 10.028 10.028 0 5.528-4.499 10.027-10.028 10.027Z" /><path fill="currentColor" d="M37.16 36.492a.89.89 0 0 0-1.257-1.257l-2.06 2.061c-.346.346-.63.63-.639.63s-.302-.288-.648-.63l-2.06-2.06a.89.89 0 0 0-1.258 1.256l2.074 2.074c.347.346.626.63.63.63s-.283.285-.63.631l-2.074 2.074a.89.89 0 0 0 1.257 1.257l2.074-2.074.626-.626.626.626 2.074 2.074a.89.89 0 0 0 1.257-1.257l-2.07-2.07c-.346-.346-.63-.63-.63-.634s.284-.293.63-.64l2.07-2.07z" /></g></svg>;
const ForwardRef = forwardRef(SvgNoContract);
export default ForwardRef;