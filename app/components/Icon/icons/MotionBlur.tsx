import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgMotionBlur = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M280-280v-80h240q-14-17-22.5-37T484-440H360v-80h124q5-23 13.5-43t22.5-37H120v-80h560q83 0 141.5 58.5T880-480t-58.5 141.5T680-280zm400-80q50 0 85-35t35-85-35-85-85-35-85 35-35 85 35 85 85 35M80-440v-80h240v80zm40 160v-80h120v80z" /></svg>;
const ForwardRef = forwardRef(SvgMotionBlur);
export default ForwardRef;