import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgLowCoverage = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="m80-80 800-800v800zm320-80h400v-526L400-286z" /></svg>;
const ForwardRef = forwardRef(SvgLowCoverage);
export default ForwardRef;