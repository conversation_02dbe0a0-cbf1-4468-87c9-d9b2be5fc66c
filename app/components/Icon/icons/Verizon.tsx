import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgVerizon = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" xmlSpace="preserve" id="Layer_1" x={0} y={0} viewBox="0 0 796.918 326.657" ref={ref} {...props}><style>{".st0{fill:#f50a23}"}</style><path d="M606.78 229.306h29.661v-54.187c0-12.36 7.225-21.107 17.872-21.107 10.267 0 15.591 7.227 15.591 17.495v57.8h29.661v-63.503c0-21.488-12.928-36.699-33.844-36.699-13.308 0-22.626 5.705-29.661 16.162h-.571v-13.309h-28.71v97.348zm-58.182-100.011c-30.993 0-51.716 22.246-51.716 51.527 0 29.092 20.724 51.528 51.716 51.528s51.716-22.436 51.716-51.528c0-29.282-20.724-51.527-51.716-51.527m-.19 81.188c-14.072 0-21.485-11.787-21.485-29.661 0-18.063 7.413-29.662 21.485-29.662 14.069 0 21.865 11.598 21.865 29.662 0 17.874-7.796 29.661-21.865 29.661M412.46 229.306h83.28v-23.385h-47.153v-.573l45.062-50.576v-22.814H412.46v23.385h45.633v.571L412.46 206.87zm-38.031 0h29.851v-97.348h-29.851zm-69.21 0h29.661v-44.68c0-20.346 12.169-29.283 30.802-26.81h.571v-25.668c-1.522-.571-3.232-.761-6.084-.761-11.598 0-19.394 5.323-26.05 16.731h-.571v-16.16h-28.329zm-54.568-18.631c-13.12 0-21.105-8.559-22.627-21.675h70.16c.19-20.918-5.325-37.65-16.923-48.107-8.174-7.605-19.014-11.789-32.703-11.789-29.279 0-49.625 22.246-49.625 51.337 0 29.28 19.394 51.716 51.527 51.716 12.169 0 21.865-3.232 29.851-8.747 8.557-5.891 14.64-14.45 16.35-23.005h-28.519c-2.851 6.465-8.746 10.27-17.491 10.27m-1.522-60.276c10.457 0 17.682 7.798 18.823 19.205h-39.738c2.283-11.6 8.556-19.205 20.915-19.205m125.3-53.049h29.851v26.43h-29.851z" className="st0" /><radialGradient id="SVGID_1_" cx={-1103.699} cy={2428.092} r={354.369} fx={-1190.054} fy={2412.94} gradientTransform="matrix(.0495 -.1371 .1483 .0536 -145.643 -68.868)" gradientUnits="userSpaceOnUse"><stop offset={0} style={{
      stopColor: "#f5ff1e"
    }} /><stop offset={0.257} style={{
      stopColor: "#f5ff1e"
    }} /><stop offset={0.266} style={{
      stopColor: "#f5fa1e"
    }} /><stop offset={0.423} style={{
      stopColor: "#f5a620"
    }} /><stop offset={0.562} style={{
      stopColor: "#f56321"
    }} /><stop offset={0.678} style={{
      stopColor: "#f53322"
    }} /><stop offset={0.766} style={{
      stopColor: "#f51523"
    }} /><stop offset={0.816} style={{
      stopColor: "#f50a23"
    }} /></radialGradient><path d="m164.201 228.095-36.908-96.137H97.35l36.908 96.137c.28.733.983 1.213 1.765 1.213h29.943c-.782 0-1.485-.48-1.765-1.213" style={{
    fill: "url(#SVGID_1_)"
  }} /><path d="m137.787 228.095 36.908-96.137h29.943l-36.908 96.137a1.89 1.89 0 0 1-1.765 1.213h-29.943c.783 0 1.486-.48 1.765-1.213" className="st0" /></svg>;
const ForwardRef = forwardRef(SvgVerizon);
export default ForwardRef;