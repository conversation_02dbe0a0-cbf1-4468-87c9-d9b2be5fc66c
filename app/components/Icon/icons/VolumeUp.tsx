import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgVolumeUp = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M560-131v-82q90-26 145-100t55-168-55-168-145-100v-82q124 28 202 125.5T840-481t-78 224.5T560-131M120-360v-240h160l200-200v640L280-360zm440 40v-322q47 22 73.5 66t26.5 96q0 51-26.5 94.5T560-320M400-606l-86 86H200v80h114l86 86zM300-480" /></svg>;
const ForwardRef = forwardRef(SvgVolumeUp);
export default ForwardRef;