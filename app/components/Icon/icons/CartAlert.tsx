import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgCartAlert = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={34} height={34} fill="none" viewBox="0 0 34 34" ref={ref} {...props}><path fill="currentColor" d="M2.19 28.163v-9.077.04-.018zm21.744-2.333q.95 0 1.59-.643t.64-1.554q0-.954-.649-1.594a2.15 2.15 0 0 0-1.566-.641q-.96 0-1.61.638t-.65 1.582.648 1.578q.648.634 1.597.634m-17.164 0q.948 0 1.59-.643.64-.643.64-1.554 0-.954-.65-1.594a2.15 2.15 0 0 0-1.565-.641q-.96 0-1.61.638t-.65 1.582q0 .944.648 1.578t1.597.634m19.35-10.215q-3.294 0-5.59-2.287T18.234 7.76q-.001-3.218 2.296-5.488Q22.828 0 26.13 0q3.292 0 5.58 2.268Q34 4.535 34 7.802q0 3.261-2.302 5.537-2.302 2.277-5.578 2.276m-.679-6.613h1.47v-5.84h-1.47zm.735 3.536a.86.86 0 0 0 .628-.261.92.92 0 0 0 .282-.63.84.84 0 0 0-.276-.636.9.9 0 0 0-.63-.258q-.423.04-.659.284a.84.84 0 0 0-.237.611q0 .368.247.629t.645.261M14.809 9.385H5.67l-2.615 7.532h16.383q1.313.975 2.904 1.547 1.59.57 3.367.622H2.191v9.077H28.48v-9.172a4.7 4.7 0 0 0 1.116-.244q.556-.194 1.074-.451v14.63q0 .474-.318.774t-.788.3h-.6q-.499 0-.8-.3-.3-.3-.3-.774v-2.593H2.806v2.593q0 .474-.319.774-.318.3-.787.3h-.582q-.485 0-.801-.3A1.02 1.02 0 0 1 0 32.926V19.083L3.734 8.279q.19-.487.618-.78t.968-.293h9.499l-.01.538v1.641" /></svg>;
const ForwardRef = forwardRef(SvgCartAlert);
export default ForwardRef;