import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgPlay = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={10} height={12} fill="none" viewBox="0 0 10 12" ref={ref} {...props}><path fill="currentColor" d="M.188 11.838V.438l9 5.7z" /></svg>;
const ForwardRef = forwardRef(SvgPlay);
export default ForwardRef;