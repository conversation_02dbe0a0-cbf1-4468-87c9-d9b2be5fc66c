import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgEditorChoice = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M240-40v-329L110-580l185-300h370l185 300-130 211v329l-240-80zm80-111 160-53 160 53v-129H320zm20-649L204-580l136 220h280l136-220-136-220zm98 383L296-558l57-57 85 85 169-170 57 56zM320-280h320z" /></svg>;
const ForwardRef = forwardRef(SvgEditorChoice);
export default ForwardRef;