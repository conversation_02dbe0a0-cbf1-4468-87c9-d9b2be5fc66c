import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgCurrencyExchange = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="currentColor" viewBox="0 -960 960 960" ref={ref} {...props}><path d="M480-40q-112 0-206-51T120-227v107H40v-240h240v80h-99q48 72 126.5 116T480-120q75 0 140.5-28.5t114-77 77-114T840-480h80q0 91-34.5 171T791-169 651-74.5 480-40m-36-160v-52q-47-11-76.5-40.5T324-370l66-26q12 41 37.5 61.5T486-314t56.5-15.5T566-378q0-29-24.5-47T454-466q-59-21-86.5-50T340-592q0-41 28.5-74.5T446-710v-50h70v50q36 3 65.5 29t40.5 61l-64 26q-8-23-26-38.5T482-648q-35 0-53.5 15T410-592t23 41 83 35q72 26 96 61t24 77q0 29-10 51t-26.5 37.5-38.5 25-47 14.5v50zM40-480q0-91 34.5-171T169-791t140-94.5T480-920q112 0 206 51t154 136v-107h80v240H680v-80h99q-48-72-126.5-116T480-840q-75 0-140.5 28.5t-114 77-77 114T120-480z" /></svg>;
const ForwardRef = forwardRef(SvgCurrencyExchange);
export default ForwardRef;