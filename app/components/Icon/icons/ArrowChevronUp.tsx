import * as React from "react";
import type { SVGProps } from "react";
import { Ref, forwardRef } from "react";
const SvgArrowChevronUp = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" viewBox="0 0 24 24" ref={ref} {...props}><path fill="currentColor" d="m7.4 15.375-1.4-1.4 6-6 6 6-1.4 1.4-4.6-4.6z" /></svg>;
const ForwardRef = forwardRef(SvgArrowChevronUp);
export default ForwardRef;