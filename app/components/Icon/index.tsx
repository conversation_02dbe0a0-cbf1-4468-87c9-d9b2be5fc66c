// import classNames from 'classnames';
// import React, { Suspense, useEffect, useState, useTransition } from 'react';
// import { icons } from './icons';
// import { IconProps } from './props';

// const Icon = React.forwardRef<HTMLDivElement, IconProps>(({ icon, className, style }: IconProps, ref) => {
//   const [SvgIcon, setSvgIcon] = useState<(typeof icons)[keyof typeof icons]>();
//   const [transitioning, startTransition] = useTransition();
//   useEffect(() => startTransition(() => setSvgIcon(icons[icon])), [icon]);

//   if (!SvgIcon || transitioning) return null;

//   return (
//     <div
//       ref={ref}
//       style={style}
//       className={classNames('items-center justify-center', className)}
//       aria-label={icon}
//       role="img"
//     >
//       <Suspense fallback={null}>
//         <SvgIcon style={{ width: '100%', height: '100%' }} />
//       </Suspense>
//     </div>
//   );
// });

// export default Icon;

import classNames from 'classnames';
import React from 'react';
import { icons } from './icons';
import { IconProps } from './props';

const Icon = React.forwardRef<HTMLDivElement, IconProps>(({ icon, className, style }: IconProps, ref) => {
  const SvgIcon = icons[icon];

  if (!SvgIcon) return null;

  return (
    <div
      ref={ref}
      style={style}
      className={classNames('items-center justify-center', className)}
      aria-label={icon}
      role="img"
    >
      <SvgIcon style={{ width: '100%', height: '100%' }} />
    </div>
  );
});

export default Icon;
