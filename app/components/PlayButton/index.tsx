import { cn } from '@/lib/utils';
import Icon from '../Icon';
import { Button } from '../ui/button';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';

interface PlayButtonProps {
  onClick: () => void;
  show?: boolean;
}

export default function PlayButton({ onClick, show = true }: PlayButtonProps) {
  const isMobile = useIsMobileBreakpoint();
  return (
    <div
      className={cn(
        'absolute inset-0 flex h-full w-full items-center justify-center',
        'transition-opacity duration-300',
        show ? 'opacity-100' : 'opacity-0',
      )}
      onClick={e => {
        e.stopPropagation();
        onClick();
      }}
      onKeyDown={e => e.key === 'Enter' && onClick()}
      role="button"
      tabIndex={0}
    >
      <Button variant="primary" size={isMobile ? 'icon-md' : 'icon-lg'} role="button" tabIndex={0}>
        {/* -mr-0.5 is to overcome a weird optical illusion where the play icon looks like it's too far to the left */}
        <Icon icon="play" className="-mr-0.5" />
      </Button>
    </div>
  );
}
