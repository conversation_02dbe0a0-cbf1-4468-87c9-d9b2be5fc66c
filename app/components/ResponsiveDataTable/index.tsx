import { Fragment, ReactNode } from 'react';
import { Separator } from '../ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

export type LabeledDataRow = { [label: string]: ReactNode };

function MobileView({
  rows,
  labelClassNames,
}: {
  rows: LabeledDataRow[];
  labelClassNames?: { [label: string]: string };
}) {
  return (
    <div className="space-y-4">
      {rows.map((row, index) => (
        <Fragment key={index}>
          {index > 0 && <Separator orientation="horizontal" />}
          <div className="relative space-y-1.5 px-4 py-4">
            {Object.entries(row).map(([label, value]) => (
              <div key={label} className="flex items-center justify-between">
                <span className={cn('text-xs font-bold sm:text-sm', labelClassNames?.all, labelClassNames?.[label])}>
                  {label}
                </span>
                <span className="text-right text-xs text-muted-foreground">{value || '-'}</span>
              </div>
            ))}
          </div>
        </Fragment>
      ))}
    </div>
  );
}

function TableView({
  rows,
  labelClassNames,
}: {
  rows: LabeledDataRow[];
  labelClassNames?: { [label: string]: string };
}) {
  const modelRow = rows[0];
  const labels = Object.keys(modelRow);

  return (
    <Table>
      <TableHeader>
        <TableRow>
          {labels.map(label => (
            <TableHead className={cn('text-foreground', labelClassNames?.all, labelClassNames?.[label])} key={label}>
              {label}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody className="text-xs text-muted-foreground">
        {rows.map((row, index) => (
          <TableRow key={index}>
            {labels.map(label => (
              <TableCell key={label}>{row[label] || '-'}</TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}

export function ResponsiveDataTable({
  rows,
  labelClassNames,
  mobileBreakpoint = 900,
}: {
  rows: LabeledDataRow[];
  labelClassNames?: { [label: string]: string };
  mobileBreakpoint?: number;
}) {
  const isMobile = useIsMobileBreakpoint(mobileBreakpoint);

  return isMobile ? (
    <MobileView rows={rows} labelClassNames={labelClassNames} />
  ) : (
    <TableView rows={rows} labelClassNames={labelClassNames} />
  );
}
