import { Minus, Plus } from 'lucide-react';
import { Dispatch, SetStateAction, useCallback } from 'react';
import { Button } from '../ui/button';
import { cn } from '@/lib/utils';

export default function IntField({
  value,
  onChange,
  max = 100,
  min = 1,
  name,
  className,
}: {
  value: number;
  onChange: (value: number) => void;
  max?: number;
  min?: number;
  className?: string;
  name?: string;
}) {
  const setQty = useCallback(
    (newValue: number) => {
      let setValue = newValue || 0;

      if (max !== undefined) setValue = Math.min(max, setValue);
      if (min !== undefined) setValue = Math.max(min, setValue);

      onChange(Math.round(setValue));
    },
    [onChange],
  );

  return (
    <div className={cn('flex gap-4 items-center', className)}>
      <Button variant="outline" size="icon-sm" onClick={() => setQty(value - 1)}>
        <Minus />
      </Button>
      <input
        name={name}
        className="remove-arrow w-[1ch] overflow-visible bg-transparent outline-none border-none font-bold text-lg"
        type="number"
        value={value}
        style={{
          width: `${value.toString().length}ch`,
        }}
        onChange={e => {
          setQty(parseInt(e.target.value));
          e.target.style.width = `${e.target.value.length}ch`;
        }}
      />
      <Button variant="outline" size="icon-sm" onClick={() => setQty(value + 1)}>
        <Plus />
      </Button>
    </div>
  );
}
