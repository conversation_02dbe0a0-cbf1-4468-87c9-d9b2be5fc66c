import { useRemixFormContext } from 'remix-hook-form';
import { FormControl, FormField, FormItem } from '../ui/form';

interface IProps {
  name: string;
  value?: string;
}

export default function HiddenField({ name, value }: IProps) {
  const form = useRemixFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <input type="hidden" {...field} value={value || ''} />
          </FormControl>
        </FormItem>
      )}
    />
  );
}
