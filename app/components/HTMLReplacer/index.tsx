import { useMemo, FC } from 'react';

export interface HTMLSection {
  tag: string;
  outerHtml?: string;
  innerHtml?: string;
  afterHtml: string;
  index: number;
}

export function extractHTMLSections(html: string, htmlTags: string[]) {
  const sections: HTMLSection[] = [
    {
      tag: 'undefined',
      afterHtml: '',
      index: 0,
    },
  ];
  let lastContentStartIndex = 0;

  const regex = new RegExp(`<(${htmlTags.join('|')})[^>]*>([\\S\\s]*?)</\\1>`, 'gm');
  for (const match of html.matchAll(regex)) {
    sections[sections.length - 1].afterHtml = html.substring(lastContentStartIndex, match.index!);
    lastContentStartIndex = match.index! + match[0].length;
    sections.push({
      outerHtml: match[0],
      tag: match[1],
      innerHtml: match[2],
      afterHtml: '',
      index: sections.length,
    });
  }

  sections[sections.length - 1].afterHtml = html.substring(lastContentStartIndex);

  return sections;
}

export default function HTMLReplacer({
  HTML,
  map,
}: {
  HTML: string;
  map: Record<string, FC<HTMLSection & { className?: string }> | undefined>;
}) {
  const listSections = useMemo(() => extractHTMLSections(HTML, Object.keys(map)), [HTML, Object.keys(map).join()]);

  return (
    <>
      {listSections.map(section => {
        const Tag = map[section.tag + ''];
        if (!Tag) return null;

        return <Tag key={section.index} {...section} />;
      })}
    </>
  );
}
