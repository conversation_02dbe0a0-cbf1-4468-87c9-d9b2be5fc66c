import { Button, ButtonProps } from '@/components/ui/button';
import { useNavigation } from 'react-router';

interface IProps {
  title: string;
  description?: string;
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
  footer?: React.ReactNode;
  children: React.ReactNode;
  actionComponent?: React.ReactNode;
}

export default function WizardStep({
  title,
  description,
  primaryButton,
  secondaryButton,
  footer,
  actionComponent = <></>,
  children,
}: IProps) {
  const { state } = useNavigation();
  const isLoading = !primaryButton?.to && state === 'submitting';

  return (
    <div className="flex w-[250px] flex-col items-center justify-center space-y-8">
      <h1 className="text-center text-2xl tracking-tight">{title}</h1>
      {description && <p className="text-center text-sm text-gray-500">{description}</p>}
      <div className="flex w-full flex-col space-y-4">{children}</div>
      <hr className="w-full border-b border-solid border-gray-500/50" />
      <div className="flex w-full flex-col space-y-4">
        {!!primaryButton && <Button variant="primary" type="submit" loading={isLoading} {...primaryButton} />}
        {!!secondaryButton && <Button variant="outline" {...secondaryButton} />}
        {actionComponent}
        {footer}
      </div>
    </div>
  );
}
