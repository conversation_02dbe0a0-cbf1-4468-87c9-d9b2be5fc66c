import useTheme from '@/hooks/use-theme';
import ThemeProvider from '../ThemeProvider';
import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface IProps {
  children: React.ReactNode;
}

export default function ThemeInverter({ children, attributes }: { children?: ReactNode; attributes?: any }) {
  const theme = useTheme();

  return (
    <ThemeProvider theme={theme === 'business' ? 'personal' : 'business'}>
      {themeClassName => (
        <div {...attributes} className={cn(themeClassName, attributes?.className)}>
          {children}
        </div>
      )}
    </ThemeProvider>
  );
}
