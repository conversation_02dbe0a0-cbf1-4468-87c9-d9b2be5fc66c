import { cn } from '@/lib/utils';
import { Image } from '@shopify/hydrogen';
import { useProduct } from '../ProductDataProvider/context';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselProvider,
  CarouselThumbnail,
  CarouselThumbnailContainer,
} from '../ui/carousel';
import { Skeleton } from '../ui/skeleton';

export default function ProductCarousel({ className }: { className?: string }) {
  const { product } = useProduct();

  if (!product) {
    return <Skeleton className="aspect-square w-full" />;
  }
  const slides = product?.images?.nodes || [];

  // const selectedVariant = useSelectedVariant();
  // const featuredImage = selectedVariant?.image;
  // if (featuredImage && !slides.includes(featuredImage)) slides.unshift(featuredImage);

  return (
    <CarouselProvider>
      <div className={cn('flex flex-col items-start', className)}>
        <Carousel>
          <CarouselContent>
            {slides.map((img, idx) => (
              <CarouselItem key={img?.id || idx}>
                <Image
                  alt={img.altText || 'Product Image'}
                  data={img}
                  sizes="(max-width: 1023px) 90vw, 50vw"
                  className="aspect-square! object-contain"
                />
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="-left-6 xl:-left-20" />
          <CarouselNext className="-right-6 xl:-right-20" />
        </Carousel>
        <div className="flex w-full justify-center">
          <CarouselThumbnailContainer className="mt-4 flex max-w-[437px] overflow-x-auto">
            {slides.map((img, idx) => (
              <CarouselThumbnail
                key={img?.id || idx}
                index={idx}
                className={cn('w-[56px] min-w-[56px] overflow-clip rounded-xs', {
                  'mr-4': idx !== slides.length - 1,
                })}
              >
                <Image
                  alt={img.altText || 'Product Image'}
                  aspectRatio="4/3"
                  data={img}
                  width={56}
                  height={42}
                  sizes="56px"
                />
              </CarouselThumbnail>
            ))}
          </CarouselThumbnailContainer>
        </div>
      </div>
    </CarouselProvider>
  );
}
