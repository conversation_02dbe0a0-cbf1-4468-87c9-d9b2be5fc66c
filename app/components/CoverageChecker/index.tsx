import { useFetcher } from 'react-router';
import { ArrowR<PERSON>, Loader2 } from 'lucide-react';
import { useRemixForm } from 'remix-hook-form';
import { Form, useFormOnSubmit } from '@/components/ui/form';
import TextField from '@/components/TextField';
import { Button } from '@/components/ui/button';
import { resolver, SchemaType } from '@/routes/coverage/resolver';
import Icon from '../Icon';
import { CoverageResponse } from '@/routes/coverage/action';
import { IconName } from '../Icon/props';
import { cn } from '@/lib/utils';

const coverageText = {
  3: 'Excellent coverage',
  2: 'Good coverage',
  1: 'Moderate coverage',
  0: 'No coverage',
  999: 'Enter your zip code',
};

type CoverageDisplayProps = {
  isLoading: boolean;
  data?: CoverageResponse['data'];
};

function CoverageDisplay({ isLoading, data = { coverage: 999, zip: '', carrier: null } }: CoverageDisplayProps) {
  const carrierName = data.carrier === 'att' ? 'AT&T' : 'Verizon';
  const coverageLevel = data.coverage;

  const coverageConfig = {
    3: {
      icon: 'editor-choice',
      iconClass: 'text-icon-color',
      message: `You're covered with ${carrierName} in ${data.zip}.`,
    },
    2: {
      icon: 'editor-choice',
      iconClass: 'text-icon-color',
      message: `You have good coverage with ${carrierName} in ${data.zip}.`,
    },
    1: {
      icon: 'low-coverage',
      iconClass: 'text-warning',
      message: `You're barely covered with ${carrierName} in ${data.zip}.`,
    },
    0: {
      icon: 'no-coverage',
      iconClass: 'text-destructive',
      message: `You're not covered with ${carrierName} in ${data.zip}.`,
    },
    999: {
      icon: 'location',
      iconClass: 'text-icon-color',
      message: "We'll verify coverage in your area.",
    },
  }[coverageLevel];

  return (
    <div className="flex flex-1 gap-2">
      <div className="h-12 w-12">
        <Icon
          icon={isLoading ? 'cached' : (coverageConfig?.icon as IconName)}
          className={cn(`h-10 ${coverageConfig?.iconClass}`, isLoading && 'animate-spin text-icon-color')}
        />
      </div>
      <div className="flex flex-col gap-2">
        <p className="sm-text-xl text-lg font-bold">
          {isLoading ? 'Checking coverage' : coverageText[coverageLevel as keyof typeof coverageText]}
        </p>
        <p className="text-body-xs text-body-normal sm:text-body-md">
          {isLoading ? 'Please wait a few seconds...' : coverageConfig?.message}
        </p>
      </div>
    </div>
  );
}

export default function CoverageChecker() {
  const fetcher = useFetcher<CoverageResponse>();
  const form = useRemixForm<SchemaType>({
    resolver,
    values: {
      zip: '',
    },
    submitConfig: {
      method: 'POST',
      action: '/coverage?index',
    },
    fetcher,
  });
  const data = fetcher.data?.data;
  const error = fetcher.data?.error;
  const isLoading = fetcher.state === 'submitting' || fetcher.state === 'loading';

  useFormOnSubmit(form, {});

  return (
    <div className="flex flex-col justify-center gap-10">
      <Form form={form} className="space-y-4">
        <div className="relative">
          <TextField label="Enter Zip Code" placeholder="XXXXX" name="zip" className="h-12 rounded-full" />
          <Button
            type="submit"
            variant="ghost"
            size="icon-sm"
            loading={form.formState.isSubmitting}
            className="absolute top-9 right-3 bg-background"
          >
            <ArrowRight />
          </Button>
        </div>
      </Form>
      <div className="flex items-center gap-2 sm:gap-12">
        <div className="flex h-24 flex-1 items-center justify-between rounded-md bg-white pr-4 transition-all duration-300 sm:h-32 sm:rounded-lg sm:pr-6 sm:pl-2">
          {(!data || data.carrier === 'verizon') && (
            <Icon icon="verizon" className="h-12 transition-opacity duration-300 sm:h-14" />
          )}
          {data?.carrier === 'att' && (
            <img src="/images/att.png" alt="att" className="h-20 transition-opacity duration-300" />
          )}

          <div className="flex items-end space-x-1">
            {[
              { height: 'h-3 sm:h-4' },
              { height: 'h-5 sm:h-6' },
              { height: 'h-7 sm:h-8' },
              { height: 'h-9 sm:h-10' },
            ].map((bar, index) => (
              <div
                key={bar.height}
                className={`w-2 sm:w-3 ${bar.height} transition-all duration-300 ${
                  index <= (data?.coverage || 0) && !!data?.coverage ? 'bg-[#1ac6b8]' : 'bg-gray-400'
                } rounded-sm`}
              ></div>
            ))}
          </div>
        </div>

        <CoverageDisplay isLoading={isLoading} data={data} />
        {error && <p>{error}</p>}
      </div>
    </div>
  );
}
