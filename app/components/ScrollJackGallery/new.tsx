import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import useTheme from '@/hooks/use-theme';
import { cn, generateImageSrcSet } from '@/lib/utils';
import { useMemo } from 'react';
import HorizontalScrollJack from '../HorizontalScrollJack';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselProvider,
} from '../ui/carousel';

interface NewScrollJackGalleryItemProps {
  desktopImageUrl: string;
  mobileImageUrl?: string;
  alt: string;
  title?: string;
  description?: string;
  mobileDescription?: string;
}

function NewScrollJackGalleryItem({
  alt,
  desktopImageUrl,
  mobileImageUrl,
  title,
  description,
  mobileDescription,
  inCarousel,
  gradient,
}: NewScrollJackGalleryItemProps & { inCarousel?: boolean; gradient?: boolean }) {
  const theme = useTheme();
  const isMobile = useIsMobileBreakpoint();

  const viewWidth = 80;
  const ratio = isMobile ? 1.5015 : 0.5339;

  const url = (isMobile && mobileImageUrl) || desktopImageUrl;
  const srcSet = useMemo(() => generateImageSrcSet(url), [url]);

  return (
    <div
      className={cn(
        'relative overflow-clip rounded-md',
        inCarousel ? 'm-4' : 'mr-4 h-[calc(100%-2rem)] sm:mr-8 sm:h-[calc(100%-4rem)]',
      )}
      style={
        inCarousel
          ? undefined
          : {
              // aspectRatio: ratio,
              maxWidth: viewWidth + 'vw',
              maxHeight: viewWidth * ratio + 'vw',
            }
      }
    >
      <img
        alt={alt}
        className="h-full w-full object-cover"
        src={url}
        srcSet={srcSet}
        loading="lazy"
        role="presentation"
        sizes={inCarousel ? '100vw' : viewWidth + 'vw'}
      />
      {gradient && (
        <>
          <div className="absolute -bottom-16 -left-24 h-[30%] w-[75%] rounded-2xl bg-black/50 blur-3xl" />
          <div className="absolute bottom-0 left-0 h-[60%] w-[200%] bg-[radial-gradient(100%_100%_at_-20%_100%,black_25%,transparent_100%)]" />
        </>
      )}

      <div className="absolute bottom-0 left-0 h-40 p-6 sm:p-8 md:h-36">
        <h3 className="text-lg font-bold text-white sm:text-2xl md:text-3xl">{title}</h3>
        <p
          className={cn(
            'mt-2 max-w-md text-sm xl:max-w-2xl md:mt-4 md:text-base',
            theme == 'business' ? 'text-[#A1A1A1]' : 'text-[#ffffff80]',
          )}
        >
          {(isMobile && mobileDescription) || description}
        </p>
      </div>
    </div>
  );
}

interface NewScrollJackGalleryProps {
  children?: React.ReactNode;
  showGradient?: boolean;
  items: NewScrollJackGalleryItemProps[];
}

export default function NewScrollJackGallery({ children, items, showGradient }: NewScrollJackGalleryProps) {
  const isMobile = useIsMobileBreakpoint();

  if (isMobile) {
    return (
      <>
        <div className="mx-4">{children}</div>
        <CarouselProvider>
          <Carousel>
            <CarouselContent>
              {items.map((item, idx) => (
                <CarouselItem key={idx}>
                  <NewScrollJackGalleryItem {...item} inCarousel gradient={showGradient} />
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="left-8" />
            <CarouselNext className="right-8" />
          </Carousel>
        </CarouselProvider>
      </>
    );
  }

  return (
    <HorizontalScrollJack align="bottom" header={children} className="mx-auto w-[90%]">
      {items.map((item, index) => (
        <NewScrollJackGalleryItem key={index} gradient={showGradient} {...item} />
      ))}
    </HorizontalScrollJack>
  );
}
