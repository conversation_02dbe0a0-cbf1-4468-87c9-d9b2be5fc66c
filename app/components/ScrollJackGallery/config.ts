import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import ScrollJackGallery from '.';
import NewScrollJackGallery from './new';
export const SCROLL_JACK_GALLERY_COMPONENT: RegisteredComponent = {
  name: 'Scroll Jack Gallery',
  component: NewScrollJackGallery,
  canHaveChildren: true,
  inputs: [
    {
      name: 'items',
      type: 'list',
      defaultValue: [],
      subFields: [
        { name: 'desktopImageUrl', type: 'string', required: true },
        { name: 'mobileImageUrl', type: 'string', required: true },
        { name: 'alt', type: 'string', required: true },
        { name: 'title', type: 'string', required: false },
        { name: 'description', type: 'string', required: false },
        { name: 'mobileDescription', type: 'string', required: false },
      ],
    },
    {
      name: 'showGradient',
      type: 'boolean',
      defaultValue: false,
    },
  ],
};

export const OLD_SCROLL_JACK_GALLERY_COMPONENT: RegisteredComponent = {
  name: 'Scroll Jack Gallery',
  component: ScrollJackGallery,
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  childRequirements: {
    message: 'You can only put Buttons, Text, or Images in a Hero',
    query: {
      'component.name': { $in: ['Text', 'Columns'] },
    },
  },
  inputs: [
    {
      name: 'showAllSlidesInEditor',
      type: 'boolean',
      helperText:
        'In order to be able to edit all the slides, you need to set this to true, otherwise they are not visible. You might need to refresh the page for layout issues to go away. This has no effect on the live page. Make sure this is off when you are editing mobile.',
      defaultValue: false,
    },
    {
      name: 'blocks',
      type: 'uiBlocks',
      hideFromUI: true,
      defaultValue: [],
    },
    {
      name: 'items',
      type: 'list',
      defaultValue: [
        {
          imageUrl:
            'https://cdn.shopify.com/s/files/1/0708/3078/4833/files/different-way-long-driveway-mailbox.jpg?v=1701285880',
          alt: 'Placeholder',
          desktopBlocks: [],
          mobileBlocks: [],
        },
      ],
      subFields: [
        { name: 'desktopImageUrl', type: 'url', required: true },
        { name: 'mobileImageUrl', type: 'url', required: true },
        { name: 'alt', type: 'string', required: true },
        {
          name: 'desktopBlocks',
          type: 'uiBlocks',
          hideFromUI: true,
          defaultValue: [],
        },
        {
          name: 'mobileBlocks',
          type: 'uiBlocks',
          hideFromUI: true,
          defaultValue: [],
        },
      ],
    },
  ],
};
