import { useLayoutEffect, useRef, useState, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import SimpleBuilderCarousel from '../SimpleBuilderCarousel';
import { Blocks, type BuilderBlock, isEditing } from '@builder.io/sdk-react/edge';
import { cn } from '@/lib/utils';

export type ScrollJackGalleryItem = {
  desktopImageUrl: string;
  mobileImageUrl: string;
  alt: string;
  desktopBlocks: BuilderBlock[];
  mobileBlocks: BuilderBlock[];
};

interface IProps {
  items: ScrollJackGalleryItem[];
  builderBlock: BuilderBlock;
  blocks: BuilderBlock[];
  showAllSlidesInEditor?: boolean;
}

export default function ScrollJacked({ items = [], builderBlock, blocks, showAllSlidesInEditor }: IProps) {
  const componentRef = useRef<HTMLDivElement>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const [isMobile, setIsMobile] = useState<boolean | null>(null);

  useEffect(() => {
    const checkMobile = () => window.innerWidth < 640;
    setIsMobile(checkMobile());

    const handleResize = () => {
      setIsMobile(checkMobile());
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useLayoutEffect(() => {
    if (isMobile === null) return;

    const ctx = gsap.context(() => {
      const mm = gsap.matchMedia();

      mm.add('(min-width: 640px)', () => {
        const section = sectionRef.current;
        const trigger = triggerRef.current;

        if (!section || !trigger || section.offsetWidth === 0 || trigger.offsetHeight === 0) return;

        const calculateScrollWidth = () => {
          return Math.max(0, section.scrollWidth - window.innerWidth + 100);
        };

        gsap.to(section, {
          x: () => -calculateScrollWidth(),
          ease: 'none',
          scrollTrigger: {
            trigger,
            start: 'top top',
            end: () => `+=${calculateScrollWidth()}`,
            scrub: 1,
            pin: trigger,
            anticipatePin: 1,
            invalidateOnRefresh: true,
          },
        });

        // Refresh ScrollTrigger instances AFTER setup to ensure accurate calculations
        // Especially important when switching from mobile layout
        ScrollTrigger.refresh();
      });

      mm.add('(max-width: 639px)', () => {
        if (sectionRef.current) {
          gsap.set(sectionRef.current, { clearProps: 'transform' });
        }
      });
    }, componentRef);

    return () => {
      ctx.revert();
    };
  }, [items, isMobile]);

  if (showAllSlidesInEditor && isEditing()) {
    return (
      <div ref={componentRef} className="flex w-screen flex-col gap-10 pr-10">
        {items.map((item, idx) => (
          <div key={JSON.stringify(item)} className="relative shrink-0">
            <img src={item.desktopImageUrl} alt={item.alt} className="h-full w-full rounded-3xl object-cover" />
            <div className="absolute bottom-8 left-8 w-[50%]">
              <Blocks parent={builderBlock?.id} path={`items.${idx}.desktopBlocks`} blocks={item.desktopBlocks} />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div ref={componentRef} key={isMobile ? 'mobile' : 'desktop'}>
      {isMobile === null ? (
        <div className="h-screen w-full animate-pulse bg-gray-200"></div>
      ) : isMobile ? (
        <div className="relative w-full">
          <Blocks parent={builderBlock?.id} path={'blocks'} blocks={blocks} />
          <SimpleBuilderCarousel
            items={items.map(item => ({
              ...item,
              imageUrl: item.mobileImageUrl,
              blocks: item.mobileBlocks,
            }))}
            insideCarousel={false}
            className="h-[70vh]"
            slideBlocks={items.map(item => item.mobileBlocks)}
            builderBlock={builderBlock}
          />
        </div>
      ) : (
        <>
          <div ref={triggerRef} className="relative h-screen w-full">
            <div className="absolute top-0 left-0 h-[30vh] w-full">
              <Blocks parent={builderBlock?.id} path={'blocks'} blocks={blocks} />
            </div>

            <div ref={sectionRef} className="absolute bottom-0 left-0 flex h-[70vh] w-max gap-10 pr-10">
              {items.map((item, idx) => (
                <div
                  key={JSON.stringify(item)}
                  className={cn(
                    'relative my-auto aspect-[1.87] h-[60vh] shrink-0 overflow-hidden rounded-sm',
                    idx === 0 && 'ml-[5%]',
                  )}
                >
                  <img src={item.desktopImageUrl} alt={item.alt} className="h-full w-full rounded-sm object-cover" />
                  <div className="absolute bottom-0 left-0 h-[60%] w-[140%] bg-[radial-gradient(100%_100%_at_-20%_100%,black_25%,transparent_100%)]" />
                  <div className="absolute bottom-8 left-8 w-[50%]">
                    <Blocks parent={builderBlock?.id} path={`items.${idx}.desktopBlocks`} blocks={item.desktopBlocks} />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
