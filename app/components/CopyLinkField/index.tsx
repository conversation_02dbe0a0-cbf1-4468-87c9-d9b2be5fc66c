import { useEffect, useState } from 'react';
import { CopyTextField } from '../CopyTextField';

export function CopyLinkField({
  link: overrideLink,
  onCopied,
  template,
}: {
  link?: string;
  onCopied?: () => void;
  template?: string;
}) {
  const [pageLink, setLink] = useState('');

  const link = overrideLink || pageLink;

  useEffect(() => {
    setTimeout(() => {
      setLink(window.location.origin + window.location.pathname);
    }, 1);
  }, []);

  return (
    <CopyTextField preview={link} value={template ? template.replaceAll('{link}', link) : link} onCopied={onCopied} />
  );
}
