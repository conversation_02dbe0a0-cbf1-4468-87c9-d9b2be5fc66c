import * as React from 'react';
import useEmblaCarousel, { type UseEmblaCarouselType } from 'embla-carousel-react';
import { ArrowLeft, ArrowRight } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

type CarouselApi = UseEmblaCarouselType[1];
type UseCarouselParameters = Parameters<typeof useEmblaCarousel>;
type CarouselOptions = UseCarouselParameters[0];
type CarouselPlugin = UseCarouselParameters[1];

type CarouselProviderProps = {
  opts?: CarouselOptions;
  plugins?: CarouselPlugin;
  orientation?: 'horizontal' | 'vertical';
  setApi?: (api: CarouselApi) => void;
};

type CarouselContextProps = {
  carouselRef: ReturnType<typeof useEmblaCarousel>[0];
  api: ReturnType<typeof useEmblaCarousel>[1];
  scrollPrev: () => void;
  scrollNext: () => void;
  canScrollPrev: boolean;
  canScrollNext: boolean;
  selectedIndex: number;
  handleKeyDown: (event: React.KeyboardEvent<HTMLDivElement>) => void;
} & CarouselProviderProps;

const CarouselContext = React.createContext<CarouselContextProps | null>(null);

function useCarousel() {
  const context = React.useContext(CarouselContext);

  if (!context) {
    throw new Error('useCarousel must be used within a <Carousel />');
  }

  return context;
}

const CarouselProvider = ({
  orientation = 'horizontal',
  opts,
  setApi,
  plugins,
  children,
}: CarouselProviderProps & { children?: React.ReactNode }) => {
  const [carouselRef, api] = useEmblaCarousel(
    {
      ...opts,
      axis: orientation === 'horizontal' ? 'x' : 'y',
    },
    plugins,
  );
  const [canScrollPrev, setCanScrollPrev] = React.useState(false);
  const [canScrollNext, setCanScrollNext] = React.useState(false);
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const onSelect = React.useCallback((api: CarouselApi) => {
    if (!api) {
      return;
    }

    setCanScrollPrev(api.canScrollPrev());
    setCanScrollNext(api.canScrollNext());
    setSelectedIndex(api.selectedScrollSnap());
  }, []);

  const scrollPrev = React.useCallback(() => {
    api?.scrollPrev();
  }, [api]);

  const scrollNext = React.useCallback(() => {
    api?.scrollNext();
  }, [api]);

  const handleKeyDown = React.useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        scrollPrev();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        scrollNext();
      }
    },
    [scrollPrev, scrollNext],
  );

  React.useEffect(() => {
    if (!api || !setApi) {
      return;
    }

    setApi(api);
  }, [api, setApi]);

  React.useEffect(() => {
    if (!api) {
      return;
    }

    onSelect(api);
    api.on('reInit', onSelect);
    api.on('select', onSelect);

    return () => {
      api?.off('select', onSelect);
    };
  }, [api, onSelect]);

  return (
    <CarouselContext.Provider
      value={{
        carouselRef,
        api,
        opts,
        orientation: orientation || (opts?.axis === 'y' ? 'vertical' : 'horizontal'),
        scrollPrev,
        scrollNext,
        canScrollPrev,
        canScrollNext,
        selectedIndex,
        handleKeyDown,
      }}
    >
      {children}
    </CarouselContext.Provider>
  );
};
CarouselProvider.displayName = 'CarouselProvider';

const Carousel = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => {
    const { handleKeyDown } = useCarousel();

    return (
      <div
        ref={ref}
        onKeyDownCapture={handleKeyDown}
        className={cn('relative', className)}
        role="region"
        aria-roledescription="carousel"
        {...props}
      >
        {children}
      </div>
    );
  },
);
Carousel.displayName = 'Carousel';

const CarouselContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    const { carouselRef, orientation } = useCarousel();

    return (
      <div ref={carouselRef} className="h-full overflow-hidden">
        <div ref={ref} className={cn('flex h-full', orientation === 'vertical' && 'flex-col', className)} {...props} />
      </div>
    );
  },
);
CarouselContent.displayName = 'CarouselContent';

const CarouselPagination = ({ itemCount, className }: { itemCount: number; className?: string }) => {
  const { selectedIndex } = useCarousel();
  return (
    <div className={cn('flex justify-center gap-2', className)}>
      {Array.from({ length: itemCount }).map((_, index) => (
        <div
          key={Math.random()}
          className={`h-2 w-2 rounded-full transition-all ${selectedIndex === index ? 'bg-white' : 'bg-muted'}`}
        />
      ))}
    </div>
  );
};
CarouselPagination.displayName = 'CarouselPagination';

const CarouselItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        role="group"
        aria-roledescription="slide"
        className={cn('min-w-0 shrink-0 grow-0 basis-full', className)}
        {...props}
      />
    );
  },
);
CarouselItem.displayName = 'CarouselItem';

const CarouselPrevious = React.forwardRef<HTMLButtonElement, React.ComponentProps<typeof Button>>(
  ({ className, variant = 'outline', size = 'icon', ...props }, ref) => {
    const { orientation, scrollPrev, canScrollPrev } = useCarousel();

    return (
      <Button
        ref={ref}
        variant={variant}
        size={size}
        className={cn(
          'absolute',
          orientation === 'horizontal'
            ? 'top-1/2 -left-12 -translate-y-1/2'
            : '-top-12 left-1/2 -translate-x-1/2 rotate-90',
          className,
        )}
        disabled={!canScrollPrev}
        onClick={scrollPrev}
        {...props}
      >
        <ArrowLeft />
        <span className="sr-only">Previous slide</span>
      </Button>
    );
  },
);
CarouselPrevious.displayName = 'CarouselPrevious';

const CarouselNext = React.forwardRef<HTMLButtonElement, React.ComponentProps<typeof Button>>(
  ({ className, variant = 'outline', size = 'icon', ...props }, ref) => {
    const { orientation, scrollNext, canScrollNext, api } = useCarousel();

    const handleClick = () => {
      if (canScrollNext) {
        scrollNext();
      } else {
        api?.scrollTo(0);
      }
    };

    return (
      <Button
        ref={ref}
        variant={variant}
        size={size}
        className={cn(
          'absolute',
          orientation === 'horizontal'
            ? 'top-1/2 -right-12 -translate-y-1/2'
            : '-bottom-12 left-1/2 -translate-x-1/2 rotate-90',
          className,
        )}
        onClick={handleClick}
        {...props}
      >
        <ArrowRight />
        <span className="sr-only">Next slide</span>
      </Button>
    );
  },
);
CarouselNext.displayName = 'CarouselNext';

const CarouselThumbnailContainer = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { orientation?: 'horizontal' | 'vertical' }
>(({ className, orientation = 'horizontal', ...props }, ref) => {
  const { selectedIndex } = useCarousel();

  const previewRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!previewRef.current) return;
    const thumbnail = previewRef.current.querySelector(`button[data-thumbnail-idx="${selectedIndex}"]`);
    if (!thumbnail) return;

    const container = previewRef.current;
    const thumbnailRect = thumbnail.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    if (thumbnailRect.left < containerRect.left || thumbnailRect.right > containerRect.right) {
      thumbnail.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest',
      });
    }
  }, [selectedIndex]);
  return (
    <div
      ref={previewRef}
      className={cn(
        'hide-scrollbar snap-both snap-mandatory scroll-smooth',
        orientation === 'horizontal' ? 'overflow-x-scroll overflow-y-hidden' : 'overflow-x-hidden overflow-y-scroll',
        className,
      )}
    >
      <div ref={ref} className={cn('flex', orientation === 'horizontal' ? 'flex-row' : 'flex-col')} {...props} />
    </div>
  );
});
CarouselThumbnailContainer.displayName = 'CarouselThumbnailContainer';

const CarouselThumbnail = React.forwardRef<
  HTMLButtonElement,
  React.HTMLAttributes<HTMLButtonElement> & { index: number }
>(({ className, index, ...props }, ref) => {
  const { api, selectedIndex } = useCarousel();

  const handleClick = React.useCallback(() => api?.scrollTo(index), [api, index]);

  return (
    <button
      data-thumbnail-idx={index.toString()}
      data-selected={selectedIndex === index}
      ref={ref}
      className={cn('shrink-0 snap-start', { 'border-gray border border-solid': selectedIndex === index }, className)}
      {...props}
      onClick={handleClick}
    />
  );
});
CarouselThumbnail.displayName = 'CarouselThumbnail';

export {
  type CarouselApi,
  CarouselProvider,
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
  CarouselThumbnailContainer,
  CarouselThumbnail,
  CarouselPagination,
};
