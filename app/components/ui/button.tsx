import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

const buttonVariantsConfig = {
  variants: {
    variant: {
      primary:
        'text-background transition-all duration-500 ease-in-out bg-size-[200%_100%] bg-linear-to-r from-green-two via-green-three to-green-two',
      outline:
        'text-text-button-outline border border-button bg-background hover:bg-foreground hover:text-background active:bg-foreground/90',
      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
      link: 'text-foreground rounded-md font-bold underline-offset-4 hover:underline',
      ghost: 'text-foreground rounded-md font-bold underline-offset-4 hover:underline hover:bg-muted',
    },
    size: {
      'p-sm': 'px-4',
      'p-md': 'px-6',
      'p-lg': 'px-8',
      sm: 'w-[150px]',
      md: 'w-[180px]',
      lg: 'w-[270px]',
      full: 'w-full',
      fit: 'h-auto p-0 w-fit shrink-0',
      icon: 'h-10 w-10 [&_svg]:size-6',
      'icon-sm': 'h-6 w-6 p-1',
      'icon-md': 'h-8 w-8 p-1 [&_svg]:size-8',
      'icon-lg': 'h-14 w-14 [&_svg]:size-10',
      'icon-xl': 'h-16 w-16 [&_svg]:size-12',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
  } as const,
};

const buttonVariants = cva(
  'inline-flex overflow-clip relative items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-bold ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 py-3 h-10',
  buttonVariantsConfig,
);

export const BUTTON_VARIANTS = Object.keys(buttonVariantsConfig.variants.variant);
export const BUTTON_SIZES = Object.keys(buttonVariantsConfig.variants.size);

export type ButtonVariant = keyof typeof buttonVariantsConfig.variants.variant;

export interface ButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'variant'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  title?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant = 'primary', size = 'md', asChild = false, title, loading, disabled, children, ...props },
    ref,
  ) => {
    if (title) {
      children = title;
      asChild = false;
    }

    if (loading) {
      children = (
        <div className="absolute top-0 right-0 bottom-0 left-0 inline-flex scale-150 items-center justify-center">
          <Loader2 className="animate-spin" />
        </div>
      );
      asChild = false;
      disabled = true;
    }

    const Comp = asChild ? Slot : 'button';

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        style={{
          ...(variant === 'primary' && {
            backgroundPosition: '0% 0%',
          }),
        }}
        onMouseEnter={(e: React.MouseEvent<HTMLElement>) =>
          variant === 'primary' && (e.currentTarget.style.backgroundPosition = '100% 0%')
        }
        onMouseLeave={(e: React.MouseEvent<HTMLElement>) =>
          variant === 'primary' && (e.currentTarget.style.backgroundPosition = '0% 0%')
        }
        ref={ref}
        disabled={disabled}
        {...props}
      >
        {children}
      </Comp>
    );
  },
);

Button.displayName = 'Button';

export { Button, buttonVariants };
