import { Button } from '../ui/button';
import Icon from '../Icon';
import { useEffect, useState } from 'react';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';

export default function ChatLauncher() {
  const [unreadCount, setUnreadCount] = useState(0);
  const isMobile = useIsMobileBreakpoint();

  useEffect(() => {
    if (typeof window === 'undefined') return;

    if (document?.getElementById('ze-snippet')) return;
    // We have to load the script this way and use onload in order to avoid a really
    // strange infinite loop that happens when we call window.zE before script is done loading.
    const script = document.createElement('script');
    script.id = 'ze-snippet';
    script.src = 'https://static.zdassets.com/ekr/snippet.js?key=7e26e955-bb78-46a9-a1ae-cde73652516e';

    script.async = true;

    script.onload = () => {
      if (window.zE) {
        window.zE('messenger:on', 'unreadMessages', (count: number) => {
          setUnreadCount(count);
        });
      }
    };

    script.onerror = () => {
      console.error('Failed to load Zendesk chat widget script');
    };

    document.body.appendChild(script);
  }, []);

  const handleLaunchChat = () => {
    window?.zE?.('messenger', 'open');
  };

  return (
    <Button
      id="conversation-badge"
      variant="primary"
      size={isMobile ? 'icon-lg' : 'icon-xl'}
      className="fixed right-4 bottom-4 z-40 overflow-visible shadow-lg"
      onClick={handleLaunchChat}
    >
      <Icon icon="chat-conversation" className="w-6 sm:w-9" />
      {unreadCount > 0 && (
        <span
          id="unread-indicator"
          className="absolute -bottom-1 -left-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-body-xxs text-white sm:h-6 sm:w-6"
        >
          {unreadCount}
        </span>
      )}
    </Button>
  );
}
