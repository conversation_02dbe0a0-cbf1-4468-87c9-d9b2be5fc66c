import { RegisteredComponent } from '@builder.io/sdk-react/edge';
import NewMarketingVideo from './new';

export const MARKETING_VIDEO_COMPONENT: RegisteredComponent = {
  name: 'Marketing Video',
  component: NewMarketingVideo,
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  childRequirements: {
    message: 'You can only put Text and Multi Color Heading in a Video Player',
    query: {
      'component.name': { $in: ['Text', 'Multi Color Heading'] },
    },
  },
  inputs: [
    {
      name: 'posterImageUrlDesktop',
      required: true,
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/video-img-thumb-desktop.jpg?v=1742489530',
    },
    {
      name: 'posterImageUrlMobile',
      required: true,
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/video-img-thumb-mobile.jpg?v=1742489727',
    },
    {
      name: 'videoThumbnailUrlDesktop',
      required: true,
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/videos/c/o/v/f888da6caaa445338938cb575c6652ab.mp4',
    },
    {
      name: 'videoThumbnailUrlMobile',
      required: true,
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/videos/c/o/v/701abdbfc04b4091ba2961ecc425158b.mp4',
    },
    {
      name: 'videoUrlDesktop',
      required: true,
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/videos/c/o/v/48107705a5a94ccab37340062b8e56cc.mp4',
    },
    {
      name: 'videoUrlMobile',
      required: true,
      type: 'string',
      defaultValue: 'https://cdn.shopify.com/videos/c/o/v/1fa514e981694832963c6500f464bf64.mp4',
    },
    {
      name: 'trackSrc',
      type: 'url',
      required: true,
      helperText: 'The URL of the track file. Must be a .vtt file.',
      defaultValue: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/captions.vtt?v=1743456021',
    },
    {
      name: 'blocks',
      type: 'uiBlocks',
      hideFromUI: true,
      defaultValue: [],
    },
  ],
};
