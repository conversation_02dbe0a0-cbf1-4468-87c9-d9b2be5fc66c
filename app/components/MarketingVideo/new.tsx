import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { cn, lerp } from '@/lib/utils';
import { Blocks, BuilderBlock } from '@builder.io/sdk-react/edge';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import VideoPlayer from './VideoPlayer';
import useAnimateOnView from '@/hooks/use-animate-on-view';

interface IProps {
  posterImageUrlDesktop: string;
  posterImageUrlMobile: string;
  videoThumbnailUrlDesktop: string;
  videoThumbnailUrlMobile: string;
  videoUrlDesktop: string;
  videoUrlMobile: string;
  trackSrc: string;
  builderBlock: BuilderBlock;
  blocks: BuilderBlock[];
  children: React.ReactNode;
}

//Found at https://easings.net/#easeInOutQuad
function easeInOutQuad(x: number): number {
  return x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2;
}

export default function NewMarketingVideo({
  posterImageUrlDesktop,
  posterImageUrlMobile,
  videoThumbnailUrlDesktop,
  videoThumbnailUrlMobile,
  videoUrlDesktop,
  videoUrlMobile,
  trackSrc,
  builderBlock,
  blocks,
  children,
}: IProps) {
  const isMobile = useIsMobileBreakpoint();
  const sectionRef = useRef<HTMLElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const videoThumbnailRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPlayingThumbnail, setIsPlayingThumbnail] = useState(false);
  const [hasPressedPlay, setHasPressedPlay] = useState(false);
  const videoUrl = isMobile ? videoUrlMobile : videoUrlDesktop;
  const videoThumbnailUrl = isMobile ? videoThumbnailUrlMobile : videoThumbnailUrlDesktop;
  const posterImageUrl = isMobile ? posterImageUrlMobile : posterImageUrlDesktop;
  const showPlayButton = hasPressedPlay ? !isPlaying : true;

  function handlePlayPause(playing: boolean) {
    if (!hasPressedPlay) {
      setHasPressedPlay(true);
    }
    setIsPlaying(playing);
  }

  useEffect(() => {
    if (!hasPressedPlay) return;

    const currentVideo = videoRef.current;

    if (isPlaying) {
      currentVideo?.play();
    } else {
      currentVideo?.pause();
    }
  }, [isPlaying, hasPressedPlay]);

  useEffect(() => {
    if (hasPressedPlay) return;

    const currentVideo = videoThumbnailRef.current;

    if (isPlayingThumbnail) {
      currentVideo?.play();
    } else {
      currentVideo?.pause();
    }
  }, [isPlayingThumbnail, hasPressedPlay]);

  const { ref: rootRef } = useAnimateOnView(root => {
    const section = sectionRef.current;
    const container = containerRef.current;

    if (!section || !container) return;

    const progress = Math.max(0, Math.min(1, -root.getBoundingClientRect().top / section.clientHeight));

    if (progress > 0.01) setIsPlayingThumbnail(true);

    const t = easeInOutQuad(progress);
    container.style.transform = `translateZ(0) scale(${lerp(0.4, 1, t)})`;
    container.style.borderRadius = lerp(64, 0, t) + 'px';
  });

  return (
    <div ref={rootRef} className="relative h-[200vh]">
      <section
        ref={sectionRef}
        className="sticky top-0 flex h-screen w-full flex-col items-center justify-center overflow-hidden"
      >
        <div className="absolute top-0 right-0 left-0">
          <Blocks parent={builderBlock?.id} path={'blocks'} blocks={blocks} />
        </div>
        <div
          ref={containerRef}
          className={cn(
            'relative z-10 w-full transform-[translateZ(0)_scale(0.4)] overflow-hidden will-change-transform',
            //isMobile ? 'mx-auto aspect-9/16 w-2/5 rounded-sm' : 'aspect-video w-2/5 rounded-lg',
          )}
          style={{ transformOrigin: 'center center' }}
        >
          {hasPressedPlay ? (
            <VideoPlayer
              videoRef={videoRef}
              src={videoUrl}
              preload="none"
              muted={false}
              loop={false}
              controls={false}
              trackSrc={trackSrc}
              showCustomControls={true}
              onPlay={() => handlePlayPause(hasPressedPlay ? !isPlaying : true)}
              showPlayButton={showPlayButton}
            />
          ) : (
            <VideoPlayer
              videoRef={videoThumbnailRef}
              src={videoThumbnailUrl}
              posterImageUrl={posterImageUrl}
              preload="metadata"
              muted={true}
              loop={true}
              controls={false}
              showPlayButton={showPlayButton}
              onPlay={() => handlePlayPause(hasPressedPlay ? !isPlaying : true)}
            />
          )}
          {children}
        </div>
      </section>
    </div>
  );
}
