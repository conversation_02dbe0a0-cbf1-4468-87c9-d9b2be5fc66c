import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Blocks, BuilderBlock } from '@builder.io/sdk-react/edge';
import PlayButton from '../PlayButton';
import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import VideoPlayer from './VideoPlayer';
import { cn } from '@/lib/utils';

interface IProps {
  posterImageUrlDesktop: string;
  posterImageUrlMobile: string;
  videoThumbnailUrlDesktop: string;
  videoThumbnailUrlMobile: string;
  videoUrlDesktop: string;
  videoUrlMobile: string;
  trackSrc: string;
  builderBlock: BuilderBlock;
  blocks: BuilderBlock[];
  children: React.ReactNode;
}

export default function MarketingVideo({
  posterImageUrlDesktop,
  posterImageUrlMobile,
  videoThumbnailUrlDesktop,
  videoThumbnailUrlMobile,
  videoUrlDesktop,
  videoUrlMobile,
  trackSrc,
  builderBlock,
  blocks,
  children,
}: IProps) {
  const isMobile = useIsMobileBreakpoint();
  const sectionRef = useRef<HTMLElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const blocksContainerRef = useRef<HTMLDivElement | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const videoThumbnailRef = useRef<HTMLVideoElement>(null);
  const animationRef = useRef<gsap.Context | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPlayingThumbnail, setIsPlayingThumbnail] = useState(false);
  const [hasPressedPlay, setHasPressedPlay] = useState(false);
  const videoUrl = isMobile ? videoUrlMobile : videoUrlDesktop;
  const videoThumbnailUrl = isMobile ? videoThumbnailUrlMobile : videoThumbnailUrlDesktop;
  const posterImageUrl = isMobile ? posterImageUrlMobile : posterImageUrlDesktop;
  const showPlayButton = hasPressedPlay ? !isPlaying : true;

  function handlePlayPause(playing: boolean) {
    if (!hasPressedPlay) {
      setHasPressedPlay(true);
    }
    setIsPlaying(playing);
  }

  useEffect(() => {
    if (!hasPressedPlay) return;

    const currentVideo = videoRef.current;

    if (isPlaying) {
      currentVideo?.play();
    } else {
      currentVideo?.pause();
    }
  }, [isPlaying, hasPressedPlay]);

  useEffect(() => {
    if (hasPressedPlay) return;

    const currentVideo = videoThumbnailRef.current;

    if (isPlayingThumbnail) {
      currentVideo?.play();
    } else {
      currentVideo?.pause();
    }
  }, [isPlayingThumbnail, hasPressedPlay]);

  useEffect(() => {
    const initTimeout = setTimeout(() => {
      const section = sectionRef.current;
      const container = containerRef.current;
      const blocksContainer = blocksContainerRef.current;

      if (!section || !container || !blocksContainer) return;

      // Kill any existing animations before creating new ones
      if (animationRef.current) {
        animationRef.current.revert();
      }

      // Create a GSAP context scoped to the section element
      const ctx = gsap.context(() => {
        // Ensure ScrollTrigger is refreshed to get the latest dimensions
        ScrollTrigger.refresh();

        const scaleAnimation = gsap.timeline({
          scrollTrigger: {
            trigger: section,
            start: 'top top',
            end: '+=100%',
            scrub: true,
            pin: true,
            anticipatePin: 1,
            invalidateOnRefresh: true,
            onRefresh: self => {
              if (self && self.progress === 1) {
                self.animation?.progress(1);
                setIsPlayingThumbnail(true);
              } else if (self && self.progress === 0) {
                self.animation?.progress(0);
                setIsPlayingThumbnail(false);
              }
            },
          },
        });

        // Animation to scale the element to full viewport size
        scaleAnimation.to(container, {
          width: '100%',
          height: '100%',
          borderRadius: 0,
          duration: 0.7,
          ease: 'power1.inOut',
        });

        // Move the blocks container up at the same rate as the video scales
        scaleAnimation.to(
          blocksContainer,
          {
            y: '-100%',
            opacity: 0,
            duration: 0.5,
            ease: 'power1.inOut',
          },
          0,
        ); // The ", 0" makes this animation run in parallel with the container scaling

        // Consumes scroll to allow the thumbnail to play
        scaleAnimation.to(
          {},
          {
            duration: 0.3,
            onStart: () => setIsPlayingThumbnail(true),
          },
        );
      }, section);

      // Store the context for cleanup
      animationRef.current = ctx;
    }, 300); // Small delay to ensure DOM is ready for the edge case that user refreshes while on the animation

    return () => {
      clearTimeout(initTimeout);

      if (animationRef.current) {
        animationRef.current.revert();
      }
    };
  }, [sectionRef, containerRef, blocksContainerRef, animationRef]);

  return (
    <div className="relative h-[200vh]">
      <section
        ref={sectionRef}
        className="relative flex h-screen w-full flex-col items-center justify-center overflow-hidden"
      >
        <div ref={blocksContainerRef} className="absolute top-0 right-0 left-0">
          <Blocks parent={builderBlock?.id} path={'blocks'} blocks={blocks} />
        </div>
        <div
          ref={containerRef}
          className={cn(
            'relative overflow-hidden',
            isMobile ? 'mx-auto aspect-9/16 w-2/5 rounded-sm' : 'aspect-video w-2/5 rounded-lg',
          )}
          style={{ transformOrigin: 'center center' }}
        >
          {hasPressedPlay ? (
            <VideoPlayer
              videoRef={videoRef}
              src={videoUrl}
              preload="none"
              muted={false}
              loop={false}
              controls={false}
              trackSrc={trackSrc}
              showCustomControls={true}
              onPlay={() => handlePlayPause(hasPressedPlay ? !isPlaying : true)}
              showPlayButton={showPlayButton}
            />
          ) : (
            <VideoPlayer
              videoRef={videoThumbnailRef}
              src={videoThumbnailUrl}
              posterImageUrl={posterImageUrl}
              preload="metadata"
              muted={true}
              loop={true}
              controls={false}
              showPlayButton={showPlayButton}
              onPlay={() => handlePlayPause(hasPressedPlay ? !isPlaying : true)}
            />
          )}
          {children}
        </div>
      </section>
    </div>
  );
}
