import Icon from '../Icon';
import { Button } from '../ui/button';
import { useState, useEffect } from 'react';
import PlayButton from '../PlayButton';

export default function VideoPlayer({
  videoRef,
  src,
  posterImageUrl = '',
  preload,
  muted: initialMuted = false,
  loop,
  controls,
  trackSrc,
  showCustomControls = false,
  showPlayButton = false,
  onPlay,
}: {
  videoRef: React.RefObject<HTMLVideoElement>;
  src: string;
  posterImageUrl?: string;
  preload: 'metadata' | 'auto' | 'none';
  muted?: boolean;
  loop: boolean;
  controls: boolean;
  trackSrc?: string;
  showCustomControls?: boolean;
  showPlayButton: boolean;
  onPlay: () => void;
}) {
  const [captionsEnabled, setCaptionsEnabled] = useState(false);
  const [isMuted, setIsMuted] = useState(initialMuted);

  useEffect(() => {
    setIsMuted(initialMuted);
  }, [initialMuted]);

  const toggleCaptions = () => {
    if (!videoRef.current) return;

    const tracks = videoRef.current.textTracks;
    if (tracks.length > 0) {
      const newCaptionsEnabled = !captionsEnabled;
      setCaptionsEnabled(newCaptionsEnabled);

      tracks[0].mode = newCaptionsEnabled ? 'showing' : 'disabled';
    }
  };

  const toggleMute = () => {
    if (!videoRef.current) return;

    const newMutedState = !isMuted;
    setIsMuted(newMutedState);
    videoRef.current.muted = newMutedState;
  };

  return (
    <>
      {/* eslint-disable-next-line jsx-a11y/media-has-caption */}
      <video
        ref={videoRef}
        src={src}
        poster={posterImageUrl}
        preload={preload}
        muted={isMuted}
        loop={loop}
        controls={controls}
        className="h-full w-full"
        crossOrigin="anonymous"
        style={{
          objectFit: 'cover',
          objectPosition: 'center',
        }}
      >
        {trackSrc && <track kind="captions" src={trackSrc} label="Captions" srcLang="en" default={false} />}
      </video>
      <PlayButton onClick={onPlay} show={showPlayButton} />
      {showCustomControls && (
        <div className="absolute top-2 right-2 flex gap-1">
          <Button
            variant="ghost"
            size="icon-lg"
            onClick={toggleCaptions}
            aria-label={captionsEnabled ? 'Disable captions' : 'Enable captions'}
          >
            <Icon icon={captionsEnabled ? 'closed-caption-disabled' : 'closed-caption'} />
          </Button>
          <Button variant="ghost" size="icon-lg" onClick={toggleMute} aria-label={isMuted ? 'Unmute' : 'Mute'}>
            <Icon icon={isMuted ? 'volume-off' : 'volume-up'} />
          </Button>
        </div>
      )}
    </>
  );
}
