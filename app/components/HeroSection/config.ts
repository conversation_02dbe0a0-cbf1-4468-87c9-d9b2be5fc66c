import { type RegisteredComponent } from '@builder.io/sdk-react/edge';
import HeroSection from '.';

export const HERO_SECTION_COMPONENT: RegisteredComponent = {
  name: 'Hero Section',
  component: HeroSection,
  shouldReceiveBuilderProps: {
    builderBlock: true,
    builderComponents: true,
    builderContext: true,
  },
  inputs: [
    {
      name: 'images',
      type: 'list',
      defaultValue: [
        {
          desktopImageUrl: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/hero-storage.png?v=1742920798',
          mobileImageUrl: 'https://cdn.shopify.com/s/files/1/0608/4762/0347/files/hero-storage-mobile.png?v=1743117603',
          alt: 'Placeholder',
        },
      ],
      subFields: [
        {
          name: 'desktopImageUrl',
          type: 'url',
        },
        {
          name: 'mobileImageUrl',
          type: 'url',
        },
        {
          name: 'alt',
          type: 'string',
        },
      ],
    },
    {
      name: 'animationDuration',
      type: 'number',
      defaultValue: 4.5,
      helperText: 'The duration of the animation in seconds',
    },
    {
      name: 'blocks',
      type: 'uiBlocks',
      hideFromUI: true,
      defaultValue: [],
    },
  ],
};
