import { useIsMobileBreakpoint } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { Blocks, BuilderBlock } from '@builder.io/sdk-react/edge';
import { useState, useEffect } from 'react';

interface IProps {
  blocks: BuilderBlock[];
  builderBlock: BuilderBlock;
  images: {
    desktopImageUrl: string;
    mobileImageUrl: string;
    alt: string;
  }[];
  animationDuration: number;
}

// TODO needs to work in dark mode
// TODO needs to work on mobile
// TODO mobile image should be separate

export default function HeroSection({ blocks, builderBlock, images, animationDuration }: IProps) {
  const isMobile = useIsMobileBreakpoint();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [prevImageIndex, setPrevImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const duration = animationDuration * 1000; // duration in seconds

  useEffect(() => {
    if (images.length <= 1) return;

    const interval = setInterval(() => {
      setPrevImageIndex(currentImageIndex);
      setIsTransitioning(true);

      const nextIndex = (currentImageIndex + 1) % images.length;
      setCurrentImageIndex(nextIndex);

      const transitionTimeout = setTimeout(() => {
        setIsTransitioning(false);
      }, duration);

      return () => clearTimeout(transitionTimeout);
    }, duration);

    return () => clearInterval(interval);
  }, [currentImageIndex, images.length, duration]);

  return (
    <section className="h-[70vh] w-screen xl:h-[calc(100vh-144px)]">
      <div className="relative flex h-full flex-col justify-end xl:flex-row">
        <div className="absolute top-0 left-0 z-30 h-[30%] w-full xl:bottom-0 xl:h-auto xl:w-[40%]">
          <Blocks parent={builderBlock?.id} path="blocks" blocks={blocks} />
        </div>

        <div className="relative h-[70%] w-full xl:h-full xl:w-[60%]">
          <div className="absolute -top-1 bottom-0 left-0 z-10 w-full bg-linear-to-b from-background to-transparent xl:w-2/5 xl:bg-linear-to-r" />

          {images.map((image, index) => (
            <img
              key={isMobile ? image.mobileImageUrl : image.desktopImageUrl}
              src={isMobile ? image.mobileImageUrl : image.desktopImageUrl}
              alt={image.alt}
              style={{ transitionDuration: `${duration}ms` }}
              className={cn(
                'absolute h-full w-full object-cover transition-opacity',
                images.length <= 1
                  ? 'opacity-100'
                  : index === currentImageIndex
                    ? 'opacity-100'
                    : index === prevImageIndex && isTransitioning
                      ? 'opacity-0'
                      : 'opacity-0',
              )}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
