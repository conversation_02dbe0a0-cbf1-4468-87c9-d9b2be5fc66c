import { useToast } from '@/hooks/use-toast';
import { action } from '@/routes/api.identify-vins';
import { useFetcher } from 'react-router';
import { Camera, Loader2, Text, Upload } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import ImageUploader from '../ImageUploader';
import { Button, ButtonVariant } from '../ui/button';
import VINTextAreaDrawer from './VINTextAreaDrawer';
import CameraCaptureDrawer from '../CameraCaptureDrawer';

interface VINImporterProps {
  onSelectVINs?: (VINs: string[]) => void;
  variants?: ButtonVariant;
  className?: string;
}
export default function VINImporter({ onSelectVINs, variants = 'outline', className }: VINImporterProps) {
  const fetcher = useFetcher<typeof action>();
  const { toast } = useToast();

  const upload = useCallback(
    (file: File | Blob | null) => {
      if (!file) return;

      const formData = new FormData();

      // setSize(`${file.type}: ${file.size / 1000}Kb`);
      formData.append('image', file, 'prompt.jpg');

      fetcher.submit(formData, {
        method: 'POST',
        encType: 'multipart/form-data',
        action: '/api/identify-vins',
      });
    },
    [fetcher],
  );

  useEffect(() => {
    if (fetcher.data?.error) {
      toast({
        title: 'Try again',
        variant: 'destructive',
        description: fetcher.data?.error,
      });
    } else if (fetcher.data?.VINs?.length) {
      if (onSelectVINs) onSelectVINs(fetcher.data?.VINs);
    }
  }, [fetcher.data]);

  if (fetcher.state != 'idle') {
    return <Loader2 className="h-16 w-16 animate-spin" />;
  }

  return (
    <>
      <VINTextAreaDrawer onSelectVINs={onSelectVINs}>
        <Button className={className} variant={variants}>
          <Text /> Copy/paste VINs
        </Button>
      </VINTextAreaDrawer>
      <ImageUploader onUpload={upload}>
        <Button className={className} variant={variants}>
          <Upload /> Upload photo
        </Button>
      </ImageUploader>
      <CameraCaptureDrawer
        title="Capture your license plates or VINs"
        description="Take a picture of your license plates or VINs. Don't worry we can handle it"
        onCapture={upload}
      >
        <Button className={className} variant={variants}>
          <Camera /> Snap photo
        </Button>
      </CameraCaptureDrawer>
    </>
  );
}
