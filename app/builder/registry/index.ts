import { BUILDER_ACCORDION_COMPONENT } from '@/business/BuilderAccordion/config';
import { BLOB_BACKGROUND_WRAPPER_COMPONENT } from '@/components/BlobBackgroundWrapper/config';
import { CONTACT_FORM_COMPONENT } from '@/components/ContactForm/config';
import { COVERAGE_CHECKER_COMPONENT } from '@/components/CoverageChecker/config';
import { DELIVERS_BY_COMPONENT } from '@/components/DeliversBy/config';
import { FADE_OUT_CAROUSEL_COMPONENT } from '@/components/FadeOutCarousel/config';
import { HEADER_COMPONENT } from '@/components/Header/config';
import { HERO_GRADIENT_WRAPPER_COMPONENT } from '@/components/HeroGradientWrapper/config';
import { HERO_SECTION_COMPONENT } from '@/components/HeroSection/config';
import { HORIZONTAL_SCROLL_JACK_COMPONENT } from '@/components/HorizontalScrollJack/config';
import { LIVE_AGENT_COMPONENT } from '@/components/LiveAgent/config';
import { MARKETING_VIDEO_COMPONENT } from '@/components/MarketingVideo/config';
import { TRACKER_COMPONENT } from '@/components/Tracker/config';
import { MULTI_COLOR_HEADING_COMPONENT } from '@/components/MultiColorHeading/config';
import { PRODUCT_CAROUSEL_COMPONENT } from '@/components/ProductCarousel/config';
import { PRODUCT_DATA_PROVIDER_COMPONENT } from '@/components/ProductDataProvider/config';
import { PRODUCT_PRICE_COMPONENT } from '@/components/ProductPrice/config';
import { RAY_BACKGROUND_WRAPPER_COMPONENT } from '@/components/RayBackgroundWrapper/config';
import { SCROLL_JACK_GALLERY_COMPONENT } from '@/components/ScrollJackGallery/config';
import { SIMPLE_BUILDER_CAROUSEL_COMPONENT } from '@/components/SimpleBuilderCarousel/config';
import { TEXT_WITH_MODAL_BUTTON_COMPONENT } from '@/components/TextWithModalButton/config';
import { TEXT_WITH_READ_MORE_COMPONENT } from '@/components/TextWithReadMore/config';
import { THEME_INVERTER_COMPONENT } from '@/components/ThemeInverter/config';
import { TUTORIAL_VIDEO_COMPONENT } from '@/components/TutorialVideo/config';
import { PRODUCT_DISCOUNT_TAG_COMPONENT } from '@/components/ProductDiscountTag/config';
import { PRODUCT_ADD_TO_CART_COMPONENT } from '@/components/ProductAddToCart/config';
import { PRODUCT_DESCRIPTION_COMPONENT } from '@/components/ProductDescription/config';
import { PRODUCT_SPECS_COMPONENT } from '@/components/ProductSpecs/config';
import { PRODUCT_TITLE_COMPONENT } from '@/components/ProductTitle/config';
import { PRODUCT_OPTIONS_COMPONENT } from '@/components/ProductOptions/config';
import { BUTTON_COMPONENT } from '@/components/Button/config';
import { ICON_COMPONENT } from '@/components/Icon/config';
import { SEPARATOR_COMPONENT } from '@/components/Separator/config';
import { EXTERNAL_SUPPORT_SEARCH_BAR_COMPONENT } from '@/components/ExternalSupportSearchBar/config';

export const CUSTOM_COMPONENTS = [
  BUTTON_COMPONENT,
  ICON_COMPONENT,
  MULTI_COLOR_HEADING_COMPONENT,
  SCROLL_JACK_GALLERY_COMPONENT,
  HORIZONTAL_SCROLL_JACK_COMPONENT,
  MARKETING_VIDEO_COMPONENT,
  COVERAGE_CHECKER_COMPONENT,
  FADE_OUT_CAROUSEL_COMPONENT,
  TRACKER_COMPONENT,
  BUILDER_ACCORDION_COMPONENT,
  SIMPLE_BUILDER_CAROUSEL_COMPONENT,
  PRODUCT_DATA_PROVIDER_COMPONENT,
  PRODUCT_ADD_TO_CART_COMPONENT,
  PRODUCT_PRICE_COMPONENT,
  PRODUCT_OPTIONS_COMPONENT,
  PRODUCT_TITLE_COMPONENT,
  PRODUCT_DESCRIPTION_COMPONENT,
  PRODUCT_SPECS_COMPONENT,
  PRODUCT_CAROUSEL_COMPONENT,
  PRODUCT_DISCOUNT_TAG_COMPONENT,
  DELIVERS_BY_COMPONENT,
  CONTACT_FORM_COMPONENT,
  HEADER_COMPONENT,
  TEXT_WITH_READ_MORE_COMPONENT,
  THEME_INVERTER_COMPONENT,
  HERO_GRADIENT_WRAPPER_COMPONENT,
  SEPARATOR_COMPONENT,
  TUTORIAL_VIDEO_COMPONENT,
  HERO_SECTION_COMPONENT,
  LIVE_AGENT_COMPONENT,
  BLOB_BACKGROUND_WRAPPER_COMPONENT,
  TEXT_WITH_MODAL_BUTTON_COMPONENT,
  RAY_BACKGROUND_WRAPPER_COMPONENT,
  EXTERNAL_SUPPORT_SEARCH_BAR_COMPONENT,
];

export const CUSTOM_CONTEXT = {};
