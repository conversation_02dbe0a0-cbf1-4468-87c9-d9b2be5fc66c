# Don't change the line below!
#! oxygen_storefront_id: 1000010548

name: Development
on:
  push:
    branches:
      - main
permissions:
  contents: read
  deployments: write
jobs:
  deploy:
    name: Deploy to Oxygen
    timeout-minutes: 30
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
    - uses: actions/checkout@v4
    - name: Setup node.js
      uses: useblacksmith/setup-node@v5
      with:
        node-version: lts/*
        check-latest: true
    - name: Cache node modules
      id: cache-npm
      uses: useblacksmith/cache@v5
      env:
        cache-name: cache-node-modules
      with:
        path: "~/.npm"
        key: "${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}"
        restore-keys: |
          ${{ runner.os }}-build-${{ env.cache-name }}-
          ${{ runner.os }}-build-
          ${{ runner.os }}-
    - name: Install dependencies
      run: npm ci
    - name: Build and Publish to Oxygen
      id: deploy
      run: npx shopify hydrogen deploy
      env:
        SHOPIFY_HYDROGEN_DEPLOYMENT_TOKEN: "${{ secrets.OXYGEN_DEPLOYMENT_TOKEN_1000010548 }}"
