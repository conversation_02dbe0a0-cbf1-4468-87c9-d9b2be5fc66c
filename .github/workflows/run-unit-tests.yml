name: PR Check

on:
  pull_request:
    branches: [main]

jobs:
  unit_tests:
    runs-on: blacksmith-4vcpu-ubuntu-2404

    steps:
      - name: ⬇️ Checkout code
        uses: actions/checkout@v3

      - name: ⎔ Set up Node
        uses: useblacksmith/setup-node@v5
        with:
          node-version: 18
          cache: "npm"

      - name: 📥 Install dependencies
        run: npm ci

      - name: ✅ Run unit tests
        run: npm run test
