# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "b1c0cab21133bb53b96d17d991e988fe"
name = "storefront-dev"
handle = "storefront-dev-3"
application_url = "https://aa-storefront-dev-5651cba2c30b68d7a0f1.o2.myshopify.dev"
embedded = true

[build]
automatically_update_urls_on_dev = false
dev_store_url = "allaware-sandbox.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_discounts,write_customers,write_companies,write_products,write_orders"

[auth]
redirect_urls = [
  "https://aa-storefront-dev-5651cba2c30b68d7a0f1.o2.myshopify.dev/auth/callback"
]

[webhooks]
api_version = "2024-10"

[pos]
embedded = false
