diff --git a/node_modules/@builder.io/sdk-react/lib/edge/image-209835f4.js b/node_modules/@builder.io/sdk-react/lib/edge/image-209835f4.js
index 625e939..3331d36 100644
--- a/node_modules/@builder.io/sdk-react/lib/edge/image-209835f4.js
+++ b/node_modules/@builder.io/sdk-react/lib/edge/image-209835f4.js
@@ -59,7 +59,7 @@ function w(e) {
           "img",
           {
             loading: e.highPriority ? "eager" : "lazy",
-            fetchPriority: e.highPriority ? "high" : "auto",
+            // fetchPriority: e.highPriority ? "high" : "auto",
             alt: e.altText,
             title: e.title,
             role: e.altText ? void 0 : "presentation",
diff --git a/node_modules/@builder.io/sdk-react/lib/edge/image-87a4e006.cjs b/node_modules/@builder.io/sdk-react/lib/edge/image-87a4e006.cjs
index fb0b6f4..6d26210 100644
--- a/node_modules/@builder.io/sdk-react/lib/edge/image-87a4e006.cjs
+++ b/node_modules/@builder.io/sdk-react/lib/edge/image-87a4e006.cjs
@@ -14,4 +14,4 @@
   left: 0;
   width: 100%;
   height: 100%;
-}`}),t.jsxs(t.Fragment,{children:[t.jsxs("picture",{children:[a()?t.jsx("source",{type:"image/webp",srcSet:a()}):null,t.jsx("img",{loading:e.highPriority?"eager":"lazy",fetchPriority:e.highPriority?"high":"auto",alt:e.altText,title:e.title,role:e.altText?void 0:"presentation",style:{objectPosition:e.backgroundPosition||"center",objectFit:e.backgroundSize||"cover",...m()},className:"builder-image"+(e.className?" "+e.className:"")+" img-3cc22d08",src:e.image,srcSet:c(),sizes:e.sizes})]}),e.aspectRatio&&!((r=(s=e.builderBlock)==null?void 0:s.children)!=null&&r.length&&e.fitContent)?t.jsx("div",{className:"builder-image-sizer div-3cc22d08",style:{paddingTop:e.aspectRatio*100+"%"}}):null,(u=(d=e.builderBlock)==null?void 0:d.children)!=null&&u.length&&e.fitContent?t.jsx(t.Fragment,{children:e.children}):null,!e.fitContent&&((g=(o=e.builderBlock)==null?void 0:o.children)!=null&&g.length)?t.jsx("div",{className:"div-3cc22d08-2",children:e.children}):null]})]})}exports.default=b;
+}`}),t.jsxs(t.Fragment,{children:[t.jsxs("picture",{children:[a()?t.jsx("source",{type:"image/webp",srcSet:a()}):null,t.jsx("img",{loading:e.highPriority?"eager":"lazy",/*fetchPriority:e.highPriority?"high":"auto",*/alt:e.altText,title:e.title,role:e.altText?void 0:"presentation",style:{objectPosition:e.backgroundPosition||"center",objectFit:e.backgroundSize||"cover",...m()},className:"builder-image"+(e.className?" "+e.className:"")+" img-3cc22d08",src:e.image,srcSet:c(),sizes:e.sizes})]}),e.aspectRatio&&!((r=(s=e.builderBlock)==null?void 0:s.children)!=null&&r.length&&e.fitContent)?t.jsx("div",{className:"builder-image-sizer div-3cc22d08",style:{paddingTop:e.aspectRatio*100+"%"}}):null,(u=(d=e.builderBlock)==null?void 0:d.children)!=null&&u.length&&e.fitContent?t.jsx(t.Fragment,{children:e.children}):null,!e.fitContent&&((g=(o=e.builderBlock)==null?void 0:o.children)!=null&&g.length)?t.jsx("div",{className:"div-3cc22d08-2",children:e.children}):null]})]})}exports.default=b;
diff --git a/node_modules/@builder.io/sdk-react/lib/edge/image.helpers-3e926d47.js b/node_modules/@builder.io/sdk-react/lib/edge/image.helpers-3e926d47.js
index 69a52e2..308b84e 100644
--- a/node_modules/@builder.io/sdk-react/lib/edge/image.helpers-3e926d47.js
+++ b/node_modules/@builder.io/sdk-react/lib/edge/image.helpers-3e926d47.js
@@ -21,12 +21,12 @@ function r(t) {
   if (!t)
     return t;
   const e = [100, 200, 400, 800, 1200, 1600, 2e3];
-  if (t.match(/builder\.io/)) {
+  // if (t.match(/builder\.io/)) {
     let n = t;
     const o = Number(t.split("?width=")[1]);
     return isNaN(o) || (n = `${n} ${o}w`), e.filter((i) => i !== o).map((i) => `${p(t, "width", i)} ${i}w`).concat([n]).join(", ");
-  }
-  return t.match(/cdn\.shopify\.com/) ? e.map((n) => [a(t, `${n}x${n}`), n]).filter(([n]) => !!n).map(([n, o]) => `${n} ${o}w`).concat([t]).join(", ") : t;
+  // }
+  // return t.match(/cdn\.shopify\.com/) ? e.map((n) => [a(t, `${n}x${n}`), n]).filter(([n]) => !!n).map(([n, o]) => `${n} ${o}w`).concat([t]).join(", ") : t;
 }
 export {
   r as getSrcSet
diff --git a/node_modules/@builder.io/sdk-react/lib/edge/image.helpers-dba8a869.cjs b/node_modules/@builder.io/sdk-react/lib/edge/image.helpers-dba8a869.cjs
index 8928cf9..3798e3a 100644
--- a/node_modules/@builder.io/sdk-react/lib/edge/image.helpers-dba8a869.cjs
+++ b/node_modules/@builder.io/sdk-react/lib/edge/image.helpers-dba8a869.cjs
@@ -1 +1 @@
-"use strict";function c(t){return t.replace(/http(s)?:/,"")}function r(t="",e,n){const o=new RegExp("([?&])"+e+"=.*?(&|$)","i"),i=t.indexOf("?")!==-1?"&":"?";return t.match(o)?t.replace(o,"$1"+e+"="+encodeURIComponent(n)+"$2"):t+i+e+"="+encodeURIComponent(n)}function a(t,e){if(!t||!(t!=null&&t.match(/cdn\.shopify\.com/))||!e)return t;if(e==="master")return c(t);const n=t.match(/(_\d+x(\d+)?)?(\.(jpg|jpeg|gif|png|bmp|bitmap|tiff|tif)(\?v=\d+)?)/i);if(n){const o=t.split(n[0]),i=n[3],f=e.match("x")?e:`${e}x`;return c(`${o[0]}_${f}${i}`)}return null}function p(t){if(!t)return t;const e=[100,200,400,800,1200,1600,2e3];if(t.match(/builder\.io/)){let n=t;const o=Number(t.split("?width=")[1]);return isNaN(o)||(n=`${n} ${o}w`),e.filter(i=>i!==o).map(i=>`${r(t,"width",i)} ${i}w`).concat([n]).join(", ")}return t.match(/cdn\.shopify\.com/)?e.map(n=>[a(t,`${n}x${n}`),n]).filter(([n])=>!!n).map(([n,o])=>`${n} ${o}w`).concat([t]).join(", "):t}exports.getSrcSet=p;
+"use strict";function c(t){return t.replace(/http(s)?:/,"")}function r(t="",e,n){const o=new RegExp("([?&])"+e+"=.*?(&|$)","i"),i=t.indexOf("?")!==-1?"&":"?";return t.match(o)?t.replace(o,"$1"+e+"="+encodeURIComponent(n)+"$2"):t+i+e+"="+encodeURIComponent(n)}function a(t,e){if(!t||!(t!=null&&t.match(/cdn\.shopify\.com/))||!e)return t;if(e==="master")return c(t);const n=t.match(/(_\d+x(\d+)?)?(\.(jpg|jpeg|gif|png|bmp|bitmap|tiff|tif)(\?v=\d+)?)/i);if(n){const o=t.split(n[0]),i=n[3],f=e.match("x")?e:`${e}x`;return c(`${o[0]}_${f}${i}`)}return null}function p(t){if(!t)return t;const e=[100,200,400,800,1200,1600,2e3];/*if(t.match(/builder\.io/)){*/let n=t;const o=Number(t.split("?width=")[1]);return isNaN(o)||(n=`${n} ${o}w`),e.filter(i=>i!==o).map(i=>`${r(t,"width",i)} ${i}w`).concat([n]).join(", ")/*}return t.match(/cdn\.shopify\.com/)?e.map(n=>[a(t,`${n}x${n}`),n]).filter(([n])=>!!n).map(([n,o])=>`${n} ${o}w`).concat([t]).join(", "):t*/}exports.getSrcSet=p;
diff --git a/node_modules/@builder.io/sdk-react/lib/node/blocks-exports.mjs b/node_modules/@builder.io/sdk-react/lib/node/blocks-exports.mjs
index 88160df..f589870 100644
--- a/node_modules/@builder.io/sdk-react/lib/node/blocks-exports.mjs
+++ b/node_modules/@builder.io/sdk-react/lib/node/blocks-exports.mjs
@@ -1260,12 +1260,12 @@ function ne(e) {
   if (!e)
     return e;
   const t = [100, 200, 400, 800, 1200, 1600, 2e3];
-  if (e.match(/builder\.io/)) {
+  // if (e.match(/builder\.io/)) {
     let n = e;
     const i = Number(e.split("?width=")[1]);
     return isNaN(i) || (n = `${n} ${i}w`), t.filter((r) => r !== i).map((r) => `${dn(e, "width", r)} ${r}w`).concat([n]).join(", ");
-  }
-  return e.match(/cdn\.shopify\.com/) ? t.map((n) => [mn(e, `${n}x${n}`), n]).filter(([n]) => !!n).map(([n, i]) => `${n} ${i}w`).concat([e]).join(", ") : e;
+  // }
+  // return e.match(/cdn\.shopify\.com/) ? t.map((n) => [mn(e, `${n}x${n}`), n]).filter(([n]) => !!n).map(([n, i]) => `${n} ${i}w`).concat([e]).join(", ") : e;
 }
 function fn(e) {
   var r, o, a, l, c, u;
@@ -1325,7 +1325,7 @@ function fn(e) {
           "img",
           {
             loading: e.highPriority ? "eager" : "lazy",
-            fetchPriority: e.highPriority ? "high" : "auto",
+            // fetchPriority: e.highPriority ? "high" : "auto",
             alt: e.altText,
             title: e.title,
             role: e.altText ? void 0 : "presentation",
