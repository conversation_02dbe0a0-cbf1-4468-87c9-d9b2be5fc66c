const fs = require('node:fs');

const INJECT = ['SHOPIFY_API_KEY', 'SHOPIFY_API_SECRET', 'HOST', 'SCOPES', 'NODE_ENV', 'PORT'];

const ENV_PATH = '.env';
console.log('App env variables injected:');
try {
  var data = fs.readFileSync(ENV_PATH, { encoding: 'utf-8', flag: 'r' });
} catch {
  var data = '';
}

for (const key of INJECT) {
  const value = process.env[key];

  const re = new RegExp(`^${key}=.*$`, 'm');

  if (data.search(re) >= 0) {
    console.log(`UPDATED ${key}`);
    data = data.replace(re, value ? `${key}=${value}` : '');
  } else if (value) {
    console.log(`ADDED ${key}`);
    data += `\n${key}=${value}`;
  }
}

fs.writeFileSync(ENV_PATH, data, { encoding: 'utf-8', flag: 'w' });
