const fs = require('fs').promises;
const path = require('path');
const util = require('util');
const exec = util.promisify(require('child_process').exec);

const config = process.argv[2];
const option = process.argv[3];

const PLACEHOLDER = '{url}';

async function updateRuntimeUrls(url = PLACEHOLDER) {
  const folderPath = path.join(__dirname, '../extensions');
  const files = await fs.readdir(folderPath, { withFileTypes: true });
  const folders = files.filter(file => file.isDirectory());

  // Update the URL in the toml file
  for (const folder of folders) {
    const tomlFilePath = path.join(folderPath, folder.name, 'shopify.extension.toml');

    let data;
    try {
      data = await fs.readFile(tomlFilePath, 'utf8');
    } catch (error) {
      continue;
    }

    const updatedData = data.replace(/runtime_url = "https:\/\/([^\/]*)\//g, `runtime_url = "https://${url}/`);

    if (data != updatedData) {
      await fs.writeFile(tomlFilePath, updatedData, 'utf8');
      if (url == PLACEHOLDER) {
        console.log(`Reset ${folder.name}`);
      } else {
        console.log(`Updated ${folder.name} runtime_url to ${url}`);
      }
    }
  }
}

(async function () {
  const configFileName = config ? `shopify.app.${config}.toml` : 'shopify.app.toml';
  console.log('configFileName', configFileName);
  const configFilePath = path.join(__dirname, `../${configFileName}`);

  let configFile = '';
  try {
    configFile = (await fs.readFile(configFilePath, 'utf8')) || '';
  } catch (error) {
    console.error(error.message);
  }

  const match = Array.from(/^\s*application_url\s*=\s*"https:\/\/(.*)"\s*$/gm.exec(configFile));
  const url = match?.[1];

  if (!url) {
    console.log(`Could not find url in config "${config}". Does ${configFileName} file exists?`);
    process.exit(1);
  }

  console.log(`Found config url: ${url}`);

  await updateRuntimeUrls(url);

  console.log(`Deploying ${configFileName}...`);

  // Deploy the extensions
  const { error, stdout, stderr } = await exec(`shopify app deploy --force --config=${configFileName}`);

  if (error) {
    console.log(`error: ${error.message}`);
  }

  if (stderr) {
    console.log(`stderr: ${stderr}`);
  }

  console.log(`stdout: ${stdout}`);

  if (option == '--no-reset') {
    return;
  }
  await updateRuntimeUrls();
})();
