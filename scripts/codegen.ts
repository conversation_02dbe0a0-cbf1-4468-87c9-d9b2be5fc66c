import config from '../.graphqlrc';
import { spawn } from 'child_process';

const projectNames = Object.keys((config as any).projects);

const processes = projectNames.map(projectName => {
  const ls = spawn(`npx`, ['graphql-codegen', '--watch', '--project', projectName]);

  ls.stdout.on('data', data => {
    console.log(`${data}`);
  });

  ls.stderr.on('data', data => {
    console.error(`${data}`);
  });

  ls.on('close', code => {
    console.log(`child process exited with code ${code}`);
  });
  return ls;
});

process.on('SIGINT', data => {
  processes.forEach(p => p.kill('SIGINT'));

  console.log('Ctrl-C was pressed');
  process.exit();
});
