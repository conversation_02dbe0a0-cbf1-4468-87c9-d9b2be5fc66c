/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
import * as StorefrontTypes from './storefront.types';

export type MoneyFragment = Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>;

export type CartProductFragment = (
  Pick<StorefrontTypes.Product, 'handle' | 'title' | 'id' | 'vendor'>
  & { deviceHandle?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>>, advertisedDiscountSearchTerm?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>> }
);

export type CartLineFragment = (
  Pick<StorefrontTypes.CartLine, 'id' | 'quantity'>
  & { attributes: Array<Pick<StorefrontTypes.Attribute, 'key' | 'value'>>, cost: { totalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, subtotalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, amountPerQuantity: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, compareAtAmountPerQuantity?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>> }, merchandise: (
    Pick<StorefrontTypes.ProductVariant, 'id' | 'availableForSale' | 'requiresShipping' | 'title'>
    & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>>, price: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, image?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>>, product: (
      Pick<StorefrontTypes.Product, 'handle' | 'title' | 'id' | 'vendor'>
      & { deviceHandle?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>>, advertisedDiscountSearchTerm?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>> }
    ), selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>> }
  ) }
);

export type CartLineComponentFragment = (
  Pick<StorefrontTypes.ComponentizableCartLine, 'id' | 'quantity'>
  & { attributes: Array<Pick<StorefrontTypes.Attribute, 'key' | 'value'>>, cost: { totalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, subtotalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, amountPerQuantity: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, compareAtAmountPerQuantity?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>> }, merchandise: (
    Pick<StorefrontTypes.ProductVariant, 'id' | 'availableForSale' | 'requiresShipping' | 'title'>
    & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>>, price: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, image?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>>, product: (
      Pick<StorefrontTypes.Product, 'handle' | 'title' | 'id' | 'vendor'>
      & { deviceHandle?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>>, advertisedDiscountSearchTerm?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>> }
    ), selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>> }
  ) }
);

export type CartApiQueryFragment = (
  Pick<StorefrontTypes.Cart, 'updatedAt' | 'id' | 'checkoutUrl' | 'totalQuantity' | 'note'>
  & { appliedGiftCards: Array<(
    Pick<StorefrontTypes.AppliedGiftCard, 'lastCharacters'>
    & { amountUsed: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'> }
  )>, buyerIdentity: (
    Pick<StorefrontTypes.CartBuyerIdentity, 'countryCode' | 'email' | 'phone'>
    & { customer?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Customer, 'id' | 'email' | 'firstName' | 'lastName' | 'displayName'>>, purchasingCompany?: StorefrontTypes.Maybe<{ company: Pick<StorefrontTypes.Company, 'id'>, location: Pick<StorefrontTypes.CompanyLocation, 'id'> }> }
  ), lines: { nodes: Array<(
      Pick<StorefrontTypes.CartLine, 'id' | 'quantity'>
      & { attributes: Array<Pick<StorefrontTypes.Attribute, 'key' | 'value'>>, cost: { totalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, subtotalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, amountPerQuantity: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, compareAtAmountPerQuantity?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>> }, merchandise: (
        Pick<StorefrontTypes.ProductVariant, 'id' | 'availableForSale' | 'requiresShipping' | 'title'>
        & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>>, price: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, image?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>>, product: (
          Pick<StorefrontTypes.Product, 'handle' | 'title' | 'id' | 'vendor'>
          & { deviceHandle?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>>, advertisedDiscountSearchTerm?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>> }
        ), selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>> }
      ) }
    ) | (
      Pick<StorefrontTypes.ComponentizableCartLine, 'id' | 'quantity'>
      & { attributes: Array<Pick<StorefrontTypes.Attribute, 'key' | 'value'>>, cost: { totalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, subtotalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, amountPerQuantity: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, compareAtAmountPerQuantity?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>> }, merchandise: (
        Pick<StorefrontTypes.ProductVariant, 'id' | 'availableForSale' | 'requiresShipping' | 'title'>
        & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>>, price: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, image?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>>, product: (
          Pick<StorefrontTypes.Product, 'handle' | 'title' | 'id' | 'vendor'>
          & { deviceHandle?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>>, advertisedDiscountSearchTerm?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>> }
        ), selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>> }
      ) }
    )> }, cost: { subtotalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, totalAmount: Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>, totalDutyAmount?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>>, totalTaxAmount?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'currencyCode' | 'amount'>> }, attributes: Array<Pick<StorefrontTypes.Attribute, 'key' | 'value'>>, discountCodes: Array<Pick<StorefrontTypes.CartDiscountCode, 'code' | 'applicable'>> }
);

export type ArticleItemFragment = (
  Pick<StorefrontTypes.Article, 'contentHtml' | 'handle' | 'id' | 'excerptHtml' | 'publishedAt' | 'title'>
  & { author?: StorefrontTypes.Maybe<Pick<StorefrontTypes.ArticleAuthor, 'name'>>, image?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Image, 'id' | 'altText' | 'url' | 'width' | 'height'>>, blog: Pick<StorefrontTypes.Blog, 'handle'> }
);

export type ImageFragment = Pick<StorefrontTypes.Image, 'altText' | 'url' | 'width' | 'height' | 'id'>;

export type CustomerFragment = (
  Pick<StorefrontTypes.Customer, 'acceptsMarketing' | 'email' | 'firstName' | 'lastName' | 'numberOfOrders' | 'phone'>
  & { addresses: { nodes: Array<Pick<StorefrontTypes.MailingAddress, 'id' | 'formatted' | 'firstName' | 'lastName' | 'company' | 'address1' | 'address2' | 'country' | 'province' | 'city' | 'zip' | 'phone'>> }, defaultAddress?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MailingAddress, 'id' | 'formatted' | 'firstName' | 'lastName' | 'company' | 'address1' | 'address2' | 'country' | 'province' | 'city' | 'zip' | 'phone'>> }
);

export type AddressFragment = Pick<StorefrontTypes.MailingAddress, 'id' | 'formatted' | 'firstName' | 'lastName' | 'company' | 'address1' | 'address2' | 'country' | 'province' | 'city' | 'zip' | 'phone'>;

export type GetCustomerEmailQueryVariables = StorefrontTypes.Exact<{
  customerAccessToken: StorefrontTypes.Scalars['String']['input'];
  country?: StorefrontTypes.InputMaybe<StorefrontTypes.CountryCode>;
  language?: StorefrontTypes.InputMaybe<StorefrontTypes.LanguageCode>;
}>;


export type GetCustomerEmailQuery = { customer?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Customer, 'email'>> };

export type GetCustomerIdQueryVariables = StorefrontTypes.Exact<{
  customerAccessToken: StorefrontTypes.Scalars['String']['input'];
  country?: StorefrontTypes.InputMaybe<StorefrontTypes.CountryCode>;
  language?: StorefrontTypes.InputMaybe<StorefrontTypes.LanguageCode>;
}>;


export type GetCustomerIdQuery = { customer?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Customer, 'id'>> };

export type ProductVariantFragment = (
  Pick<StorefrontTypes.ProductVariant, 'availableForSale' | 'id' | 'sku' | 'title'>
  & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>>, image?: StorefrontTypes.Maybe<(
    { __typename: 'Image' }
    & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
  )>, price: Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>, product: Pick<StorefrontTypes.Product, 'title' | 'handle'>, selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>>, unitPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>> }
);

export type ProductFragment = (
  Pick<StorefrontTypes.Product, 'id' | 'title' | 'vendor' | 'handle' | 'descriptionHtml' | 'description' | 'encodedVariantExistence' | 'encodedVariantAvailability'>
  & { images: { nodes: Array<(
      { __typename: 'Image' }
      & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
    )> }, options: Array<(
    Pick<StorefrontTypes.ProductOption, 'name'>
    & { optionValues: Array<(
      Pick<StorefrontTypes.ProductOptionValue, 'name'>
      & { firstSelectableVariant?: StorefrontTypes.Maybe<(
        Pick<StorefrontTypes.ProductVariant, 'availableForSale' | 'id' | 'sku' | 'title'>
        & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>>, image?: StorefrontTypes.Maybe<(
          { __typename: 'Image' }
          & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
        )>, price: Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>, product: Pick<StorefrontTypes.Product, 'title' | 'handle'>, selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>>, unitPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>> }
      )>, swatch?: StorefrontTypes.Maybe<(
        Pick<StorefrontTypes.ProductOptionValueSwatch, 'color'>
        & { image?: StorefrontTypes.Maybe<{ previewImage?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Image, 'url'>> }> }
      )> }
    )> }
  )>, selectedOrFirstAvailableVariant?: StorefrontTypes.Maybe<(
    Pick<StorefrontTypes.ProductVariant, 'availableForSale' | 'id' | 'sku' | 'title'>
    & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>>, image?: StorefrontTypes.Maybe<(
      { __typename: 'Image' }
      & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
    )>, price: Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>, product: Pick<StorefrontTypes.Product, 'title' | 'handle'>, selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>>, unitPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>> }
  )>, adjacentVariants: Array<(
    Pick<StorefrontTypes.ProductVariant, 'availableForSale' | 'id' | 'sku' | 'title'>
    & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>>, image?: StorefrontTypes.Maybe<(
      { __typename: 'Image' }
      & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
    )>, price: Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>, product: Pick<StorefrontTypes.Product, 'title' | 'handle'>, selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>>, unitPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>> }
  )>, seo: Pick<StorefrontTypes.SEO, 'description' | 'title'>, deviceHandle?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>>, advertisedDiscountSearchTerm?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>> }
);

export type ProductQueryVariables = StorefrontTypes.Exact<{
  country?: StorefrontTypes.InputMaybe<StorefrontTypes.CountryCode>;
  handle: StorefrontTypes.Scalars['String']['input'];
  language?: StorefrontTypes.InputMaybe<StorefrontTypes.LanguageCode>;
  selectedOptions: Array<StorefrontTypes.SelectedOptionInput> | StorefrontTypes.SelectedOptionInput;
}>;


export type ProductQuery = { product?: StorefrontTypes.Maybe<(
    Pick<StorefrontTypes.Product, 'id' | 'title' | 'vendor' | 'handle' | 'descriptionHtml' | 'description' | 'encodedVariantExistence' | 'encodedVariantAvailability'>
    & { images: { nodes: Array<(
        { __typename: 'Image' }
        & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
      )> }, options: Array<(
      Pick<StorefrontTypes.ProductOption, 'name'>
      & { optionValues: Array<(
        Pick<StorefrontTypes.ProductOptionValue, 'name'>
        & { firstSelectableVariant?: StorefrontTypes.Maybe<(
          Pick<StorefrontTypes.ProductVariant, 'availableForSale' | 'id' | 'sku' | 'title'>
          & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>>, image?: StorefrontTypes.Maybe<(
            { __typename: 'Image' }
            & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
          )>, price: Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>, product: Pick<StorefrontTypes.Product, 'title' | 'handle'>, selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>>, unitPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>> }
        )>, swatch?: StorefrontTypes.Maybe<(
          Pick<StorefrontTypes.ProductOptionValueSwatch, 'color'>
          & { image?: StorefrontTypes.Maybe<{ previewImage?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Image, 'url'>> }> }
        )> }
      )> }
    )>, selectedOrFirstAvailableVariant?: StorefrontTypes.Maybe<(
      Pick<StorefrontTypes.ProductVariant, 'availableForSale' | 'id' | 'sku' | 'title'>
      & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>>, image?: StorefrontTypes.Maybe<(
        { __typename: 'Image' }
        & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
      )>, price: Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>, product: Pick<StorefrontTypes.Product, 'title' | 'handle'>, selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>>, unitPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>> }
    )>, adjacentVariants: Array<(
      Pick<StorefrontTypes.ProductVariant, 'availableForSale' | 'id' | 'sku' | 'title'>
      & { compareAtPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>>, image?: StorefrontTypes.Maybe<(
        { __typename: 'Image' }
        & Pick<StorefrontTypes.Image, 'id' | 'url' | 'altText' | 'width' | 'height'>
      )>, price: Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>, product: Pick<StorefrontTypes.Product, 'title' | 'handle'>, selectedOptions: Array<Pick<StorefrontTypes.SelectedOption, 'name' | 'value'>>, unitPrice?: StorefrontTypes.Maybe<Pick<StorefrontTypes.MoneyV2, 'amount' | 'currencyCode'>> }
    )>, seo: Pick<StorefrontTypes.SEO, 'description' | 'title'>, deviceHandle?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>>, advertisedDiscountSearchTerm?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Metafield, 'value'>> }
  )> };

export type StoreRobotsQueryVariables = StorefrontTypes.Exact<{
  country?: StorefrontTypes.InputMaybe<StorefrontTypes.CountryCode>;
  language?: StorefrontTypes.InputMaybe<StorefrontTypes.LanguageCode>;
}>;


export type StoreRobotsQuery = { shop: Pick<StorefrontTypes.Shop, 'id'> };

export type SitemapQueryVariables = StorefrontTypes.Exact<{
  urlLimits?: StorefrontTypes.InputMaybe<StorefrontTypes.Scalars['Int']['input']>;
  language?: StorefrontTypes.InputMaybe<StorefrontTypes.LanguageCode>;
}>;


export type SitemapQuery = { products: { nodes: Array<(
      Pick<StorefrontTypes.Product, 'updatedAt' | 'handle' | 'onlineStoreUrl' | 'title'>
      & { featuredImage?: StorefrontTypes.Maybe<Pick<StorefrontTypes.Image, 'url' | 'altText'>> }
    )> }, collections: { nodes: Array<Pick<StorefrontTypes.Collection, 'updatedAt' | 'handle' | 'onlineStoreUrl'>> }, pages: { nodes: Array<Pick<StorefrontTypes.Page, 'updatedAt' | 'handle' | 'onlineStoreUrl'>> } };

export type PolicyFragment = Pick<StorefrontTypes.ShopPolicy, 'body' | 'handle' | 'id' | 'title' | 'url'>;

export type PolicyQueryVariables = StorefrontTypes.Exact<{
  country?: StorefrontTypes.InputMaybe<StorefrontTypes.CountryCode>;
  language?: StorefrontTypes.InputMaybe<StorefrontTypes.LanguageCode>;
  privacyPolicy: StorefrontTypes.Scalars['Boolean']['input'];
  refundPolicy: StorefrontTypes.Scalars['Boolean']['input'];
  shippingPolicy: StorefrontTypes.Scalars['Boolean']['input'];
  termsOfService: StorefrontTypes.Scalars['Boolean']['input'];
}>;


export type PolicyQuery = { shop: { privacyPolicy?: StorefrontTypes.Maybe<Pick<StorefrontTypes.ShopPolicy, 'body' | 'handle' | 'id' | 'title' | 'url'>>, shippingPolicy?: StorefrontTypes.Maybe<Pick<StorefrontTypes.ShopPolicy, 'body' | 'handle' | 'id' | 'title' | 'url'>>, termsOfService?: StorefrontTypes.Maybe<Pick<StorefrontTypes.ShopPolicy, 'body' | 'handle' | 'id' | 'title' | 'url'>>, refundPolicy?: StorefrontTypes.Maybe<Pick<StorefrontTypes.ShopPolicy, 'body' | 'handle' | 'id' | 'title' | 'url'>> } };

interface GeneratedQueryTypes {
  "#graphql\n                query GetCustomerEmail(\n                  $customerAccessToken: String!\n                  $country: CountryCode\n                  $language: LanguageCode\n                ) @inContext(country: $country, language: $language) {\n                  customer(customerAccessToken: $customerAccessToken) {\n                    email\n                  }\n                }\n              ": {return: GetCustomerEmailQuery, variables: GetCustomerEmailQueryVariables},
  "#graphql\n                query GetCustomerId(\n                  $customerAccessToken: String!\n                  $country: CountryCode\n                  $language: LanguageCode\n                ) @inContext(country: $country, language: $language) {\n                  customer(customerAccessToken: $customerAccessToken) {\n                    id\n                  }\n                }\n              ": {return: GetCustomerIdQuery, variables: GetCustomerIdQueryVariables},
  "#graphql\n  query Product(\n    $country: CountryCode\n    $handle: String!\n    $language: LanguageCode\n    $selectedOptions: [SelectedOptionInput!]!\n  ) @inContext(country: $country, language: $language) {\n    product(handle: $handle) {\n      ...Product\n    }\n  }\n  #graphql\n  fragment Product on Product {\n    id\n    title\n    vendor\n    handle\n    descriptionHtml\n    description\n    encodedVariantExistence\n    encodedVariantAvailability\n    images (first: 50) {\n      nodes {\n        __typename\n        id\n        url\n        altText\n        width\n        height\n      }\n    }\n    options {\n      name\n      optionValues {\n        name\n        firstSelectableVariant {\n          ...ProductVariant\n        }\n        swatch {\n          color\n          image {\n            previewImage {\n              url\n            }\n          }\n        }\n      }\n    }\n    selectedOrFirstAvailableVariant(selectedOptions: $selectedOptions, ignoreUnknownOptions: true, caseInsensitiveMatch: true) {\n      ...ProductVariant\n    }\n    adjacentVariants (selectedOptions: $selectedOptions) {\n      ...ProductVariant\n    }\n    seo {\n      description\n      title\n    }\n    deviceHandle: metafield(namespace: \"device\", key: \"handle\") {\n      value\n    }\n    advertisedDiscountSearchTerm: metafield(namespace: \"marketing\", key: \"advertised-discount-search-term\") {\n      value\n    }\n  }\n  #graphql\n  fragment ProductVariant on ProductVariant {\n    availableForSale\n    compareAtPrice {\n      amount\n      currencyCode\n    }\n    id\n    image {\n      __typename\n      id\n      url\n      altText\n      width\n      height\n    }\n    price {\n      amount\n      currencyCode\n    }\n    product {\n      title\n      handle\n    }\n    selectedOptions {\n      name\n      value\n    }\n    sku\n    title\n    unitPrice {\n      amount\n      currencyCode\n    }\n  }\n\n\n": {return: ProductQuery, variables: ProductQueryVariables},
  "#graphql\n  query StoreRobots($country: CountryCode, $language: LanguageCode)\n   @inContext(country: $country, language: $language) {\n    shop {\n      id\n    }\n  }\n": {return: StoreRobotsQuery, variables: StoreRobotsQueryVariables},
  "#graphql\n  query Sitemap($urlLimits: Int, $language: LanguageCode)\n  @inContext(language: $language) {\n    products(\n      first: $urlLimits\n      query: \"published_status:'online_store:visible'\"\n    ) {\n      nodes {\n        updatedAt\n        handle\n        onlineStoreUrl\n        title\n        featuredImage {\n          url\n          altText\n        }\n      }\n    }\n    collections(\n      first: $urlLimits\n      query: \"published_status:'online_store:visible'\"\n    ) {\n      nodes {\n        updatedAt\n        handle\n        onlineStoreUrl\n      }\n    }\n    pages(first: $urlLimits, query: \"published_status:'published'\") {\n      nodes {\n        updatedAt\n        handle\n        onlineStoreUrl\n      }\n    }\n  }\n": {return: SitemapQuery, variables: SitemapQueryVariables},
  "#graphql\n    fragment Policy on ShopPolicy {\n      body\n      handle\n      id\n      title\n      url\n    }\n    query Policy(\n      $country: CountryCode\n      $language: LanguageCode\n      $privacyPolicy: Boolean!\n      $refundPolicy: Boolean!\n      $shippingPolicy: Boolean!\n      $termsOfService: Boolean!\n    ) @inContext(language: $language, country: $country) {\n      shop {\n        privacyPolicy @include(if: $privacyPolicy) {\n          ...Policy\n        }\n        shippingPolicy @include(if: $shippingPolicy) {\n          ...Policy\n        }\n        termsOfService @include(if: $termsOfService) {\n          ...Policy\n        }\n        refundPolicy @include(if: $refundPolicy) {\n          ...Policy\n        }\n      }\n    }\n  ": {return: PolicyQuery, variables: PolicyQueryVariables},
}

interface GeneratedMutationTypes {
}
declare module '@shopify/hydrogen' {
  type InputMaybe<T> = StorefrontTypes.InputMaybe<T>;
  interface StorefrontQueries extends GeneratedQueryTypes {}
  interface StorefrontMutations extends GeneratedMutationTypes {}
}
